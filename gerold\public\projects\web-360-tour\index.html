<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Unity WebGL Player | 360</title>
    <style>
      body {
        background-color: #0a1929;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        overflow-x: hidden;
        position: relative;
      }
      
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('background.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(3px) brightness(0.7);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.5s ease;
      }
      
      @media (min-width: 769px) and (hover: hover) {
        body::before {
          opacity: 1;
        }
      }
      
      #unity-container {
        position: relative;
        background: rgba(35, 31, 32, 0.95);
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: visible;
        margin: 10px;
        width: 1020px;
        max-width: 95vw;
        height: auto;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      /* Стили для полноэкранного режима */
      #unity-container:fullscreen,
      #unity-container:-webkit-full-screen {
        width: 100vw;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
      }
      
      #unity-canvas {
        background: #231F20;
        width: 100%;
        height: 100%;
        display: block;
      }
      
      #loading {
        color: white;
        font-size: 18px;
        letter-spacing: 2px;
        margin-top: 20px;
        text-align: center;
      }
      
      .loader {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 20px;
      }
      
      .loader div {
        position: absolute;
        top: 8px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: #fff;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
      }
      
      .loader div:nth-child(1) {
        left: 8px;
        animation: loader1 0.6s infinite;
      }
      
      .loader div:nth-child(2) {
        left: 8px;
        animation: loader2 0.6s infinite;
      }
      
      .loader div:nth-child(3) {
        left: 32px;
        animation: loader2 0.6s infinite;
      }
      
      .loader div:nth-child(4) {
        left: 56px;
        animation: loader3 0.6s infinite;
      }
      
      @keyframes loader1 {
        0% { transform: scale(0); }
        100% { transform: scale(1); }
      }
      
      @keyframes loader2 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(24px, 0); }
      }
      
      @keyframes loader3 {
        0% { transform: scale(1); }
        100% { transform: scale(0); }
      }
      
      /* Медиа-запросы для адаптивности */
      @media (max-width: 1024px) {
        #unity-container {
          width: 90vw;
          max-width: 800px;
        }
      }
      
      @media (max-width: 768px) {
        #unity-container {
          width: 95vw;
        }
        
        #loading {
          font-size: 16px;
        }
      }
      
      @media (max-width: 480px) {
        body {
          padding: 10px 0;
        }
        
        #unity-container {
          border-radius: 4px;
          margin: 5px;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }
        
        #loading {
          font-size: 14px;
          margin-top: 15px;
        }
        
        .loader {
          width: 60px;
        }
        
        .loader div {
          width: 10px;
          height: 10px;
        }
      }
      
      /* Стили для мобильных устройств в полноэкранном режиме */
      .mobile-fullscreen #unity-container {
        width: 100vw;
        height: 100vh;
        max-width: 100%;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 10;
      }
      
      .mobile-fullscreen #loading {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        z-index: 20;
      }
      
      /* Стили для сообщения о повороте экрана */
      #rotate-device {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #0a1929;
        color: white;
        z-index: 30;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 20px;
        box-sizing: border-box;
      }
      
      #rotate-device .icon {
        font-size: 50px;
        margin-bottom: 20px;
        animation: rotate 2s infinite ease-in-out;
        display: inline-block;
      }
      
      #rotate-device p {
        font-size: 18px;
        margin: 10px 0;
      }
      
      @keyframes rotate {
        0% { transform: rotate(0deg); }
        25% { transform: rotate(90deg); }
        75% { transform: rotate(90deg); }
        100% { transform: rotate(0deg); }
      }
      
      /* Блокируем портретную ориентацию на мобильных устройствах */
      @media screen and (max-width: 768px) and (orientation: portrait) {
        .mobile-fullscreen #unity-container {
          display: none;
        }
        
        #rotate-device {
          display: flex;
        }
      }
      
      /* Стили для подсказки */
      #hint {
        display: none;
        position: absolute;
        right: 20px;
        bottom: 20px;
        transform: none;
        z-index: 25;
      }
      
      #hint button {
        background: rgba(0, 0, 0, 0.4);
        border: none;
        width: 40px;
        height: 40px;
        padding: 8px;
        cursor: pointer;
        border-radius: 8px;
        transition: all 0.3s ease;
        backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      #hint button svg {
        width: 24px;
        height: 24px;
        fill: white;
        transition: all 0.3s ease;
      }
      
      #hint button:hover {
        background: rgba(0, 0, 0, 0.6);
        transform: scale(1.1);
      }
      
      #hint button:active {
        transform: scale(0.95);
      }
      
      /* Анимация появления подсказки */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: scale(0.8);
        }
        to {
          opacity: 1;
          transform: scale(1);
        }
      }
      
      #hint.show {
        animation: fadeInUp 0.3s ease forwards;
      }
    </style>
  </head>
  <body>
    <div id="unity-container">
      <canvas id="unity-canvas" tabindex="-1"></canvas>
      <!-- Hint for fullscreen mode -->
      <div id="hint">
        <button onclick="toggleFullscreen()" title="Toggle Fullscreen">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <div id="loading">
      LOADING
      <div class="loader"><div></div><div></div><div></div><div></div></div>
    </div>
    
    <div id="rotate-device">
      <div class="icon">📱</div>
      <p>For a better view, please,</p>
      <p>rotate the device horizontally</p>
    </div>
    
    <script src="Build/vtour-14-start-point-fix.loader.js"></script>
    <script>
      var isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      var containerElement = document.getElementById("unity-container");
      var canvasElement = document.getElementById("unity-canvas");
      var rotateMessage = document.getElementById("rotate-device");
      var loadingElement = document.getElementById("loading");
      var hintElement = document.getElementById("hint");
      
      // Настройки размера canvas
      var defaultWidth = 1020;
      var defaultHeight = 650;
      
      if (isMobile) {
        // Настройки для мобильных устройств
        document.body.classList.add("mobile-fullscreen");
        
        canvasElement.style.width = "100%";
        canvasElement.style.height = "100%";
        
        // Функция для проверки ориентации устройства
        function checkOrientation() {
          if (window.innerHeight > window.innerWidth) {
            // Портретная ориентация
            rotateMessage.style.display = "flex";
            containerElement.style.display = "none";
            loadingElement.style.display = "none";
            hintElement.style.display = "none";
          } else {
            // Альбомная ориентация
            rotateMessage.style.display = "none";
            containerElement.style.display = "block";
            // Показываем индикатор загрузки только если Unity еще не загружен
            if (!window.unityInstanceLoaded) {
              loadingElement.style.display = "block";
            }
          }
        }
        
        // Проверяем ориентацию при загрузке и изменении
        window.addEventListener('orientationchange', checkOrientation);
        window.addEventListener('resize', checkOrientation);
        checkOrientation();
      } else {
        // Настройки для десктопа
        // Устанавливаем начальные размеры для canvas
        canvasElement.width = defaultWidth;
        canvasElement.height = defaultHeight;
        canvasElement.style.width = defaultWidth + "px";
        canvasElement.style.height = defaultHeight + "px";
      }

      // Функция для настройки размера канваса
      function resizeCanvas() {
        if (isMobile) {
          if (window.innerHeight < window.innerWidth) { // только в альбомной ориентации
            canvasElement.width = window.innerWidth;
            canvasElement.height = window.innerHeight;
          }
        } else {
          // Если игра в полноэкранном режиме
          if (document.fullscreenElement || document.webkitFullscreenElement) {
            canvasElement.style.width = "100%";
            canvasElement.style.height = "100%";
          } else {
            // Масштабирование canvas на десктопе при маленьком окне
            if (window.innerWidth < defaultWidth + 20) {
              let maxWidth = Math.min(window.innerWidth - 20, defaultWidth);
              let aspectRatio = defaultHeight / defaultWidth;
              
              canvasElement.style.width = maxWidth + "px";
              canvasElement.style.height = (maxWidth * aspectRatio) + "px";
            } else {
              canvasElement.style.width = defaultWidth + "px";
              canvasElement.style.height = defaultHeight + "px";
            }
          }
        }
      }
      
      window.addEventListener('resize', resizeCanvas);
      resizeCanvas();

      createUnityInstance(canvasElement, {
        dataUrl: "Build/vtour-14-start-point-fix.data.unityweb",
        frameworkUrl: "Build/vtour-14-start-point-fix.framework.js.unityweb",
        codeUrl: "Build/vtour-14-start-point-fix.wasm.unityweb",
        streamingAssetsUrl: "StreamingAssets",
        companyName: "DefaultCompany",
        productName: "360",
        productVersion: "0.1.0"
      }).then((unityInstance) => {
        window.unityInstanceLoaded = true;
        document.getElementById('loading').style.display = 'none';
        // Показываем подсказку только на десктопе с анимацией
        if (!isMobile) {
          const hintElement = document.getElementById('hint');
          hintElement.style.display = 'block';
          // Добавляем небольшую задержку перед показом анимации
          setTimeout(() => {
            hintElement.classList.add('show');
          }, 100);
        }
      }).catch((error) => {
        console.error('Error loading Unity:', error);
      });
      
      // Функция для переключения полноэкранного режима
      function toggleFullscreen() {
        if (isMobile) {
          if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
          } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
          }
        } else {
          if (document.fullscreenEnabled) {
            if (!document.fullscreenElement) {
              containerElement.requestFullscreen();
            } else {
              document.exitFullscreen();
            }
          } else if (document.webkitFullscreenEnabled) {
            if (!document.webkitFullscreenElement) {
              containerElement.webkitRequestFullscreen();
            } else {
              document.webkitExitFullscreen();
            }
          }
        }
      }
    </script>
  </body>
</html>