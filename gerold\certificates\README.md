# Настройка Apple Wallet для D2D Studio Loyalty Card

Для полноценной работы карты лояльности в Apple Wallet требуется настройка Apple Developer аккаунта и получение соответствующих сертификатов.

## Необходимые файлы

В этой директории должны находиться следующие файлы:

1. `pass.json` - шаблон карты лояльности (уже создан)
2. `wwdr.pem` - сертификат Apple WWDR 
3. `signerCert.pem` - ваш сертификат для подписи Pass
4. `signerKey.pem` - приватный ключ для подписи
5. `demo_loyalty.pkpass` - демо-файл для тестирования (опционально)

## Шаги для получения сертификатов

### 1. Зарегистрируйтесь в Apple Developer Program

Необходимо иметь аккаунт Apple Developer ($99/год): [developer.apple.com](https://developer.apple.com)

### 2. Создайте Pass Type ID

1. Войдите в [Apple Developer Portal](https://developer.apple.com/account/)
2. Перейдите в "Certificates, IDs & Profiles"
3. Выберите "Identifiers" и нажмите "+" для создания нового идентификатора
4. Выберите "Pass Type ID" и нажмите "Continue"
5. Введите описание (например, "D2D Studio Loyalty Pass")
6. Введите идентификатор (например, "pass.com.d2dstudio.loyalty")
7. Нажмите "Register" и затем "Done"

### 3. Создайте сертификат для подписи Pass

1. В Developer Portal перейдите в "Certificates"
2. Нажмите "+" для создания нового сертификата
3. Выберите "Pass Type ID Certificate" и нажмите "Continue"
4. Выберите созданный ранее Pass Type ID и нажмите "Continue"
5. Следуйте инструкциям для создания CSR (Certificate Signing Request)
6. Загрузите созданный CSR и нажмите "Continue"
7. Скачайте сертификат (.cer файл)

### 4. Преобразуйте сертификаты в PEM формат

Используйте следующие команды в терминале:

```bash
openssl x509 -in PassTypeIDCertificate.cer -inform DER -out signerCert.pem -outform PEM

openssl pkcs12 -export -inkey private_key.key -in signerCert.pem -out certificate.p12
openssl pkcs12 -in certificate.p12 -nocerts -out signerKey.pem -nodes

curl https://www.apple.com/certificateauthority/AppleWWDRCA.cer -o AppleWWDRCA.cer
openssl x509 -in AppleWWDRCA.cer -inform DER -out wwdr.pem -outform PEM
```

### 5. Разместите сертификаты в этой директории

Поместите полученные файлы `wwdr.pem`, `signerCert.pem` и `signerKey.pem` в эту директорию.

### 6. Настройте переменную окружения (если сертификат защищен паролем)

Если ваш приватный ключ защищен паролем, установите переменную окружения:

```bash
export PASS_SIGNING_KEY=your_passphrase

```

## Тестирование без сертификатов

Для тестирования без реальных сертификатов вы можете создать демо-файл `.pkpass`:

1. Используйте онлайн-сервисы для создания тестовых .pkpass файлов
2. Разместите полученный файл в этой директории с именем `demo_loyalty.pkpass`

## Дополнительные ресурсы

- [Apple Wallet Developer Guide](https://developer.apple.com/library/archive/documentation/UserExperience/Conceptual/PassKit_PG/index.html)
- [PassKit Package Format Reference](https://developer.apple.com/library/archive/documentation/UserExperience/Reference/PassKit_Bundle/Chapters/Introduction.html) 