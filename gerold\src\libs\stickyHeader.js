const stickyHeader = () => {
  let lastScrollTop = 0;

  window.addEventListener("scroll", () => {
    const scroll = window.scrollY;
    const header = document.querySelector(".header-area.header-sticky");
    if (header) {
      if (scroll > 100) {
        // Show header with fade in
        header.classList.add("sticky");
        header.classList.remove("sticky-out");
      } else {
        // Hide header with fade out
        header.classList.remove("sticky");
        header.classList.add("sticky-out");
      }

      lastScrollTop = scroll;
    }
  });
};

export default stickyHeader;
