"use client";
import { useHeaderContext } from "@/context_api/HeaderContext";
import getNavItems from "@/libs/getNavItems";
import Link from "next/link";
import { useEffect } from "react";

const MobileMenu = ({ isActiveMobileMenu, setIsActiveMobileMenu }) => {
  const { isIndexPage } = useHeaderContext();
  const navItems = getNavItems();
  
  const handleLinkClick = () => {
    setIsActiveMobileMenu(false);
  };

  // Add scroll blocking when mobile menu is active
  useEffect(() => {
    if (isActiveMobileMenu) {
      document.body.style.overflow = "hidden"; // Block scrolling
    } else {
      document.body.style.overflow = ""; // Restore scrolling
    }
    
    // Cleanup function to ensure scrolling is restored
    return () => {
      document.body.style.overflow = "";
    };
  }, [isActiveMobileMenu]);
  
  return (
    <div
      className={`mobile-menu fixed left-0 top-0 w-full h-screen bg-seondary-color z-[9999] transition-all duration-300 ease-in-out lg:hidden ${
        isActiveMobileMenu ? "visible opacity-100" : "invisible opacity-0"
      }`}
      style={{
        transform: isActiveMobileMenu ? "translateY(0)" : "translateY(-100%)",
        transition: "transform 0.3s ease, opacity 0.3s ease, visibility 0.3s",
        pointerEvents: isActiveMobileMenu ? 'auto' : 'none'
      }}
    >
      <div className="container pt-40">
        <ul className="ml-4 mt-20">
          {navItems?.length
            ? navItems?.map(({ name, path, path2 }, idx) => (
                <li key={idx} className="mb-6">
                  <Link
                    href={isIndexPage ? path : path2}
                    className="text-size-48 text-white-color uppercase leading-1.2 py-10px block"
                    onClick={handleLinkClick}
                  >
                    {name}
                  </Link>
                </li>
              ))
            : ""}
        </ul>
      </div>
    </div>
  );
};

export default MobileMenu;
