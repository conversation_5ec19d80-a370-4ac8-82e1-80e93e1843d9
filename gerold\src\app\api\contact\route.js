import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { firstName, lastName, email, phone, subject, message } = await request.json();
    
    // Basic validation
    if (!firstName || !lastName || !email || !message) {
      return NextResponse.json(
        { success: false, message: 'Required fields missing' },
        { status: 400 }
      );
    }
    
    // For now, just log the contact form data
    console.log('Contact form submission:', {
      firstName,
      lastName,
      email,
      phone,
      subject,
      message
    });
    
    return NextResponse.json(
      { success: true, message: 'Message received successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}