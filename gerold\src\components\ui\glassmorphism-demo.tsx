'use client'

import { GlassmorphismHeader } from './glassmorphism-header'

export function GlassmorphismDemo() {
  return (
    <div className="min-h-screen relative">
      {/* Background content to show through the glass */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-green-600">
        <div className="grid grid-cols-4 gap-4 p-8">
          {Array.from({ length: 20 }).map((_, i) => (
            <div 
              key={i} 
              className="h-32 bg-white/20 rounded-lg flex items-center justify-center text-white text-2xl font-bold"
            >
              {i + 1}
            </div>
          ))}
        </div>
      </div>

      {/* Test glassmorphism header */}
      <GlassmorphismHeader threshold={50}>
        <div className="py-4">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center">
              <div className="text-white font-bold text-xl">Glass Header</div>
              <nav className="flex space-x-6">
                <a href="#" className="text-white hover:text-purple-300">Home</a>
                <a href="#" className="text-white hover:text-purple-300">About</a>
                <a href="#" className="text-white hover:text-purple-300">Contact</a>
              </nav>
            </div>
          </div>
        </div>
      </GlassmorphismHeader>

      {/* Scrollable content */}
      <div className="relative z-10 pt-20">
        <div className="bg-black/60 text-white p-8 space-y-8">
          <h1 className="text-4xl font-bold">Scroll Down to See Glassmorphism Effect</h1>
          {Array.from({ length: 10 }).map((_, i) => (
            <div key={i} className="p-6 bg-white/10 rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">Section {i + 1}</h2>
              <p className="text-lg leading-relaxed">
                This is content that will be blurred behind the glassmorphism header. 
                The header should become visible with a beautiful blur effect when you scroll down.
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 