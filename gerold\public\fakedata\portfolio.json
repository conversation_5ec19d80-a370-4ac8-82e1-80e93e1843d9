[{"id": 9, "title": "3D Modelling and VFX г", "title2": "High-Quality Game Assets and Visual Effects Production", "img": "/img/portfolio/bb-preview.jpg", "imgLarge": "/img/portfolio/bb-preview.jpg", "detailsImg": "/img/portfolio/bb-preview.jpg", "desc": "We delivered a comprehensive package of high-quality 3D models, textures, and visual effects for a leading game development studio. Our team created optimized game-ready assets including character models, environment pieces, weapons, and particle effects that enhanced the overall gaming experience while maintaining optimal performance across multiple platforms.", "shortDesc": "Professional 3D assets and VFX for AAA game production", "desc1": "Our specialized team developed a complete suite of game-ready 3D assets, from detailed character models to immersive environmental elements, all optimized for real-time rendering in modern game engines.", "desc2": "The project included advanced visual effects, particle systems, and shader work that brought the game world to life while ensuring smooth performance across PC, console, and mobile platforms.", "category": "Branding", "featured": true, "featuredDesc": "This project showcases our expertise in creating production-ready 3D assets and visual effects for the gaming industry, combining artistic excellence with technical optimization for seamless gameplay experience.", "featuredImg": "/img/portfolio/bb-preview.jpg", "employee": {"name": "D2D Game Assets Team", "desig": "3D Artists & VFX Specialists", "img": ""}, "tags": ["Branding", "Graphic Design", "3D Model"], "dataFilter": "branding", "statusItem": [{"title": "Category", "desc": "3D Modelling & VFX"}, {"title": "Client", "desc": "NDA"}, {"title": "Duration", "desc": "8 months"}], "descItems": [{"title": "The Challenge & Our Approach", "desc": "The game development studio required a diverse range of high-quality 3D assets and visual effects that would work seamlessly across multiple platforms while maintaining consistent visual quality. We developed a streamlined pipeline that included high-poly sculpting, low-poly optimization, UV mapping, PBR texturing, and real-time VFX creation. Our team worked closely with the client's technical artists to ensure all assets met their specific engine requirements and performance targets."}, {"title": "Production Pipeline & Results", "desc": "We delivered over 200 unique 3D models, including characters, weapons, vehicles, and environmental assets, along with 50+ custom visual effects and particle systems. Each asset was optimized for real-time rendering with multiple LOD levels and platform-specific optimizations. The project resulted in enhanced visual fidelity for the game while maintaining target frame rates across all supported platforms. Our efficient workflow allowed the client to meet their production deadlines and launch successfully.", "video": "/videos/bb.mp4"}], "gallery": ["/img/portfolio/bb-preview.jpg", "/img/portfolio/bb-preview-2.jpg"]}, {"id": 1, "title": "3D Dental Visualization", "title2": "Project Description", "img": "/img/portfolio/dentall.jpg", "imgLarge": "/img/portfolio/dentall.jpg", "detailsImg": "/img/portfolio/dentall.jpg", "desc": "For the Spanish dental clinic San Pedro, we developed a cutting-edge 3D visualization that clearly illustrates the structure of teeth, the placement of nano-braces, and the step-by-step process of dental procedures. This interactive solution was designed to help patients better understand how to care for their braces and what to expect before and after treatment. As a result, patient anxiety was significantly reduced, communication became more efficient, and client satisfaction improved through transparency and education. This tool not only enhanced trust in the clinic but also strengthened the clinic's reputation as a tech-forward, patient-first medical provider.", "shortDesc": "High-Quality Visualization for San Pedro Clinic in Spain", "desc1": "This interactive solution helps patients understand procedures, brace care, and treatment outcomes, reducing anxiety and improving satisfaction.", "desc2": "The visualization enhanced clinic trust and positioned San Pedro Clinic as a tech-forward, patient-focused provider.", "category": "Branding", "featured": false, "featuredDesc": "", "featuredImg": "", "employee": {"name": "", "desig": "", "img": ""}, "tags": ["Branding", "Graphic Design", "User Stories"], "dataFilter": "mobile-app", "statusItem": [{"title": "Category", "desc": "3D"}, {"title": "Client", "desc": "San Pedro Clinic (Spain)"}, {"title": "Start Date", "desc": "March 12, 2024"}], "descItems": [{"title": "The Challenge & Solution", "desc": "For the Spanish dental clinic San Pedro, we developed a cutting-edge 3D visualization that clearly illustrates the structure of teeth, the placement of nano-braces, and the step-by-step process of dental procedures. This interactive solution was designed to help patients better understand how to care for their braces and what to expect before and after treatment."}, {"title": "The Results", "desc": "Patient anxiety was significantly reduced, communication became more efficient, and client satisfaction improved through transparency and education. This tool not only enhanced trust in the clinic but also strengthened the clinic's reputation as a tech-forward, patient-first medical provider."}]}, {"id": 2, "title": "Farming VR Expo", "title2": "Project Description", "img": "/img/portfolio/vr-1.1.jpeg", "imgLarge": "/img/project/vr-1.1.jpeg", "detailsImg": "/img/portfolio/portfolio-details-1.jpg", "desc": "A virtual reality simulator that immerses exhibition visitors in the role of a combine harvester operator, enabling them to learn the basics of modern farming.", "shortDesc": "Experience the Thrill of Harvesting with VR", "desc1": "From the very start of the simulator, the user is shown a tutorial that teaches them how to interact with the combine harvester. The main goal for the user is to drive to the field while operating the machine, and then, based on the indicators of the combine, collect wheat from the field.", "category": "UX/UI", "featured": true, "featuredDesc": " Project was about precision and information. That's all. Our design tem helps clients achieve their marketing Trager and branding that appeals to a website", "featuredImg": "/img/project/project-2.png", "employee": {"name": "<PERSON>", "desig": "UI & UX designer", "img": "/img/project/project-1.png"}, "tags": ["Branding", "Graphic Design", "User Stories"], "dataFilter": "uxui", "statusItem": [{"title": "Category", "desc": "VR Simulator"}, {"title": "Client", "desc": "Independent investor"}, {"title": "Start Date", "desc": "December 05, 2024"}], "descItems": [{"title": "The story", "desc": "The project was developed from scratch, including code and design. Since this is a VR simulator, the project integrates control elements in the form of a steering wheel and pedals, a specialized farming joystick, and most importantly, it is fully integrated with the Oculus Quest Pro headset for free interaction with the VR world. Additionally, we were fully responsible for the installation and adjustment of the equipment directly at the exhibition stand."}]}, {"id": 3, "title": "Nuclear Station", "title2": "Project Description", "img": "/img/portfolio/vr-2.png", "imgLarge": "/img/portfolio/vr-2.png", "detailsImg": "/img/portfolio/portfolio-details-2.jpg", "desc": "A detailed 3D render demonstrating the operation of a nuclear power plant through interactive engagement, inviting visitors to insert a rod into the reactor model on the stand.", "shortDesc": "Engaging interactive 3D nuclear reactor experience", "category": "3D render", "featured": true, "featuredDesc": " Project was about precision and information. That's all. Our design tem helps clients achieve their marketing Trager and branding that appeals to a website", "featuredImg": "/img/project/project-3.png", "employee": {"name": "", "desig": "", "img": ""}, "tags": ["Branding", "Graphic Design", "User Stories"], "dataFilter": "3d", "statusItem": [{"title": "Category", "desc": "3D render"}, {"title": "Client", "desc": "Independent investor"}, {"title": "Start Date", "desc": "January 01, 2025"}], "descItems": [{"title": "The story", "desc": "The project involved creating a detailed 3D render and interactive model of a nuclear power plant's reactor. Visitors could physically interact by inserting a model rod, triggering an animation showing the plant's operation. Developed entirely in-house, including coding and 3D design."}]}, {"id": 4, "title": "3D Solar Battery Model", "title2": "Transforming Engagement with 3D Innovation: TOTECO's Solar Panel Battery Model", "img": "/img/portfolio/solar3d.jpg", "imgLarge": "/img/portfolio/3.jpg", "detailsImg": "/img/portfolio/portfolio-details-4.jpg", "desc": "For the Dutch company TOTECO, we crafted a state-of-the-art interactive 3D model of their solar panel battery specifically for a dynamic video presentation. This innovative solution was designed to make complex technical details visually accessible, significantly boosting internal client engagement and streamlining communication. As a result, conversion rates increased by 15%, and team synergy improved markedly, accelerating decision-making and reinforcing trust in innovative technologies.", "shortDesc": "Interactive detailed 3D Model for TOTECO's Solar Panel Battery", "desc1": "The goal was to create an interactive 3D model for a video presentation to make complex technical details visually accessible.", "desc2": "This boosted client engagement, streamlined communication, increased conversion rates by 15%, and improved team synergy.", "category": "Branding", "featured": false, "featuredDesc": "", "featuredImg": "", "employee": {"name": "", "desig": "", "img": ""}, "tags": ["Branding", "Graphic Design", "3D Model"], "dataFilter": "branding", "statusItem": [{"title": "Category", "desc": "3D"}, {"title": "Client", "desc": "TOTECO"}, {"title": "Start Date", "desc": "August 20, 2024"}], "descItems": [{"title": "The Story", "desc": "We developed a cutting-edge interactive 3D model of TOTECO's solar panel battery for a video presentation. The aim was to simplify complex technical details, making them visually engaging and easy to understand."}, {"title": "Our Approach & Results", "desc": "The 3D model significantly enhanced internal client engagement and streamlined communication processes. This led to a tangible 15% increase in conversion rates, improved team synergy, faster decision-making, and built greater trust in innovative technologies within the company."}]}, {"id": 5, "title": "3D Animated Animals", "title2": "Project Description", "img": "/img/portfolio/kids-game.png", "imgLarge": "/img/portfolio/kids-game.png", "detailsImg": "/img/portfolio/kids-game.png", "desc": "For a colorful and educational game developed by Kids Games LLC, our team at D2D Studio created a full collection of stylized 3D animal characters — fun, expressive, and optimized for real-time use. Each model was fully rigged and animated with kid-friendly motion to enhance engagement and support interactive learning. From playful puppies to dancing ducks, every creature brought charm and energy to the game world.", "shortDesc": "Stylized 3D Animal Characters for Educational Kids Game", "desc1": "Created a collection of fun, expressive 3D animal characters optimized for real-time use in an educational game.", "desc2": "Each model was fully rigged and animated with kid-friendly motion to enhance engagement and support interactive learning.", "category": "Branding", "featured": true, "featuredDesc": "This project showcases our expertise in delivering game-ready, optimized 3D assets for mobile and web-based platforms tailored to young audiences.", "featuredImg": "/img/portfolio/kids-game.jpg", "employee": {"name": "", "desig": "", "img": ""}, "tags": ["Branding", "Graphic Design", "User Stories"], "dataFilter": "branding", "statusItem": [{"title": "Category", "desc": "3D Modeling & Animation"}, {"title": "Client", "desc": "Kids Games LLC"}, {"title": "Start Date", "desc": "December 2024"}], "descItems": [{"title": "The Challenge & Solution", "desc": "For a colorful and educational game developed by Kids Games LLC, our team at D2D Studio created a full collection of stylized 3D animal characters — fun, expressive, and optimized for real-time use."}, {"title": "The Results", "desc": "Each model was fully rigged and animated with kid-friendly motion to enhance engagement and support interactive learning. From playful puppies to dancing ducks, every creature brought charm and energy to the game world."}]}, {"id": 6, "title": "3D Model for NFT", "title2": "High-Quality 3D Characters for NFT Collection", "img": "/img/portfolio/project-4.png", "imgLarge": "/img/portfolio/nft-models.jpg", "detailsImg": "/img/portfolio/portfolio-details-3.jpg", "desc": "One of our outsourcing clients approached us for 3D models of beautiful characters for their NFT collection. We provided them with 4 high-quality models that truly stand out in the competitive NFT marketplace.", "shortDesc": "Crafting premium 3D character models for exclusive NFT collections", "desc1": "Our streamlined 3D modeling pipeline ensures efficiency and excellence at every step, from concept design to final rendering. We focus on creativity and precision, delivering stunning results that meet our clients' unique needs.", "desc2": "For this project, we followed our comprehensive 6-step workflow: High Poly modeling, Low-Poly optimization, UV Wrapping, Baking, Texturing, and Rigging for Unreal Engine 5. This methodical approach allowed us to create characters with both visual appeal and technical excellence.", "category": "Branding", "featured": true, "featuredDesc": "Our team created highly detailed 3D character models optimized for NFT collections, combining artistic excellence with technical optimization for blockchain platforms.", "featuredImg": "/img/portfolio/project-4.png", "employee": {"name": "", "desig": "", "img": ""}, "tags": ["Branding", "Graphic Design", "3D Art", "NFT"], "dataFilter": "branding", "statusItem": [{"title": "Category", "desc": "3D Art"}, {"title": "Client", "desc": "NDA (NFT Project)"}, {"title": "Duration", "desc": "5 weeks"}], "descItems": [{"title": "Our Process", "desc": "Our comprehensive 3D modeling workflow for this NFT collection included six critical stages: 1) High Poly modeling to create detailed, expressive characters; 2) Low-Poly optimization for efficient rendering and compatibility with various platforms; 3) Careful UV Wrapping to ensure perfect texture placement; 4) Baking to transfer details from high-poly to low-poly models; 5) Detailed Texturing to give each character unique visual appeal; and 6) Rigging for Unreal Engine 5, making the characters animation-ready for future implementations."}, {"title": "The Results", "desc": "The finished collection features four distinct, highly detailed character models that exceeded our client's expectations. Each character maintains its visual quality across different platforms while being optimized for NFT marketplaces. The models stand out for their artistic quality and technical excellence, providing our client with valuable assets for their blockchain project. Our efficient workflow allowed us to complete the project within the 5-week timeframe, delivering production-ready assets that immediately added value to our client's NFT initiative."}]}, {"id": 7, "title": "Sustainable Office Visualization", "title2": "Project Description", "img": "/img/portfolio/office3.png", "imgLarge": "/img/portfolio/office3.png", "detailsImg": "/img/portfolio/office3.png", "desc": "We created a photorealistic 3D visualization of a sustainable office space, illustrating how workspace organization influences employee productivity, emotional well-being, and overall team atmosphere. The render was based on reference photos of the client’s existing standard office, allowing for a realistic transformation into a more eco-conscious and ergonomic environment.", "shortDesc": "3D visualization of an eco-friendly, productivity-enhancing office", "desc1": "Our team developed detailed 3D models reflecting an environmentally conscious redesign of a typical office, with special attention to lighting, materials, and layout.", "desc2": "The visualization showcases how thoughtful spatial planning improves employee focus and collaboration, while creating a positive and healthy work atmosphere.", "category": "Apps", "featured": true, "featuredDesc": "This project highlights our expertise in using 3D architectural visualization to demonstrate the positive impact of eco-friendly office design on employee well-being and performance.", "featuredImg": "/img/portfolio/office2.png", "employee": {"name": "", "desig": "", "img": ""}, "tags": ["3D Visualization", "Architecture", "Sustainable Design", "Interior Design"], "dataFilter": "mobile-app", "statusItem": [{"title": "Category", "desc": "3D Architectural Visualization"}, {"title": "Client", "desc": "Modern Spaces Design"}, {"title": "Start Date", "desc": "April 15, 2025"}], "descItems": [{"title": "The Challenge & Solution", "desc": "The client wanted to explore how redesigning their standard office could enhance productivity and employee satisfaction. We delivered a lifelike 3D visualization of a sustainable office layout that made it easy to assess potential improvements before any physical changes were made."}, {"title": "The Process", "desc": "Using reference photos of the current office space, we created precise 3D models, integrating eco-friendly materials, optimized lighting, and ergonomic layouts. A video walkthrough demonstrates how the new design supports a more effective and positive work environment."}, {"title": "Additional Media", "desc": "A video walkthrough of the redesigned office space is available, showing the upgraded environment and emphasizing how spatial changes influence employee mood and collaboration.", "video": "/videos/office2.mp4"}], "gallery": ["/img/portfolio/office1.png", "/img/portfolio/office2.png", "/img/portfolio/office3.png"]}, {"id": 8, "title": "Realtime Virtual Interior", "title2": "Interactive Real-time Interior Visualization with Unity", "img": "/img/portfolio/interior-featured.jpg", "imgLarge": "/img/portfolio/interior-featured.jpg", "desc": "A cutting-edge interior design simulation built with Unity that allows clients to explore and customize high-end residential spaces in real-time, providing an immersive experience with photorealistic rendering quality.", "shortDesc": "Experience luxury interiors with interactive real-time customization made with Unity 3D", "desc1": "Our Unity-powered interior visualization solution transforms how clients experience design proposals. With real-time rendering capabilities, users can walk through spaces, change materials, adjust lighting, and see the results instantly - all with stunning visual fidelity comparable to traditional pre-rendered imagery.", "desc2": "The application provides an intuitive interface allowing users to modify over 200 design elements from furniture to finishes. The platform supports both desktop and VR modes, making it versatile for presentations to clients at any location or remotely through our cloud streaming service.", "category": "Apps", "featured": true, "featuredDesc": "Our team developed an immersive real-time interior simulation application that combines architectural precision with interactive design capabilities, powered by Unity's advanced rendering technology.", "featuredImg": "/img/portfolio/interior-featured.jpg", "employee": {"name": "Design Team", "desig": "Unity Developers"}, "tags": ["Unity", "Real-time", "Interior Design", "VR"], "dataFilter": "mobile-app", "statusItem": [{"title": "Category", "desc": "Interior Simulation"}, {"title": "Client", "desc": "NDA"}, {"title": "Duration", "desc": "NDA"}], "descItems": [{"title": "Our Solution", "desc": "We leveraged Unity's High Definition Render Pipeline (HDRP) to create a real-time visualization tool that maintains exceptional visual quality while enabling dynamic interaction. The application features physically-based rendering, advanced post-processing, and dynamic global illumination to achieve photorealistic results. Users can navigate through spaces with intuitive controls, interact with objects, change time-of-day, swap materials, and modify layouts—all instantly rendered in real-time with accurate lighting and reflections. The platform includes a custom user interface designed specifically for non-technical users, making it accessible to both the design firm and their clients."}, {"title": "The Results", "desc": "Since implementing the interactive interior simulation, our client has reported a 40% increase in client satisfaction and a 25% reduction in the decision-making timeline for major projects. The ability to make real-time adjustments during client meetings has dramatically improved communication between designers and clients, reducing misunderstandings and revision requests. The platform has become an integral part of their sales process, differentiating them from competitors and justifying premium pricing for their design services. Additionally, the VR component has been particularly valuable for international clients who can now experience spaces remotely as if they were physically present."}]}]