<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Hex Merge Game</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html, body {
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: #231F20;
      }

      #app {
        position: fixed;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .game-container {
        position: relative;
        background-color: #f0f0f0;
        aspect-ratio: 1 / 1;
      }

      /* ПК в альбомной ориентации */
      @media (orientation: landscape) {
        .game-container {
          height: 90vh;
          width: auto;
          max-width: 90vh;
        }
      }

      /* Мобильные в портретной ориентации */
      @media (orientation: portrait) {
        .game-container {
          width: 95vw;
          height: auto;
          max-height: 95vw;
        }
      }

      canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: block;
        touch-action: none;
      }

      #loading {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 18px;
        text-align: center;
        z-index: 100;
        background: rgba(0, 0, 0, 0.8);
        padding: 20px;
        border-radius: 10px;
      }

      .loader {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 20px;
        margin-top: 10px;
      }

      .loader div {
        position: absolute;
        top: 8px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: #fff;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
      }

      .loader div:nth-child(1) {
        left: 8px;
        animation: loader1 0.6s infinite;
      }

      .loader div:nth-child(2) {
        left: 8px;
        animation: loader2 0.6s infinite;
      }

      .loader div:nth-child(3) {
        left: 32px;
        animation: loader2 0.6s infinite;
      }

      .loader div:nth-child(4) {
        left: 56px;
        animation: loader3 0.6s infinite;
      }

      @keyframes loader1 {
        0% { transform: scale(0); }
        100% { transform: scale(1); }
      }

      @keyframes loader2 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(24px, 0); }
      }

      @keyframes loader3 {
        0% { transform: scale(1); }
        100% { transform: scale(0); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="game-container">
        <canvas id="game-canvas" tabindex="-1"></canvas>
      </div>
    </div>
    
    <div id="loading">
      LOADING
      <div class="loader"><div></div><div></div><div></div><div></div></div>
    </div>

    <script type="module" crossorigin src="assets/index-C6oMYL5y.js"></script>
    <link rel="stylesheet" crossorigin href="assets/index-BaiKPFAq.css">
    
    <script>
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      const appEl = document.getElementById("app");
      const gameContainer = document.querySelector('.game-container');
      const canvasEl = document.getElementById("game-canvas");
      const loadingEl = document.getElementById("loading");

      // Функция для настройки размеров canvas
      function resizeCanvas() {
        const rect = gameContainer.getBoundingClientRect();
        const width = Math.floor(rect.width);
        const height = Math.floor(rect.height);
        
        // Принудительно устанавливаем размеры canvas
        canvasEl.width = width;
        canvasEl.height = height;
        
        console.log('Canvas size:', width, 'x', height);
      }

      // Вызываем сразу и с задержками для надежности
      resizeCanvas();
      requestAnimationFrame(resizeCanvas);
      setTimeout(resizeCanvas, 200);
      
      // Слушаем изменения размера экрана
      window.addEventListener('resize', resizeCanvas);
      window.addEventListener('orientationchange', function() {
        setTimeout(resizeCanvas, 300);
        setTimeout(resizeCanvas, 600);
      });

      // Скрываем индикатор загрузки
      window.addEventListener('load', function() {
        setTimeout(function() {
          loadingEl.style.display = 'none';
          resizeCanvas();
        }, 1000);
      });

      // Блокируем масштабирование на мобильных устройствах
      document.addEventListener('touchmove', function(e) {
        if (e.target === canvasEl) {
          e.preventDefault();
        }
      }, { passive: false });
    </script>
  </body>
</html>
