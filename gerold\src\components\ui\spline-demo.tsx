'use client'

import { SplineScene } from "@/components/ui/spline";
import { Spotlight } from "@/components/ui/spotlight"
 
export function SplineSceneBasic() {
  return (
    <div className="w-full h-[500px] bg-black/[0.96] relative overflow-hidden rounded-lg">
      <Spotlight
        className="-top-40 left-0 md:left-60 md:-top-20"
        fill="white"
      />
      
      <div className="flex h-full">
        

        {/* Right content - 40% */}
        <div className="w-[40%] relative">
          <SplineScene 
            scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
            className="w-full h-full"
            fallbackImage="/img/hero/d2d-card.jpeg"
            fallbackAlt="D2D Studio - VR/AR Development Agency"
            enableOnLowEnd={true}
          />
        </div>
      </div>
    </div>
  )
} 