'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import deviceDetection from '@/libs/deviceDetection'

interface SplineSceneProps {
  scene: string
  className?: string
  enableOnLowEnd?: boolean
  fallbackImage?: string // Custom fallback image path
  fallbackAlt?: string // Alt text for fallback image
  forceStatic?: boolean // Force static mode for testing
}

// Static image fallback component
const StaticFallback = ({ 
  fallbackImage, 
  fallbackAlt, 
  className 
}: { 
  fallbackImage: string
  fallbackAlt: string
  className?: string 
}) => {
  const [displayMode, setDisplayMode] = useState(13) // Start with nested-scale-60 (index 13)
  const [scrollPercent, setScrollPercent] = useState(0)
  const [isInitialAnimationPeriod, setIsInitialAnimationPeriod] = useState(true)
  
  // Check if we're in test mode
  const isTestMode = typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('test') === 'true'
  
  const modes = [
    { name: 'object-contain', class: 'object-contain', container: 'p-8', maxWidth: 'max-w-4xl' },
    { name: 'object-cover', class: 'object-cover', container: 'p-0', maxWidth: 'w-full' },
    { name: 'object-fill', class: 'object-fill', container: 'p-4', maxWidth: 'max-w-5xl' },
    { name: 'object-scale-down', class: 'object-scale-down', container: 'p-6', maxWidth: 'max-w-3xl' },
    { name: 'contain-centered', class: 'object-contain', container: 'p-12', maxWidth: 'max-w-2xl' },
    { name: 'cover-rounded', class: 'object-cover rounded-2xl', container: 'p-8', maxWidth: 'max-w-4xl' },
    { name: 'contain-large', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-6xl' },
    { name: 'contain-small', class: 'object-contain', container: 'p-16', maxWidth: 'max-w-xl' },
    { name: 'cover-no-crop', class: 'object-contain', container: 'p-2', maxWidth: 'w-full' },
    { name: 'responsive-fit', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-fit' },
    { name: 'full-width-fit', class: 'object-contain', container: 'p-0', maxWidth: 'w-full h-full' },
    { name: 'auto-scale', class: 'object-contain', container: 'p-6', maxWidth: 'max-w-none' },
    { name: 'perfect-scale', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none' },
    { name: 'nested-scale-80', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.8 },
    { name: 'nested-scale-70', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.7 },
    { name: 'nested-scale-60', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6 },
    { name: 'horizontal-80', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scaleX: 0.8, scaleY: 1 },
    { name: 'horizontal-70', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scaleX: 0.7, scaleY: 1 },
    { name: 'horizontal-60', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scaleX: 0.6, scaleY: 1 },
    { name: 'gradient-subtle', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6, gradient: 'linear-gradient(to bottom, transparent 0%, transparent 80%, rgba(0,0,0,0.1) 95%, rgba(0,0,0,0.4) 100%)' },
    { name: 'gradient-soft', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6, gradient: 'linear-gradient(to bottom, transparent 0%, transparent 70%, rgba(0,0,0,0.2) 90%, rgba(0,0,0,0.6) 100%)' },
    { name: 'gradient-smooth', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6, gradient: 'linear-gradient(to bottom, transparent 0%, transparent 75%, rgba(0,0,0,0.15) 88%, rgba(0,0,0,0.5) 100%)' },
    { name: 'gradient-natural', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6, gradient: 'linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.05) 70%, rgba(0,0,0,0.25) 90%, rgba(0,0,0,0.7) 100%)' },
    { name: 'gradient-gentle', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6, gradient: 'linear-gradient(to bottom, transparent 0%, transparent 85%, rgba(0,0,0,0.1) 97%, rgba(0,0,0,0.3) 100%)' },
    { name: 'no-gradient', class: 'object-contain', container: 'p-4', maxWidth: 'max-w-none', scale: 0.6, gradient: 'none' }
  ]
  
  const currentMode = modes[displayMode]
  
  // Easing function for smooth animation
  const easeOutQuart = (t: number) => 1 - Math.pow(1 - t, 4)
  
  // Mark initial animation as played after it completes
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialAnimationPeriod(false)
    }, 2700) // 1.5s delay + 1.2s slide-in + 1s buffer
    
    return () => clearTimeout(timer)
  }, [])
  
  // Scroll detection for progressive fly-out animation
  useEffect(() => {
    let ticking = false
    
    // Initialize scroll percentage
    const initScrollPercent = () => {
      const scrollY = window.scrollY
      const maxScroll = 800 // Increased from 400 to make less sensitive
      const percent = Math.min(scrollY / maxScroll, 1)
      setScrollPercent(percent)
    }
    
    initScrollPercent() // Set initial value
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY
          const maxScroll = 800 // Increased from 400 to make less sensitive
          const percent = Math.min(scrollY / maxScroll, 1) // Clamp between 0 and 1
          setScrollPercent(percent)
          ticking = false
        })
        ticking = true
      }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])
  
  const handleClick = () => {
    // Disabled clicking functionality - just for show now
    console.log('🎨 Static image display - no mode switching')
  }
  
  // Special handling for perfect-scale mode
  if (currentMode.name === 'perfect-scale') {
    return (
      <div 
        className={`${className} relative overflow-hidden bg-gradient-to-br from-purple-900/20 to-blue-900/20 flex items-center justify-center p-4 cursor-pointer`}
        onClick={handleClick}
      >
        <div className="absolute top-4 right-4 z-10 bg-black/80 text-white px-3 py-1 rounded-lg text-sm font-mono">
          {displayMode + 1}/{modes.length}: {currentMode.name}
        </div>
        
        <div className="relative" style={{ maxWidth: '90%', maxHeight: '90%' }}>
          <Image
            src={fallbackImage}
            alt={fallbackAlt}
            width={800}
            height={600}
            style={{
              width: 'auto',
              height: 'auto',
              maxWidth: '100%',
              maxHeight: '100%',
              borderRadius: '50px'
            }}
            priority
          />
        </div>
      </div>
    )
  }
  
  // Special handling for nested-scale modes (what we're using)
  if (currentMode.name.includes('nested-scale')) {
    return (
      <div 
        className={`${className} relative overflow-hidden bg-gradient-to-br from-purple-900/20 to-blue-900/20 flex items-center justify-center cursor-pointer`}
        onClick={handleClick}
      >
        {/* Outer container with click handler */}
        <div 
          onClick={handleClick}
          className="relative"
          style={{ 
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transform: 'translateY(-50px)' // Move image up by 50px
          }}
        >
          {/* Inner container with scaling and scroll-based animations */}
          <div 
            key={displayMode} // Force re-render when mode changes
            className={`relative ${isInitialAnimationPeriod ? 'floating-element-initial' : 'floating-element-scroll'}`}
            style={isInitialAnimationPeriod ? {} : { 
              transform: `scale(${0.6 - (easeOutQuart(scrollPercent) * 0.18)}) translateY(${-easeOutQuart(scrollPercent) * 100}px) translateX(${easeOutQuart(scrollPercent) * 120}px) rotate(${easeOutQuart(scrollPercent) * 8}deg)`,
              transformOrigin: 'center center',
              maxWidth: '100%',
              maxHeight: '100%',
              opacity: Math.max(0, 1 - (scrollPercent * 1.4)),
              transition: 'opacity 0.2s ease-out'
            }}
          >
            <Image
              src={fallbackImage}
              alt={fallbackAlt}
              width={800}
              height={600}
              className="rounded-[25px] md:rounded-[100px]"
              style={{
                width: 'auto',
                height: 'auto',
                display: 'block'
              }}
              priority
            />
          </div>
        </div>
        
        {/* CSS keyframes for slide-in fade animation and floating */}
        <style jsx>{`
          @keyframes slideInFadeUp {
            0% {
              opacity: 0;
              transform: translateY(40px) scale(0.6);
            }
            100% {
              opacity: 1;
              transform: translateY(0) scale(0.6);
            }
          }
          
          @keyframes float {
            0%, 100% {
              transform: translateY(0) scale(0.6);
            }
            50% {
              transform: translateY(-10px) scale(0.6);
            }
          }
          
          .floating-element-initial {
            opacity: 0;
            max-width: 100%;
            max-height: 100%;
            animation: slideInFadeUp 1.2s ease-out 1.5s forwards, float 6s ease-in-out 2.7s infinite;
          }
          
          .floating-element-scroll {
            /* No default styles - all controlled by JavaScript */
          }
        `}</style>
      </div>
    )
  }
  
  // Special handling for horizontal scaling modes
  if (currentMode.name.includes('horizontal')) {
    return (
      <div 
        className={`${className} relative overflow-hidden bg-gradient-to-br from-purple-900/20 to-blue-900/20 flex items-center justify-center p-4 cursor-pointer`}
        onClick={handleClick}
      >
        <div className="absolute top-4 right-4 z-10 bg-black/80 text-white px-3 py-1 rounded-lg text-sm font-mono">
          {displayMode + 1}/{modes.length}: {currentMode.name}
        </div>
        
        {/* Outer container for centering */}
        <div className="relative w-full h-full flex items-center justify-center">
          {/* Inner container with horizontal scaling only */}
          <div 
            className="relative"
            style={{ 
              transform: `scaleX(${currentMode.scaleX}) scaleY(${currentMode.scaleY})`,
              transformOrigin: 'center center',
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <Image
              src={fallbackImage}
              alt={fallbackAlt}
              width={800}
              height={600}
              style={{
                width: 'auto',
                height: 'auto',
                display: 'block',
                borderRadius: '50px'
              }}
              priority
            />
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div 
      className={`${className} relative overflow-hidden bg-gradient-to-br from-purple-900/20 to-blue-900/20 flex items-center justify-center ${currentMode.container} cursor-pointer`}
      onClick={handleClick}
    >
      {/* Mode indicator */}
      <div className="absolute top-4 right-4 z-10 bg-black/80 text-white px-3 py-1 rounded-lg text-sm font-mono">
        {displayMode + 1}/{modes.length}: {currentMode.name}
      </div>
      
      <div className={`relative w-full h-full ${currentMode.maxWidth} max-h-full`}>
        <Image
          src={fallbackImage}
          alt={fallbackAlt}
          fill
          className={currentMode.class}
          priority
        />
      </div>
    </div>
  )
}

// Loading animation component
const LoadingAnimation = ({ message = "🚀 Initializing neural networks..." }: { message?: string }) => (
  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900/20 to-blue-900/20">
    <div className="flex flex-col items-center space-y-8">
      {/* Spinning loader */}
      <div className="relative w-20 h-20">
        <div className="absolute inset-0 border-4 border-white/30 rounded-full"></div>
        <div className="absolute inset-0 border-4 border-transparent border-t-purple-400 rounded-full animate-spin"></div>
        <div className="absolute inset-3 border-2 border-transparent border-r-blue-400 rounded-full animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
        <div className="absolute inset-8 bg-white rounded-full animate-pulse"></div>
      </div>
      
      {/* Clear, visible text */}
      <div className="text-center space-y-4">
        <p className="text-white text-lg font-mono tracking-wide font-semibold drop-shadow-lg">
          {message}
        </p>
        
        {/* Simple animated dots */}
        <div className="flex justify-center space-x-2">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
          <div className="w-3 h-3 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '200ms'}}></div>
          <div className="w-3 h-3 bg-white rounded-full animate-bounce" style={{animationDelay: '400ms'}}></div>
        </div>
      </div>
    </div>
  </div>
)

// Runtime Spline loader to avoid build issues
const SplineWrapper = ({ scene, className, onLoad, onError }: {
  scene: string
  className?: string
  onLoad: () => void
  onError: () => void
}) => {
  const [SplineComponent, setSplineComponent] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load Spline at runtime to avoid build issues
    const loadSpline = async () => {
      try {
        const splineModule = await import('@splinetool/react-spline')
        setSplineComponent(() => splineModule.default)
        setIsLoading(false)
      } catch (error) {
        console.error('Failed to load Spline:', error)
        onError()
        setIsLoading(false)
      }
    }

    const timer = setTimeout(loadSpline, 1000)
    return () => clearTimeout(timer)
  }, [onError])

  if (isLoading || !SplineComponent) {
    return <LoadingAnimation message="🔮 Loading 3D holographic engine..." />
  }

  return (
    <SplineComponent
      scene={scene}
      className={className}
      onLoad={onLoad}
      onError={onError}
    />
  )
}

// Get default fallback image based on scene URL
const getDefaultFallbackImage = (scene: string): string => {
  // Map different Spline scenes to appropriate fallback images
  if (scene.includes('kZDDjO5HuC9GJUM2')) {
    return '/img/hero/d2d-card.jpeg' // Main VR scene fallback
  }
  
  // Default fallback for other scenes
  return '/img/hero/d2d-card.jpeg'
}

// Main Spline component
const SplineSceneInternal = ({ 
  scene, 
  className, 
  enableOnLowEnd = false,
  fallbackImage,
  fallbackAlt = "VR Experience Preview",
  forceStatic = false
}: SplineSceneProps) => {
  const [hasError, setHasError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isClient, setIsClient] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)
  const [shouldUseStatic, setShouldUseStatic] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Device detection on client side
  useEffect(() => {
    setIsClient(true)
    
    // Check URL parameter for testing
    const urlParams = new URLSearchParams(window.location.search)
    const urlForceStatic = urlParams.get('static') === 'true'
    
    // Check if should use static images
    const useStatic = forceStatic || urlForceStatic || (!enableOnLowEnd && deviceDetection.shouldUseStaticImages())
    setShouldUseStatic(useStatic)
    
    // If using static, no need for delayed rendering
    if (useStatic) {
      setShouldRender(true)
      setIsLoading(false)
    } else {
      const timer = setTimeout(() => setShouldRender(true), 2000)
      return () => clearTimeout(timer)
    }
  }, [enableOnLowEnd, forceStatic])

  useEffect(() => {
    if (shouldRender && !hasError && isClient && !shouldUseStatic) {
      timeoutRef.current = setTimeout(() => {
        setHasError(true)
        setIsLoading(false)
      }, 20000)
      
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
      }
    }
  }, [shouldRender, hasError, isClient, shouldUseStatic])

  const handleLoad = useCallback(() => {
    setIsLoading(false)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  const handleError = useCallback(() => {
    setHasError(true)
    setIsLoading(false)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  // Determine fallback image to use
  const finalFallbackImage = fallbackImage || getDefaultFallbackImage(scene)

  // SSR or static image fallback
  if (!isClient || shouldUseStatic) {
    return (
      <div className={`${className} relative overflow-hidden`}>
        <StaticFallback 
          fallbackImage={finalFallbackImage}
          fallbackAlt={fallbackAlt}
          className={className}
        />
      </div>
    )
  }

  // Loading states for 3D content
  if (!shouldRender) {
    return (
      <div className={`${className} relative overflow-hidden`}>
        <LoadingAnimation message="⚡ Calibrating holographic systems..." />
      </div>
    )
  }

  if (hasError) {
    return (
      <div className={`${className} relative overflow-hidden`}>
        <StaticFallback 
          fallbackImage={finalFallbackImage}
          fallbackAlt={fallbackAlt}
          className={className}
        />
      </div>
    )
  }

  // Render 3D Spline content for supported devices
  return (
    <div className={`${className} relative overflow-hidden`}>
      <SplineWrapper
        scene={scene}
        className={className}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  )
}

// Export with SSR protection
export const SplineScene = dynamic(() => Promise.resolve(SplineSceneInternal), {
  ssr: false,
  loading: () => {
    // Show static fallback during SSR
    return (
      <div className="w-full h-full relative overflow-hidden">
        <StaticFallback 
          fallbackImage="/img/hero/d2d-card.jpeg"
          fallbackAlt="D2D Studio - VR/AR Development Agency"
          className="w-full h-full"
        />
      </div>
    )
  }
}) 