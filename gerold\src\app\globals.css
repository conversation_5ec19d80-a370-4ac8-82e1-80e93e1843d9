@import url("https://fonts.googleapis.com/css2?family=Sora:wght@100..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Russo+One&display=swap");
/*-----------------------------------------------------------------------------------

Theme Name: Gerold - Personal Portfolio Tailwind Template
Theme URI: https://themejunction.net/html/gerold/demo/
Author: Theme-Junction
Author URI: https://themeforest.net/user/theme-junction
Description: Gerold - Personal Portfolio Tailwind Template

-----------------------------------------------------------------------------------

/************ TABLE OF CONTENTS ***************

  font family
	tailwind derectives
	variable
	default css
	header css
  hero css
  sevices css
  portfolio css
  testimonials css
  nice select css
  preloader css
  blog css
  paginations css
  sidebar css
  portfolio css

  
**********************************************/

/* tailwind derectives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* variable */
:root {
	--body-color: #dddddd;
	--primary-color: #8750f7;
	--primary-color-light: #140c1c;
	--secondary-color: #2a1454;
	--white-color: #ffffff;
	--off-white-color: #f6f3fc;
	--black-color: #050709;
	--gray-color: #d9d9d9;
	--gray-color2: #747779;
	--gray-color3: #22272c;
	--accent-color: #0f0715;
	--cream-light-color: #f6f3fc;

	/* font family */
	--body-font: "Sora", sans-serif;
	--russo: "Russo One", sans-serif;
	--font-family: "Font Awesome 6 Pro";

	/* shadcn/ui card variables */
	--card: 0 0% 100%;
	--card-foreground: 0 0% 3.9%;
	--muted-foreground: 0 0% 45.1%;
}
/* default css */
* {
	scrollbar-width: thin;
	scrollbar-color: var(--primary-color) var(--secondary-color);
}

::-webkit-scrollbar {
	height: 4px;
	width: 4px;
	background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
	background: var(--primary-color);
	-webkit-border-radius: 1ex;
	-webkit-box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.75);
}

::-webkit-scrollbar-corner {
	background: transparent;
}

body {
	color: var(--body-color);
	font-family: var(--body-font);
}
/* typography */
h1,
h2,
h3,
h4,
h5,
h6 {
	line-height: 1.2;
	font-weight: 700;
}
p {
	line-height: 1.5;
}
a {
	display: inline-block;
	transition: all 0.3s;
}
img {
	max-width: 100%;
	height: auto;
}

/* container */
.container {
	margin: 0 auto;
	padding: 0 15px;
}
@media (min-width: 576px) {
	.container {
		max-width: 540px;
	}
}
@media (min-width: 768px) {
	.container {
		max-width: 720px;
	}
}
@media (min-width: 992px) {
	.container {
		max-width: 960px;
	}
}
@media (min-width: 1200px) {
	.container {
		max-width: 1140px;
	}
}
@media (min-width: 1400px) {
	.container {
		max-width: 1320px;
	}
}
/* header css  */
@-webkit-keyframes bounceInUp {
	0%,
	10%,
	35%,
	50%,
	to {
		-webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
	}
	10% {
		-webkit-transform: translate3d(0, 0px, 0);
		transform: translate3d(0, 0px, 0);
	}
	35% {
		-webkit-transform: translate3d(0, -10px, 0);
		transform: translate3d(0, -10px, 0);
	}
	50% {
		-webkit-transform: translate3d(0, 2px, 0);
		transform: translate3d(0, 2px, 0);
	}
	80% {
		-webkit-transform: translate3d(0, -60px, 0); /* Adjusted to match new height */
		transform: translate3d(0, -60px, 0);
	}
	to {
		-webkit-transform: translate3d(0, -3000px, 0);
		transform: translate3d(0, -3000px, 0);
	}
}
@keyframes bounceInUp {
	0%,
	10%,
	35%,
	50%,
	to {
		-webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
	}
	10% {
		-webkit-transform: translate3d(0, 0px, 0);
		transform: translate3d(0, 0px, 0);
	}
	35% {
		-webkit-transform: translate3d(0, -10px, 0);
		transform: translate3d(0, -10px, 0);
	}
	50% {
		-webkit-transform: translate3d(0, 2px, 0);
		transform: translate3d(0, 2px, 0);
	}
	80% {
		-webkit-transform: translate3d(0, -60px, 0); /* Adjusted to match new height */
		transform: translate3d(0, -60px, 0);
	}
	to {
		-webkit-transform: translate3d(0, -3000px, 0);
		transform: translate3d(0, -3000px, 0);
	}
}
.header-area {
	position: relative;
	z-index: 9;
	background-color: var(--off-white-color);
}
.dark .header-area {
	background-color: var(--black-color);
}
.header-area.header-absolute {
	position: absolute;
	background-color: transparent;
	left: 0;
	top: 0;
	width: 100%;
	z-index: 99;
}

.header-area.header-2 {
	width: 100%;
	position: fixed;
	top: -70px; /* Reduced from -90px */
	left: 0;
	z-index: 100;
	opacity: 0;
	transition: 0.3s;
	box-shadow: 0 0 30px rgba(135, 80, 247, 0.2);
}

/* Disable old sticky header - using new glassmorphism component instead */
.header-area.header-2.sticky {
	display: none !important;
	pointer-events: none !important;
	z-index: -1 !important;
}
.header-area.header-2.sticky-out {
	display: none !important;
	pointer-events: none !important;
	z-index: -1 !important;
}
.nav .nav_item .active:after {
	width: 100%;
}
.nav .nav_item ul li .active {
	color: var(--primary-color);
}

.menu-bar button {
	border: none;
	padding: 0;
	background-color: transparent;
	height: 25px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	position: relative;
	transform-origin: center center;
	transform: rotate(0deg);
	cursor: pointer;
	transition: transform 300ms ease;
}
.menu-bar button span {
	height: 3px;
	width: 35px;
	display: block;
	background: var(--primary-color);
	cursor: pointer;
	transition: all 0.3s ease-in-out 0s;
	margin-left: auto;
}
.dark .menu-bar button span {
	background: var(--white-color);
}
.menu-bar button span:nth-child(2) {
	width: 40px;
	transition-delay: 200ms;
}
.menu-bar button span:nth-child(3) {
	width: 30px;
}
.menu-bar button span:nth-child(4) {
	position: absolute;
	top: -8px;
	left: 50%;
	transform: translateX(-50%);
	display: block;
	width: 3px;
	height: 0;
	transition: height 400ms;
}
.menu-bar button.active {
	transform: rotate(45deg);
	transition-delay: 400ms;
}
.menu-bar button.active span:nth-child(1) {
	width: 0;
}
.menu-bar button.active span:nth-child(3) {
	width: 0;
}
.menu-bar button.active span:nth-child(4) {
	height: 40px;
	transition: height 200ms ease;
	transition-delay: 200ms;
}
/* mobile menu */
.mobile-menu {
	transform: scaleY(0);
	transition: all 0.5s;
}
.mobile-menu.active {
	transform: scaleY(1);
}

@media (max-width: 991px) {
    .header-area.header-2.sticky {
        height: 110px; 
    }
    
    .header-area.header-2.sticky-out {
        height: 110px;
    }


}

/* Enhanced glassmorphism sticky header */
/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(20px)) {
    .header-area.header-2.sticky,
    .header-area.header-2.sticky-out {
        background: rgba(5, 7, 9, 0.9) !important;
    }
}

/* Removed old bounceInUp animations - now using smooth fade transitions */

/* Adjust header height for desktop */
.header-area.header-2 {
    width: 100%;
    position: fixed;
    top: -70px; 
    left: 0;
    z-index: 100;
    opacity: 0;
    background: transparent;
    transition: opacity 0.25s ease-in-out, top 0.2s ease-in-out;
    box-shadow: 0 0 30px rgba(135, 80, 247, 0.2);
}



/* Keep mobile height as is */
@media (max-width: 991px) {
    .header-area.header-2.sticky,
    .header-area.header-2.sticky-out {
        height: 110px;
    }
}

/* Removed duplicate bounceInUp animations - now using smooth fade transitions */



/* Adjust header heights and positioning FOR DESKTOP ONLY */
@media (min-width: 992px) {
    .header-area.header-2.sticky,
    .header-area.header-2.sticky-out {
        height: 70px;
        padding-top: -50px;
    }
    
    /* Specific logo positioning for desktop sticky header */
    .header-area.header-2.sticky .logo-wrapper,
    .header-area.header-2.sticky-out .logo-wrapper {
        transform: translateY(-15px);
    }
    
    /* Position elements in the navbar for desktop */
    .header-area.header-2.sticky .nav,
    .header-area.header-2.sticky-out .nav,
    .header-area.header-2.sticky .btn-primary,
    .header-area.header-2.sticky-out .btn-primary {
        transform: translateY(-15px);
    }
    
    /* Add more specific class for logo adjustment */
    .logo.logo-sticky {
        margin-top: -15px;
    }
}

/* Keep mobile positioning as is - explicitly remove transforms */
@media (max-width: 991px) {
    .header-area.header-2.sticky .logo-wrapper,
    .header-area.header-2.sticky-out .logo-wrapper,
    .logo.logo-sticky {
        transform: none;
        margin-top: -0.5rem; 
    }
}

@media (min-width: 992px) {
    .header-area.header-2.sticky,
    .header-area.header-2.sticky-out {
        height: 90px;
    }
    
    .header-area.header-2.sticky .container > div,
    .header-area.header-2.sticky-out .container > div {
        transform: translateY(-15px);
    }

    .header-area.header-2.sticky .logo-wrapper,
    .header-area.header-2.sticky-out .logo-wrapper {
        transform: translateY(-25px);
    }

    .navbar-sticky {
        transform: translateY(-65px);
    }

    .header-area.header-2.sticky .btn-primary,
    .header-area.header-2.sticky-out .btn-primary {
        transform: translateY(-65px);
    }
}

@media (max-width: 991px) {
    .header-area.header-2.sticky .container > div,
    .header-area.header-2.sticky-out .container > div,
    .header-area.header-2.sticky .logo-wrapper,
    .header-area.header-2.sticky-out .logo-wrapper,
    .navbar-sticky {
        transform: none;
    }
}

/* Desktop header positioning */
@media (min-width: 992px) {
    /* Default state - elements lifted up */
    .header-area.header-2 .container > div {
        transform: translateY(-16px);
    }

    .header-area.header-2 .logo-wrapper {
        transform: translateY(-18px);
    }

    .header-area.header-2 .btn-primary {
        transform: translateY(-15px);
    }

    /* Sticky state adjustments */
    .header-area.header-2.sticky,
    .header-area.header-2.sticky-out {
        height: 79px;
    }
}

/* Keep mobile positioning as is */
@media (max-width: 991px) {
    .header-area.header-2 .container > div,
    .header-area.header-2 .logo-wrapper,
    .header-area.header-2 .btn-primary {
        transform: none;
    }
}

/* Keep mobile positioning with adjusted logo position */
@media (max-width: 991px) {
    .header-area.header-2.sticky,
    .header-area.header-2.sticky-out {
        height: 110px;
    }

    /* Adjust logo position for mobile */
    .header-area.header-2.sticky .logo-wrapper,
    .header-area.header-2.sticky-out .logo-wrapper {
        transform: translateY(10px); /* Move logo down on mobile */
        margin-top: 0; /* Reset any margin */
    }

    /* Keep other elements in place */
    .header-area.header-2.sticky .container > div,
    .header-area.header-2.sticky-out .container > div,
    .navbar-sticky {
        transform: none;
    }
}

/* hero css */
.intro_text svg {
	font-family: var(--russo);
	position: absolute;
	width: 100%;
	height: 100%;
	left: 46%;
	top: 55%;
	transform: translate(-50%, -50%) scale(1);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: -1;
	animation: 3s pulsate infinite alternate ease-in-out;
}
.intro_text svg text {
	text-transform: uppercase;
	/* animation: stroke 4s; */
	stroke-width: 1.2;
	stroke: var(--primary-color);
	fill: transparent;
	font-size: 240px;
	display: inline-block;
	opacity: 0;
}
.dark .intro_text svg text {
	stroke: var(--secondary-color);
}

.intro_text svg text.animate-stroke {
	animation: stroke 4s; /* Adjust the duration as needed */
	opacity: 0.2;
}
.dark .intro_text svg text.animate-stroke {
	opacity: 0.7;
}

@keyframes stroke {
	0% {
		stroke-dashoffset: 25%;
		stroke-dasharray: 0 50%;
		stroke-width: 1;
	}
	100% {
		stroke-dashoffset: 100%;
		stroke-dasharray: 100% 0;
		stroke-width: 1;
	}
}
@keyframes pulsate {
	0% {
		transform: translate(-50%, -50%) scale(1);
	}
	50% {
		transform: translate(-50%, -50%) scale(1.05);
	}
	100% {
		transform: translate(-50%, -50%) scale(1);
	}
}

/* sevices css */

@media (max-width: 575px) {
	.services-widget .service-item.current {
		background: linear-gradient(
			260deg,
			var(--secondary-color) 0%,
			var(--primary-color) 100%
		);
	}
}

.service-item.current .service-sl-num {
	color: var(--white-color);
}
.service-item .flaticon-up-right-arrow {
	transform: translateY(-50%) rotate(90deg);
}
.service-item:hover .flaticon-up-right-arrow {
	transform: translateY(-50%) rotate(0deg);
}

.service-item a span {
	transition: all 0.6s;
}
.service-item.current a span {
	color: var(--white-color);
}
.service-item.current .flaticon-up-right-arrow {
	color: var(--white-color);
	transform: translateY(-50%) rotate(0deg);
}
.services-widget .active-bg {
	top: 0px;
	bottom: 0px;
	left: 0px;
	right: 0px;
	position: absolute;
	z-index: 1;
	background: var(--primary-color);
	background: -o-linear-gradient(
		190deg,
		var(--secondary-color) 0%,
		var(--primary-color) 100%
	);
	background: linear-gradient(
		260deg,
		var(--secondary-color) 0%,
		var(--primary-color) 100%
	);
	-webkit-transition: all 0.5s ease;
	-o-transition: all 0.5s ease;
	transition: all 0.5s ease;
}

/* portfolio css */
.portfolio-box .portfolio-sizer {
	width: 48%;
}
.portfolio-box .gutter-sizer {
	width: 4%;
}

.portfolio-box .portfolio-item {
	margin-bottom: 4%;
	width: 48%;
}
.portfolio-box:before {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 35%;
	height: 35%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	border-radius: 50%;
	background: var(--primary-color);
	background: -o-linear-gradient(
		190deg,
		var(--primary-color) 0%,
		rgba(115, 67, 210, 0) 100%
	);
	background: linear-gradient(
		260deg,
		var(--primary-color) 0%,
		rgba(115, 67, 210, 0) 100%
	);
	-webkit-filter: blur(150px);
	filter: blur(150px);
}
.button-group .active-bg {
	border-radius: 50px;
	top: 0px;
	bottom: 0px;
	left: 0px;
	right: 0px;
	position: absolute;
	z-index: 1;
	background: var(--primary-color);
	background: -o-linear-gradient(
		190deg,
		var(--secondary-color) 0%,
		var(--primary-color) 100%
	);
	background: linear-gradient(
		260deg,
		var (--secondary-color) 0%,
		var(--primary-color) 100%
	);
	-webkit-transition: all 0.5s ease;
	-o-transition: all 0.5s ease;
	transition: all 0.5s ease;
}
.button-group button {
	transition: all 0.6s;
}
.button-group button.active {
	color: var(--white-color);
}
@media only screen and (max-width: 767px) {
	.portfolio-box .portfolio-sizer {
		width: 100%;
	}
	.portfolio-box .portfolio-item {
		width: 100%;
		margin-bottom: 30px;
	}
}
/* testimonials css */
.swiper:not(.roll-marquee) {
	padding-bottom: 60px;
}
.brand-slider.swiper {
	padding-bottom: 0;
}
.swiper-pagination {
	--swiper-pagination-top: auto;
}

.swiper-pagination .swiper-pagination-bullet {
	background: var(--gray-color);

	opacity: 0.9;
}
.dark .swiper-pagination .swiper-pagination-bullet {
	opacity: 0.2;
}
.swiper-pagination .swiper-pagination-bullet-active {
	background: var(--primary-color);
	opacity: 1;
}
.dark .swiper-pagination .swiper-pagination-bullet-active {
	background: var(--primary-color);
	opacity: 1;
}
.testimonials-slider .swiper-slide-active svg:first-child,
.testimonials-slider .swiper-slide-next svg:first-child {
	-webkit-transform: rotate(-45deg);
	-ms-transform: rotate(-45deg);
	transform: rotate(-45deg);
}
.testimonials-slider .swiper-slide-active svg:last-child,
.testimonials-slider .swiper-slide-next svg:last-child {
	-webkit-transform: translateX(-22px) rotate(135deg);
	-ms-transform: translateX(-22px) rotate(135deg);
	transform: translateX(-22px) rotate(135deg);
}

/* nice select css */
.tj-nice-select {
	float: none;
	width: 100%;
	background: transparent;
	border: 1px solid rgba(255, 255, 255, 0.1);
	color: var(--gray-color2);
}
.tj-nice-select:hover {
	border-color: rgba(255, 255, 255, 0.1);
}
.tj-nice-select:focus {
	-webkit-box-shadow: none;
	box-shadow: none;
	border-color: var(--primary-color);
}
.tj-nice-select::after {
	content: "";
	top: 50%;
	right: 10px;
	position: absolute;
	background: url(/img/icons/down-arrow-light.svg);
	background-position: center;
	background-size: cover;
	color: var(--primary-color-light);
	border: none;
	-webkit-transform: rotate(0);
	-ms-transform: rotate(0);
	transform: rotate(0);
	width: 15px;
	height: 12px;
	margin-top: -6px;
	-webkit-transform-origin: center;
	-ms-transform-origin: center;
	transform-origin: center;
}
.dark .tj-nice-select::after {
	background: url(/img/icons/down-arrow.svg);
	background-position: center;
	background-size: cover;
}
.tj-nice-select.open::after {
	-webkit-transform: rotate(-180deg);
	-ms-transform: rotate(-180deg);
	transform: rotate(-180deg);
}
.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus,
.nice-select .option {
	color: var(--accent-color);
}
.form_group select,
.form_group .nice-select,
.form_group
	input:not([type="submit"]):not([type="radio"]):not([type="checkbox"]),
.form_group textarea {
	display: block;
	width: 100%;
	background: var(--cream-light-color);
	border: 1px solid var(--gray-color3);
	font-size: 16px;
	line-height: 1;
	color: var(--secondary-color);
	padding: 14px 20px;
	border-radius: 8px;
	transition: all 0.3s 0s ease-out;
	outline: none;
	height: auto;
}
.dark .form_group select,
.dark .form_group .nice-select,
.dark
	.form_group
	input:not([type="submit"]):not([type="radio"]):not([type="checkbox"]),
.dark .form_group textarea {
	background: var(--black-color);
	color: var (--white-color);
}
.form_group .nice-select {
	padding: 17px 20px;
}

/* preloader css */
body.loaded {
	overflow: hidden !important;
	height: 100% !important;
}

.preloader {
	position: fixed;
	z-index: 10;
	height: 100vh;
	width: 100%;
	left: 0;
	top: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	overflow: hidden;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	background: transparent;
	z-index: 99999999999999;
}

.preloader svg {
	position: absolute;
	top: 0;
	width: 100vw;
	height: 110vh;
	fill: var(--black-color);
}

.preloader .preloader-heading .load-text {
	font-size: 20px;
	font-weight: 200;
	letter-spacing: 15px;
	text-transform: uppercase;
	z-index: 20;
}

.load-text span {
	-webkit-animation: loading 1s infinite alternate;
	animation: loading 1s infinite alternate;
}

.load-text span:nth-child(1) {
	-webkit-animation-delay: 0s;
	animation-delay: 0s;
}

.load-text span:nth-child(2) {
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}

.load-text span:nth-child(3) {
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}

.load-text span:nth-child(4) {
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}

.load-text span:nth-child(5) {
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}

.load-text span:nth-child(6) {
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}

.load-text span:nth-child(7) {
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}

@-webkit-keyframes loading {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}

@keyframes loading {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}

/* portfolio css */
.portfolio-slider {
	padding-bottom: 50px;
}
.portfolio-slider .swiper-pagination .swiper-pagination-bullet {
	background: var(--primary-color);
	width: 10px;
	height: 10px;
	transition: all 0.5s;
	opacity: 0.5;
}
.portfolio-slider .swiper-pagination .swiper-pagination-bullet-active,
.portfolio-slider .swiper-pagination .swiper-pagination-bullet:hover {
	background: var(--primary-color);
	width: 30px;
	height: 10px;
	border-radius: 20px;
	opacity: 100;
}
/* blog css  */
.blog-gallery-slider .swiper-button-prev,
.blog-gallery-slider .swiper-button-next {
	position: relative;
	width: 45px;
	height: 45px;
	font-size: 18px;
	line-height: 1;
	color: var(--white-color);
	padding: 0;
	border-radius: 50%;
	background-color: transparent;
	position: absolute;
	left: 15px;
	top: auto;
	bottom: 50%;
	transform: translateY(50%);
	z-index: 2;
	background-color: var(--primary-color);
	opacity: 0.7;
	transition: all 0.4s ease-in-out 0s;
	border-radius: 50%;
}
.blog-gallery-slider .swiper-button-prev::after,
.blog-gallery-slider .swiper-button-next::after {
	content: "";
	position: absolute;
	font-family: var(--font-family);
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);

	z-index: -1;
	display: block;
	font-weight: 300;
	font-size: 18px;
}
.blog-gallery-slider .swiper-button-prev::after {
	content: "\f060";
}
.blog-gallery-slider .swiper-button-next::after {
	content: "\f061";
}
.blog-gallery-slider .swiper-button-next:hover,
.blog-gallery-slider .swiper-button-prev:hover {
	opacity: 1;
}

.blog-gallery-slider .swiper-button-next {
	left: auto;
	right: 15px;
}

/* paginations css */
.paginations li.active > a {
	background: var(--primary-color);
	color: var(--white-color);
}
/* blockquote::before {
  content: "\f10e";
} */
/* sidebar css */
.sidebar-categories li.active a {
	background: var(--primary-color);
	color: var(--white-color);
}
.sidebar-categories li.active a i {
	color: var(--white-color);
}

.blog-gallery-slider {
	padding-bottom: 0;
}
/* odometer */

.odometer span {
	line-height: 1.4;
}

/* role marquee */

.roll-marqueer:after {
	animation-direction: reverse;
}
.roll-marquee .swiper-wrapper {
	-webkit-transition-timing-function: linear !important;
	transition-timing-function: linear !important;
	position: relative;
}
.roll-marquee:hover {
	animation-play-state: paused !important;
}
.roll-marquee .swiper-slide {
	text-align: center;

	display: flex;
	justify-content: center;
	align-items: center;
	width: auto;
	position: relative;
	overflow: hidden;
}
/* accordion */
.accordion-single:has(.open) {
	background-color: var(--primary-color);
	border-color: var(--primary-color);
}
.dark .accordion-single:has(.open) {
	background-color: var(--secondary-color);
	border-color: var(--secondary-color);
}

.accordion-btn {
	position: relative;
}
.accordion-btn.open {
	color: var(--white-color);
}
.accordion-btn:before {
	content: "\2b";
	position: absolute;
	top: 50%;
	right: 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	transform: translateY(-50%);
	font-family: "Font Awesome 6 Pro";
	font-size: 20px;
	color: var(--primary-color);
	font-weight: var(--tj-fw-regular);
	line-height: 1;

	transition: all 0.3s ease-in-out 0s;
}
.dark .accordion-btn:before {
	color: var(--white-color);
}
@media screen and (min-width: 768px) {
	.accordion-btn:before {
		right: 30px;
	}
}
.accordion-btn.open:before {
	content: "\f068";
	color: var (--white-color);
}

.accordion-content {
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.3s ease-out, padding 0.3s ease-out;
	padding: 0;
}

/* animated text */
#anim span {
	position: relative;
	transition: all 0.3s ease;
	display: inline-block;
	animation: wave-1 2.4s ease infinite;
	transform-origin: 100% 50%;
	transform-style: preserve-3d;
}
#anim span:nth-child(1) {
	animation-delay: 0s;
}
#anim span:nth-child(2) {
	animation-delay: 0.05s;
}
#anim span:nth-child(3) {
	animation-delay: 0.1s;
}
#anim span:nth-child(4) {
	animation-delay: 0.15s;
}
#anim span:nth-child(5) {
	animation-delay: 0.2s;
}
#anim span:nth-child(6) {
	animation-delay: 0.25s;
}
#anim span:nth-child(7) {
	animation-delay: 0.3s;
}
#anim span:nth-child(8) {
	animation-delay: 0.35s;
}
#anim span:nth-child(9) {
	animation-delay: 0.4s;
}
#anim span:nth-child(10) {
	animation-delay: 0.45s;
}
#anim span:nth-child(11) {
	animation-delay: 0.5s;
}
#anim span:nth-child(12) {
	animation-delay: 0.55s;
}
#anim span:nth-child(13) {
	animation-delay: 0.6s;
}
#anim span:nth-child(14) {
	animation-delay: 0.65s;
}
#anim span:nth-child(15) {
	animation-delay: 0.7s;
}
#anim span:nth-child(16) {
	animation-delay: 0.75s;
}
#anim span:nth-child(17) {
	animation-delay: 0.8s;
}
#anim span:nth-child(18) {
	animation-delay: 0.85s;
}
#anim span:nth-child(19) {
	animation-delay: 0.9s;
}
#anim span:nth-child(20) {
	animation-delay: 0.95s;
}
#anim span:nth-child(21) {
	animation-delay: 1s;
}
#anim span:nth-child(22) {
	animation-delay: 1.05s;
}
#anim span:nth-child(23) {
	animation-delay: 1.15s;
}

@keyframes wave-1 {
	0% {
		transform: translate3D(0, 0, 0) scale(1) rotateY(0);
	}
	12% {
		transform: translate3D(2px, -2px, 2px) scale(1.16) rotateY(6deg);
	}

	24% {
		transform: translate3D(0, 0, 0) scale(1) rotateY(0);
	}
	36% {
		transform: translate3D(0, 0, 0) scale(1);
	}
	100% {
		transform: scale(1);
	}
}
/* funfact */
.odometer-inside {
	display: inline-flex;
	align-items: center;
}

/* Video player styles */
input[type="range"] {
  -webkit-appearance: none;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  background-size: 0% 100%;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
}

.progress-bar:hover .progress-handle {
  transform: scale(1);
}

/* Make select placeholder lighter */
.tj-nice-select option[disabled] {
  color: #bcbcbc !important;
}
.nice-select .current:empty,
.nice-select .current {
  color: #bcbcbc !important;
}

/* Spotlight Animation */
@keyframes spotlight {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

.animate-spotlight {
  animation: spotlight 4s ease-in-out infinite;
}

/* Loader styles */
.loader {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #8750f7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth Glassmorphism Header Transitions */
@keyframes headerSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-16px) scale(0.97);
    filter: blur(3px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

@keyframes headerSlideOut {
  0% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
  100% {
    opacity: 0;
    transform: translateY(-16px) scale(0.97);
    filter: blur(3px);
  }
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

.header-animate-in {
  animation: headerSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.header-animate-out {
  animation: headerSlideOut 0.15s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.content-animate-in {
  animation: contentFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Enhanced smooth transition for glassmorphism */
.glassmorphism-spring {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hire Us Button Animations - Breathing + Rotating Border */
.hire-us-button {
  position: relative;
  animation: breathe 2.5s ease-in-out infinite;
  background: linear-gradient(45deg, #8750f7, #a855f7, #9333ea, #8750f7);
  background-size: 400% 400%;
  animation: breathe 2.5s ease-in-out infinite, gradientShift 4s ease-in-out infinite;
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 2px 8px rgba(135, 80, 247, 0.2);
}

.hire-us-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #8750f7, #a855f7, #9333ea, #7c3aed, #8750f7);
  border-radius: inherit;
  z-index: -1;
  animation: rotateBorder 3s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hire-us-button:hover::before {
  opacity: 1;
}

.hire-us-button:hover {
  transform: scale(1.08);
  animation: breatheHover 1.5s ease-in-out infinite, gradientShift 2s ease-in-out infinite;
  box-shadow: 0 4px 30px rgba(135, 80, 247, 0.3);
}

.hire-us-button:active {
  transform: scale(0.95);
  animation: none;
  transition: transform 0.1s ease-out;
}

.hire-us-button span {
  position: relative;
  z-index: 10;
  display: inline-block;
  animation: textFloat 3s ease-in-out infinite;
}

@keyframes breathe {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 15px rgba(135, 80, 247, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 3px 10px rgba(135, 80, 247, 0.25);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(135, 80, 247, 0.2);
  }
}

@keyframes breatheHover {
  0% {
    transform: scale(1.08);
    box-shadow: 0 4px 12px rgba(135, 80, 247, 0.3);
  }
  50% {
    transform: scale(1.12);
    box-shadow: 0 5px 15px rgba(135, 80, 247, 0.35);
  }
  100% {
    transform: scale(1.08);
    box-shadow: 0 4px 12px rgba(135, 80, 247, 0.3);
  }
}

@keyframes rotateBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes textFloat {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-1px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Portfolio Card Glassmorphism Effect - Now using inline styles */

/* Blog Card Glassmorphism Hover Effect */
.blog-glassmorphism-hover:hover {
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}
