"use client";
import Socials4 from "@/components/shared/socials/Socials4";
import { useHeaderContext } from "@/context_api/HeaderContext";
import stickyHeader from "@/libs/stickyHeader";
import { GlassmorphismHeader } from "@/components/ui/glassmorphism-header";
import Link from "next/link";
import { useEffect, useState } from "react";
import Logo from "./Logo";
import MobileMenu from "./MobileMenu";
import Navbar from "./Navbar";
import getNavItems from "@/libs/getNavItems";

const Header = ({ isSticky }) => {
    const [isActiveMobileMenu, setIsActiveMobileMenu] = useState(false);
    const { isInnerPage, headerType } = useHeaderContext();
    
    const HeaderContent = () => (
        <div className="py-0 relative">
            <div className="container">
                <div className="flex flex-wrap justify-between items-center min-h-[95px]">
                    {/* <!-- logo and contact email --> */}
                    <div className="flex items-center h-[95px]">
                        <ul className="flex items-center gap-x-15px xl:gap-x-35px">
                            <li className="flex items-center pt-[30px]">
                                <Logo isSticky={true} />
                            </li>
                        </ul>
                    </div>
                    {/* <!-- compact main menu --> */}
                    <nav className="flex items-center h-[95px]">
                        <ul className="nav flex items-center gap-x-5 xl:gap-x-30px 2xl:gap-x-45px">
                            {getNavItems()?.length
                                ? getNavItems()?.map(({ name, path, path2 }, idx) => (
                                        <li key={idx} className="nav_item group relative hidden lg:block">
                                            <Link
                                                href={isInnerPage ? path2 : path}
                                                className="text-size-15 font-medium text-white-color capitalize py-0 relative z-0 after:w-0 after:h-0.5 after:bg-gradient-primary after:absolute after:right-0 hover:after:left-0 after:bottom-[2px] after:transition-all after:duration-500 group-hover:after:w-full"
                                            >
                                                {name}
                                            </Link>
                                        </li>
                                ))
                                : ""}
                            
                            {/* <!-- compact action button --> */}
                            {headerType !== 3 && (
                                <li>
                                    <Link
                                        href={isInnerPage ? "/#contact" : "#contact"}
                                        className="hire-us-button bg-gradient-primary text-white-color text-size-15 font-medium px-6 py-3 rounded-full hover:bg-gradient-primary-hover transition-all duration-300 relative overflow-hidden"
                                    >
                                        <span className="relative z-10">Hire Us!</span>
                                    </Link>
                                </li>
                            )}
                            
                            {/* <!-- mobile menu button --> */}
                            <li className="menu-bar lg:hidden z-[999] relative flex items-center">
                                <div className="menu-bar">
                                    <button
                                        className={isActiveMobileMenu ? `active z-[999]` : "z-[999]"}
                                        onClick={() => setIsActiveMobileMenu(!isActiveMobileMenu)}
                                        aria-label="Toggle Mobile Menu"
                                    >
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </button>
                                </div>
                            </li>
                        </ul>
                    </nav>
                    {/* <!-- social button --> */}
                    {headerType === 3 ? (
                        <div className="hidden lg:block flex items-center h-[95px]">
                            <Socials4 />
                        </div>
                    ) : (
                        ""
                    )}
                </div>
            </div>
            {/* <!-- mobile menu --> */}
            <MobileMenu 
                isActiveMobileMenu={isActiveMobileMenu} 
                setIsActiveMobileMenu={setIsActiveMobileMenu}
            />
        </div>
    );

    return (
        <>
            {/* Original Header */}
            <header
                className={`header-area -mt-5 lg:mt-0 ${
                    isSticky ? "header-2 header-sticky" : "header-absolute"
                }`}
                style={{ top: isSticky ? "0" : "-7px" }}
            >
                <div
                    className={`${
                        isSticky 
                            ? "py-2" 
                            : "pt-2 xl:pt-4 pb-3 md:pb-3 xl:pb-4"
                    } relative`}
                >
                    <div className="container">
                        <div className="flex flex-wrap justify-between items-center -mt-1 lg:mt-0">
                            {/* <!-- logo and contact email --> */}
                            <div className="-mt-1 lg:mt-0">
                                <ul className="flex items-center gap-x-15px xl:gap-x-35px">
                                    <li>
                                        <Logo isSticky={isSticky} />
                                    </li>
                                </ul>
                            </div>
                            {/* <!-- main menu --> */}
                            <Navbar
                                isActiveMobileMenu={isActiveMobileMenu}
                                setIsActiveMobileMenu={setIsActiveMobileMenu}
                                isSticky={isSticky}
                            />
                            {/* <!-- social button --> */}
                            {headerType === 3 ? (
                                <div className="hidden lg:block">
                                    <Socials4 />
                                </div>
                            ) : (
                                ""
                            )}
                        </div>
                    </div>
                    {/* <!-- mobile menu --> */}
                    <MobileMenu 
                        isActiveMobileMenu={isActiveMobileMenu} 
                        setIsActiveMobileMenu={setIsActiveMobileMenu}
                    />
                </div>
            </header>

            {/* Glassmorphism Sticky Header */}
            <GlassmorphismHeader threshold={100}>
                <HeaderContent />
            </GlassmorphismHeader>
        </>
    );
};

export default Header;