"use client";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

const PortfolioCard = ({ portfolio }) => {
    const themeMode = useSearchParams()?.get("theme_mode");
    const { title, img, shortDesc, id, dataFilter } = portfolio ? portfolio : {};
    const isLight = themeMode === "light" ? true : false;
    return (
        <div
            className={`portfolio-item ${dataFilter} bg-primary-color-light px-15px pt-25px pb-0 lg:p-9 lg:pb-0 rounded-10px group relative float-left inline-flex`}
        >
            <Image src={img} alt="" width={2000} height={2000} />
            
            {/* Мобильная версия - показывается только на маленьких экранах */}
            <div 
                className="absolute left-0 bottom-0 right-0 group-hover:bottom-0 translate-y-full group-hover:translate-y-0 opacity-0 invisible group-hover:opacity-100 group-hover:visible w-full transition-all duration-300 px-[5%] md:hidden"
                style={{
                    background: 'rgba(0, 0, 0, 0.4)',
                    backdropFilter: 'blur(20px) saturate(180%)',
                    WebkitBackdropFilter: 'blur(20px) saturate(180%)'
                }}
            >
                <Link
                    href={`./portfolio/${id}${isLight ? "?theme_mode=light" : ""}`}
                    className="text-white-color px-6px py-[11px] pr-10px rounded-t-15px w-full block focus:outline-none transition-all duration-300"
                >
                    <span className="block text-sm font-bold mb-1">
                        {title}
                    </span>

                    <span className="block text-gray-200 text-xs line-clamp-1 truncate">
                        {shortDesc}
                    </span>
                </Link>
            </div>
            
            {/* ПК версия - показывается только на средних и больших экранах */}
            <div 
                className="hidden md:block absolute left-0 bottom-[5px] group-hover:bottom-5 translate-y-5 group-hover:translate-y-0 opacity-0 invisible group-hover:opacity-100 group-hover:visible w-full px-15px lg:px-5 transition-all duration-300"
                style={{
                    background: 'rgba(0, 0, 0, 0.4)',
                    backdropFilter: 'blur(20px) saturate(180%)',
                    WebkitBackdropFilter: 'blur(20px) saturate(180%)'
                }}
            >
                <Link
                    href={`./portfolio/${id}${isLight ? "?theme_mode=light" : ""}`}
                    className="text-white-color px-10px py-[15px] lg:px-3 lg:py-[17px] rounded-15px w-full relative border border-white/20 shadow-xl transition-all duration-300"
                >
                    <span className="block text-base md:text-lg lg:text-xl font-bold mb-1 lg:mb-2">
                        {title}
                    </span>

                    <span className="block text-gray-200 text-sm">{shortDesc}</span>
                </Link>
            </div>
        </div>
    );
};

export default PortfolioCard;
