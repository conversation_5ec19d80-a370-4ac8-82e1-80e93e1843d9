<!DOCTYPE html>
<html class="no-js" lang="">

<head>
  <title>Gerold - Personal Portfolio React NextJs Template</title>
  <meta charset="utf-8" />
  <meta http-equiv="x-ua-compatible" content="ie=edge" />
  <meta name="robots" content="noindex, follow" />
  <meta name="description" content="" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

  <!-- Place favicon.png in the root directory -->
  <link rel="shortcut icon" href="./img/favicon.ico" type="image/x-icon" />

  <!-- Font Icons css -->
  <link rel="stylesheet" href="css/font-icons.css" />
  <!-- plugins css -->
  <link rel="stylesheet" href="css/plugins.css" />
  <!-- Default Stylesheet -->
  <link rel="stylesheet" href="css/default.css" />
  <!-- Main Stylesheet -->
  <link rel="stylesheet" href="css/style.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.0.0/magnific-popup.min.css" />

  <!-- Modernizr JS -->
  <script src="js/vendor/modernizr-2.8.3.min.js"></script>
</head>

<body>
  <!--[if lte IE 9]>
      <p class="browserupgrade">
        You are using an <strong>outdated</strong> browser. Please
        <a href="https://browsehappy.com/">upgrade your browser</a> to improve
        your experience and security.
      </p>
    <![endif]-->

  <!-- Add your site or application content here -->

  <!-- Body main wrapper start -->
  <div class="body-wrapper" id="page-top">
    <div class="ltn__doc-header-area section-bg-1 d-block d-lg-none">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="ltn__doc-header-inner">
              <div class="site-logo">
                <a href="index.html"><img src="img/logo.png" alt="Logo" /></a>
              </div>
              <!-- Mobile Menu Button -->
              <div class="mobile-menu-toggle menu-btn-white--- menu-btn-border--- d-xl-none--">
                <a href="#ltn__utilize-mobile-menu" class="ltn__utilize-toggle">
                  <svg viewBox="0 0 800 600">
                    <path d="M300,220 C300,220 520,220 540,220 C740,220 640,540 520,420 C440,340 300,200 300,200"
                      id="top"></path>
                    <path d="M300,320 L540,320" id="middle"></path>
                    <path d="M300,210 C300,210 520,210 540,210 C740,210 640,530 520,410 C440,330 300,190 300,190"
                      id="bottom" transform="translate(480, 320) scale(1, -1) translate(-480, -318) "></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Utilize Mobile Menu Start -->
    <div id="ltn__utilize-mobile-menu" class="ltn__utilize ltn__utilize-mobile-menu">
      <div class="ltn__utilize-menu-inner ltn__scrollbar">
        <div class="ltn__utilize-menu-head">
          <div class="site-logo">
            <a href="index.html"><img src="img/logo.png" alt="Logo" /></a>
          </div>
          <button class="ltn__utilize-close d-block d-lg-none">×</button>
        </div>
        <div class="ltn__utilize-menu">
          <ul>
            <li>
              <a class="page-scroll" href="#ltn_introduction">Introduction</a>
            </li>
            <li>
              <a href="#">Getting Started</a>
              <ul class="sub-menu">
                <li>
                  <a class="page-scroll" href="#ltn_requirements">Requirements</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_whats_included">What's Included</a>
                </li>
              </ul>
            </li>
            <li>
              <a class="page-scroll" href="#ltn_installation">React Installation</a>
            </li>
            <li>
              <a href="#">Settings</a>
              <ul class="sub-menu">
                <li>
                  <a class="page-scroll" href="#ltn_set_url">Site URL</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_basename_route">Basename and Route</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_site_title_and_favicon">Site Title and Favicon</a>
                </li>
                <li><a class="page-scroll" href="#ltn_set_logo">Logo</a></li>
                <li><a class="page-scroll" href="#ltn_set_menu">Menu</a></li>
                <li>
                  <a class="page-scroll" href="#ltn_set_footer">Footer</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_copyright">Copyright</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_home_page">Change Home Page</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_section_components">Section Components</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_page_banner">Page Banner</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_blog_components">Blog Components</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_blog_single">Blog Single</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_widgets">Widgets</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_social_networks">Social Networks</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_font_family">Font Family Change</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_color">Color Change</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_set_preloader">Preloader Remove</a>
                </li>
              </ul>
            </li>
            <li>
              <a href="#">Credit</a>
              <ul class="sub-menu">
                <li>
                  <a class="page-scroll" href="#ltn_credit_fonts">Credit (Fonts)</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_credit_css">Credit (CSS)</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_credit_js">Credit (Javascript)</a>
                </li>
                <li>
                  <a class="page-scroll" href="#ltn_credit_images">Credit (Images)</a>
                </li>
              </ul>
            </li>
            <li><a class="page-scroll" href="#ltn_support">Support</a></li>
            <li><a class="page-scroll" href="#ltn_thanks">Thanks</a></li>
          </ul>
        </div>
      </div>
    </div>
    <!-- Utilize Mobile Menu End -->

    <div class="ltn__utilize-overlay"></div>

    <div class="ltn__documentation-content-area">
      <!-- Document Section Start ( Introduction ) -->
      <div class="ltn__doc-section" id="ltn_introduction">
        <div class="ltn__doc-section-title-area mt-0">
          <h2 class="ltn__doc-section-title">Intoduction</h2>
        </div>
        <div class="ltn__doc-section-content">
          <h1>Gerold - Personal Portfolio React NextJs Template</h1>
          <hr />
          <div class="list-menu-1">
            <ul>
              <li>
                <span><strong>Created :</strong></span> December 31, 2024
              </li>
              <li>
                <span><strong>Last Update :</strong></span> February 11, 2025
              </li>
              <li>
                <span><strong>By :</strong></span> Theme Junction
              </li>
              <li>
                <span><strong>E-mail:</strong></span>
                <EMAIL>
              </li>
            </ul>
          </div>
          <p>
            Thank you for purchasing our template. <br />
            If you have any questions that are beyond the scope of this help
            file, <br />
            Please feel free to contact us with sending mail to
            <a href="mailto:<EMAIL>" class="ltn__secondary-color" target="_blank"><strong>
                <EMAIL> </strong></a>
          </p>
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document React Requirments Section Start ( React Requirements ) -->
      <div class="ltn__doc-section" id="ltn_requirements">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Requirements</h2>
        </div>
        <div class="ltn__doc-section-content">
          <p>
            There are system requirements in order to install and setup the
            "quarter" and its components properly. Make sure that you are
            running the <strong>node.js install npm install</strong>.
            <br />
            You can download Node.js from here:
            <strong><a href="https://nodejs.org/" class="text-decoration ltn__secondary-color" target="_blank">Node
                JS</a></strong>. <strong>NPM</strong> comes bundled with
            <strong>Node.js</strong> Or
            <strong><a href="https://classic.yarnpkg.com/en/docs/install/#windows-stable"
                class="text-decoration ltn__secondary-color" target="_blank">yarn</a>
              install</strong>
          </p>

          <br />

          <h3>Recommended node and npm</h3>
          <div class="ltn__language-html">
            node install <br />
            npm install
          </div>
          <br />
          <br />

          <h3>You can use npm or yarn to Install Packages</h3>
          <p>
            <strong>NPM</strong> : You must have to
            <strong>npm install</strong> packages. You can do this by running
            npm install from the root of your project to install all the
            necessary dependencies.
          </p>
          <div class="ltn__language-html">
            <strong>npm install Or yarn add</strong>
          </div>
          <br />
          <br />

          <h3>Development server</h3>
          <p>
            Run npm run dev for a dev server. Navigate to
            http://localhost:3000/ . The app will automatically reload if you
            change any of the source files
          </p>
          <div class="ltn__language-html">
            <strong>npm run dev or yarn dev</strong>
          </div>
          <br />
          <br />

          <h3>Build</h3>
          <p>
            Run npm run build to build the project. The build artifacts will
            be stored in the build/ directory. Use the npm run build --prod
            flag for a production build.
          </p>
          <div class="ltn__language-html">
            <strong>npm run build or yarn build</strong>
          </div>
        </div>
      </div>
      <!-- Document React Requirments Section End -->

      <!-- Document Section Start ( Installation ) -->
      <div class="ltn__doc-section" id="ltn_whats_included">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">What's Included</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/others/folder-screenshot.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Installation ) -->
      <div class="ltn__doc-section" id="ltn_installation">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">
            How to start development server
          </h2>
        </div>
        <div class="ltn__doc-section-content">
          <p>
            Please follow the instructions of this video tutorial to see how
            you can install Packages and run the project
          </p>

          <div class="ltn__language-html">
            <ul>
              <li>
                For local host -
                <ol>
                  <li>Open your command terminal</li>
                  <li>npm install or yarn add</li>
                  <li>npm run dev or yarn dev</li>
                </ol>
              </li>
            </ul>
          </div>
          <!-- VIDEO AREA START -->
          <!-- <h3 class="ltn__secondary-color">Video Guide:</h3>
					<video width="100%" controls>
						<source src="media/1-installation.mp4" type="video/mp4">
					</video> -->
          <!-- VIDEO AREA END-->
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Installation ) -->
      <div class="ltn__doc-section" id="ltn_set_site_folders">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Folter Structure at a glance</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/others/folders.png" alt="#" />
        </div>
      </div>
      <!-- Document Section Start ( Installation ) -->
      <div class="ltn__doc-section" id="ltn_set_site_title_and_favicon">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">
            Change Site Title and Favicon
          </h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/3-meta-title-favicon.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Settings - Logo ) -->
      <div class="ltn__doc-section" id="ltn_set_logo">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Change Logo</h2>
        </div>
        <div class="ltn__doc-section-content">
          <p>
            Replace your logo on <code>theme_folder/img/logo.png</code> as the
            same name.
          </p>
          <img src="img/settings/4-logo.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Menu ) -->
      <div class="ltn__doc-section" id="ltn_set_menu">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Customize Menu</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/5-menu.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Footer ) -->
      <div class="ltn__doc-section" id="ltn_set_footer">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Footer</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/6-footer.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Settings - Copyright ) -->
      <div class="ltn__doc-section" id="ltn_set_copyright">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">
            Change Copyright Infomartaion
          </h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/7-copyright.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section End -->

      <!-- Document Section Start ( Section Components ) -->
      <div class="ltn__doc-section" id="ltn_set_section_components">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Section Components</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/8-section-components.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Page Banner ) -->
      <div class="ltn__doc-section" id="ltn_set_page_banner">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Bread Crumbs</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/9-page-banner.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Blog Components ) -->
      <div class="ltn__doc-section" id="ltn_set_blog_components">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Blog Components</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/10-blog-components.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Blog Single ) -->
      <div class="ltn__doc-section" id="ltn_set_blog_single">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Blog Single</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/11-blog-single.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Widgets ) -->
      <div class="ltn__doc-section" id="ltn_set_widgets">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Widgets</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/12-widgets.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Social Networks ) -->
      <div class="ltn__doc-section" id="ltn_set_social_networks">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Social Networks</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/13-social-networks.png" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Settings - Google Font ) -->
      <div class="ltn__doc-section" id="ltn_set_font_family">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Typography - Font Family</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/14-font-family-scss-1.jpg" alt="#" />
          <img src="img/settings/15-font-family-scss-2.jpg" alt="#" />
          <img src="img/settings/17-font-family-css-2.jpg" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->
      <!-- Document Section Start ( Settings - color Change ) -->
      <div class="ltn__doc-section" id="ltn_set_color">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Typography - Color</h2>
        </div>
        <div class="ltn__doc-section-content">
          <img src="img/settings/18-color-scss-1.jpg" alt="#" />
          <img src="img/settings/19-color-css-1.jpg" alt="#" />
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Credit - Fonts ) -->
      <div class="ltn__doc-section" id="ltn_credit_fonts">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Fonts Credit</h2>
        </div>
        <div class="ltn__doc-section-content">
          <h5>Google Fonts</h5>
          <p>
            Font-family:
            <a href="https://fonts.google.com/specimen/Sora" target="_blank" class="ltn__secondary-color">Sora,
              sans-serif</a>
          </p>
          <hr />
          <h5>Icon Fonts</h5>
          <p>
            Font Awesome:
            <a href="https://fontawesome.com/" target="_blank" class="ltn__secondary-color">
              Font Awesome</a>
          </p>
        </div>
      </div>
      <!-- Document Section End -->
      <!-- Document Section Start ( Credit - CSS ) -->
      <div class="ltn__doc-section" id="ltn_credit_css">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">CSS Credit</h2>
        </div>
        <div class="ltn__doc-section-content">
          <h5>Flugins Used for CSS:</h5>
          <p>
            Tailwind CSS:
            <a href="https://tailwindcss.com/" target="_blank" class="ltn__secondary-color">Tailwind CSS</a>
            <br />
            Animate:
            <a href="https://animate.style/" target="_blank" class="ltn__secondary-color">Animate</a>
          </p>
        </div>
      </div>
      <!-- Document Section End -->
      <!-- Document Section Start ( Credit - JavaScript ) -->
      <div class="ltn__doc-section" id="ltn_credit_js">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">JavaScript Credit</h2>
        </div>
        <div class="ltn__doc-section-content">
          <h5>Flugins Used for JavaScript:</h5>
          <p>
            <br />
            React:
            <a href="https://react.dev/" target="_blank" class="ltn__secondary-color">React</a>
            <br />
            Next Js:
            <a href="https://nextjs.org/" target="_blank" class="ltn__secondary-color">Next Js</a>
            <br />
            Swiper:
            <a href="https://swiperjs.com/react" target="_blank" class="ltn__secondary-color">Swiper</a>
          </p>
        </div>
      </div>
      <!-- Document Section End -->
      <!-- Document Section Start ( Credit - Images ) -->
      <div class="ltn__doc-section" id="ltn_credit_images">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Images Credit</h2>
        </div>
        <div class="ltn__doc-section-content">
          <p>
            <strong>Images Used:</strong> All images we used in this template
            come from sources. They are very good sources for free images
          </p>
          <p>
            Pinterest:
            <a href="https://www.pinterest.com/" target="_blank" class="ltn__secondary-color">Pinterest</a>
            <br />
            Freepik:
            <a href="https://www.freepik.com/" target="_blank" class="ltn__secondary-color">Freepik</a>
            <br />
            Unsplash:
            <a href="https://unsplash.com/" target="_blank" class="ltn__secondary-color">Unsplash</a>
            <br />
            Pexels:
            <a href="https://www.pexels.com/" target="_blank" class="ltn__secondary-color">Pexels</a>
          </p>
        </div>
      </div>
      <!-- Document Section End -->

      <!-- Document Section Start ( Support ) -->
      <div class="ltn__doc-section" id="ltn_support">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Support</h2>
        </div>
        <div class="ltn__doc-section-content">
          <p>
            Thank you for purchasing our template. <br />
            If you have any questions that are beyond the scope of this help
            file, <br />
            <!-- please feel free to open a new ticket at our <a href="#" class="ltn__secondary-color" target="_blank"><strong>support forum</strong></a> -->
            Please feel free to contact us with sending mail to
            <a href="mailto:<EMAIL>" class="ltn__secondary-color"
              target="_blank"><strong><EMAIL></strong></a>
          </p>
          <div class="btn-wrapper">
            <a href="https://themeforest.net/user/theme-junction" class="btn theme-btn-1" target="_blank">24/7 Support:
              Send Us a message from our profile</a>
          </div>
        </div>
      </div>
      <!-- Document Section End -->
      <!-- Document Section Start ( Support ) -->
      <div class="ltn__doc-section" id="ltn_thanks">
        <div class="ltn__doc-section-title-area">
          <h2 class="ltn__doc-section-title">Thanks</h2>
        </div>
        <div class="ltn__doc-section-content">
          <h4>Once again thank you for purchasing one of our Templates</h4>
          <h5>Best Regards</h5>
          <h6>Theme Junction</h6>
          <hr />
          <p>
            Copyright
            <a href="https://themeforest.net/user/theme-junction" target="_blank" class="ltn__secondary-color">Theme
              Junction</a>
            made with the Documenter v1.0.0
          </p>
        </div>
      </div>
      <!-- Document Section End -->
    </div>
  </div>

  <!-- All JS Plugins -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.0.0/jquery.magnific-popup.min.js"></script>

  <script src="./js/plugins.js"></script>
  <!-- Magnific Popup core JS file -->

  <script>
    // magnific videos popup
    $(".video-popup").magnificPopup({
      disableOn: 315,
      type: "iframe",
      mainClass: "mfp-fade",
      removalDelay: 160,
      preloader: true,
      fixedContentPos: false,
    });
  </script>
  <!-- Main JS -->
  <script src="./js/main.js"></script>
</body>

</html>