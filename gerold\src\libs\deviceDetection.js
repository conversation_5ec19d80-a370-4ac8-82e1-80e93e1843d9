/**
 * Device Detection Utility
 * Detects Android devices and low-end devices that should use static images instead of 3D Spline
 */

export const deviceDetection = {
  /**
   * Check if device is Android
   */
  isAndroid() {
    if (typeof window === 'undefined') return false;
    return /Android/i.test(navigator.userAgent);
  },

  /**
   * Check if device is iOS
   */
  isIOS() {
    if (typeof window === 'undefined') return false;
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  },

  /**
   * Check if device is mobile
   */
  isMobile() {
    if (typeof window === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },

  /**
   * Check device memory (if available)
   */
  getDeviceMemory() {
    if (typeof window === 'undefined') return null;
    return navigator.deviceMemory || null;
  },

  /**
   * Check hardware concurrency (CPU cores)
   */
  getHardwareConcurrency() {
    if (typeof window === 'undefined') return null;
    return navigator.hardwareConcurrency || null;
  },

  /**
   * Check connection speed
   */
  getConnectionSpeed() {
    if (typeof window === 'undefined') return null;
    if ('connection' in navigator) {
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      return {
        effectiveType: connection?.effectiveType || null,
        downlink: connection?.downlink || null,
        rtt: connection?.rtt || null
      };
    }
    return null;
  },

  /**
   * Check if device is a modern iOS device that should support 3D content
   */
  isModernIOS() {
    if (!this.isIOS()) return false;
    
    const userAgent = navigator.userAgent;
    // Check for iPhone models that should support 3D content
    const modernIPhonePatterns = [
      /iPhone1[0-9]/, // iPhone 10 and above
      /iPhone[X]/, // iPhone X
      /OS 1[4-9]_/, // iOS 14 and above
      /OS [2-9][0-9]_/, // iOS 20+ (future proof)
    ];
    
    return modernIPhonePatterns.some(pattern => pattern.test(userAgent));
  },

  /**
   * Detect if device is low-end based on multiple factors
   */
  isLowEndDevice() {
    if (typeof window === 'undefined') return false;

    // Modern iOS devices should not be considered low-end
    if (this.isModernIOS()) return false;

    const memory = this.getDeviceMemory();
    const cores = this.getHardwareConcurrency();
    const connection = this.getConnectionSpeed();

    // Check memory constraints (less than 3GB for stricter filtering)
    if (memory && memory < 3) return true;

    // Check CPU cores (less than 4 cores, but exclude iOS devices)
    if (cores && cores < 4 && !this.isIOS()) return true;

    // Check slow connection
    if (connection?.effectiveType && ['slow-2g', '2g'].includes(connection.effectiveType)) {
      return true;
    }

    // Check for older Android versions (likely lower-end devices)
    const userAgent = navigator.userAgent;
    const androidMatch = userAgent.match(/Android (\d+)/);
    if (androidMatch && parseInt(androidMatch[1]) < 8) return true;

    // Check for specific low-end device indicators
    const lowEndIndicators = [
      /Android.*SM-J\d+/, // Samsung J series
      /Android.*SM-A0\d+/, // Samsung A0x series  
      /Android.*LG-K\d+/, // LG K series
      /Android.*HUAWEI Y\d+/, // Huawei Y series
      /Android.*Redmi \d+A/, // Xiaomi Redmi A series
    ];

    return lowEndIndicators.some(pattern => pattern.test(userAgent));
  },

  /**
   * Main function to determine if static images should be used
   */
  shouldUseStaticImages() {
    if (typeof window === 'undefined') return true; // SSR fallback

    // Modern iOS devices should always show 3D content
    if (this.isModernIOS()) return false;

    // Always use static for Android devices (for now)
    if (this.isAndroid()) return true;

    // Use static for low-end devices
    if (this.isLowEndDevice()) return true;

    // Use static for very slow connections
    const connection = this.getConnectionSpeed();
    if (connection?.effectiveType && ['slow-2g', '2g'].includes(connection.effectiveType)) {
      return true;
    }

    // Check screen size for very small devices (but not iOS)
    if (!this.isIOS() && (window.innerWidth < 360 || window.innerHeight < 600)) return true;

    return false;
  },

  /**
   * Get device info for debugging
   */
  getDeviceInfo() {
    return {
      isAndroid: this.isAndroid(),
      isIOS: this.isIOS(),
      isModernIOS: this.isModernIOS(),
      isMobile: this.isMobile(),
      isLowEnd: this.isLowEndDevice(),
      shouldUseStatic: this.shouldUseStaticImages(),
      memory: this.getDeviceMemory(),
      cores: this.getHardwareConcurrency(),
      connection: this.getConnectionSpeed(),
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : null,
      screen: typeof window !== 'undefined' ? {
        width: window.innerWidth,
        height: window.innerHeight
      } : null
    };
  }
};

export default deviceDetection; 