"use server";

import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { PKPass } from 'passkit-generator';

/**
 * API Route для генерации Apple Wallet Pass (.pkpass)
 */
export async function POST(request) {
  try {
    // Получаем данные пользователя из запроса
    const userData = await request.json();
    
    // Путь к временному файлу
    const tempDir = path.join(process.cwd(), 'temp');
    const tempFilePath = path.join(tempDir, `d2d_loyalty_${Date.now()}.pkpass`);
    
    // Убедимся, что директория существует
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Генерируем уникальный идентификатор для карты
    const uniqueId = crypto.randomUUID();
    
    try {
      // Пытаемся сгенерировать .pkpass с реальными сертификатами
      await generatePassKit(userData, tempFilePath, uniqueId);
    } catch (genError) {
      console.error('Error generating PassKit with real certificates:', genError);
      
      // Если не удалось сгенерировать с реальными сертификатами,
      // используем демо-файл для тестирования
      const demoFilePath = path.join(process.cwd(), 'certificates', 'demo_loyalty.pkpass');
      
      if (fs.existsSync(demoFilePath)) {
        // Копируем демо-файл во временный
        fs.copyFileSync(demoFilePath, tempFilePath);
      } else {
        // Если демо-файла нет, возвращаем ошибку
        return NextResponse.json(
          { error: 'Pass generation failed - certificate error and no demo file' }, 
          { status: 500 }
        );
      }
    }
    
    // Проверяем, был ли создан файл
    if (!fs.existsSync(tempFilePath)) {
      return NextResponse.json(
        { error: 'Pass generation failed - file creation error' }, 
        { status: 500 }
      );
    }
    
    // Читаем файл
    const passData = fs.readFileSync(tempFilePath);
    
    // Удаляем временный файл после чтения
    try {
      fs.unlinkSync(tempFilePath);
    } catch (unlinkError) {
      console.error('Error removing temporary file:', unlinkError);
    }
    
    // Отправляем файл как бинарные данные
    return new NextResponse(passData, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.apple.pkpass',
        'Content-Disposition': `attachment; filename="d2d_loyalty_${uniqueId}.pkpass"`
      }
    });
    
  } catch (error) {
    console.error('Error in generate-pass API:', error);
    return NextResponse.json({ error: 'Pass generation failed' }, { status: 500 });
  }
}

/**
 * Функция для генерации pkpass файла
 */
async function generatePassKit(userData, outputPath, uniqueId) {
  try {
    // Получаем путь к шаблону и сертификатам
    const passTemplatePath = path.join(process.cwd(), 'certificates', 'pass.json');
    const wwdrPath = path.join(process.cwd(), 'certificates', 'wwdr.pem');
    const signerCertPath = path.join(process.cwd(), 'certificates', 'signerCert.pem');
    const signerKeyPath = path.join(process.cwd(), 'certificates', 'signerKey.pem');
    
    // Проверяем наличие необходимых файлов
    if (!fs.existsSync(passTemplatePath)) {
      throw new Error('Pass template not found');
    }
    
    // Проверяем наличие сертификатов
    const hasCertificates = fs.existsSync(wwdrPath) && 
                           fs.existsSync(signerCertPath) && 
                           fs.existsSync(signerKeyPath);
    
    if (!hasCertificates) {
      throw new Error('Required certificates not found');
    }
    
    // Читаем шаблон карты
    const passTemplate = JSON.parse(fs.readFileSync(passTemplatePath, 'utf8'));
    
    // Создаем объект PKPass с нужными параметрами
    const pass = new PKPass({
      model: passTemplatePath,
      certificates: {
        wwdr: fs.readFileSync(wwdrPath),
        signerCert: fs.readFileSync(signerCertPath),
        signerKey: fs.readFileSync(signerKeyPath),
        signerKeyPassphrase: process.env.PASS_SIGNING_KEY || '' // пароль из переменных окружения
      }
    });
    
    // Обновляем данные карты на основе данных пользователя
    if (userData.name) {
      pass.secondaryFields.find(field => field.key === 'name').value = userData.name;
    }
    
    // Устанавливаем уникальный серийный номер
    pass.serialNumber = uniqueId;
    
    // Устанавливаем дату регистрации
    const currentYear = new Date().getFullYear();
    pass.auxiliaryFields.find(field => field.key === 'joindate').value = currentYear.toString();
    
    // Генерируем штрих-код на основе ID
    pass.barcodes[0].message = uniqueId;
    
    // Создаем .pkpass файл
    const passBuffer = await pass.getAsBuffer();
    
    // Записываем в файл
    fs.writeFileSync(outputPath, passBuffer);
    
    return true;
  } catch (error) {
    console.error('Error in generatePassKit:', error);
    throw error;
  }
} 