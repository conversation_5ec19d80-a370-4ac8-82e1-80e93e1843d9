const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/browserAll-Ct8GKbn5.js","assets/webworkerAll-C--Xr5LI.js","assets/colorToUniform-C2GHuDhf.js","assets/CanvasPool-b3qxBLGf.js","assets/WebGPURenderer-DwwCZVVK.js","assets/SharedSystems-oOBGbWCJ.js","assets/WebGLRenderer-FBNzJiI7.js"])))=>i.map(i=>d[i]);
var Ir=Object.defineProperty;var Br=(i,t,e)=>t in i?Ir(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var Y=(i,t,e)=>Br(i,typeof t!="symbol"?t+"":t,e);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function e(n){const r={};return n.integrity&&(r.integrity=n.integrity),n.referrerPolicy&&(r.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?r.credentials="include":n.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(n){if(n.ep)return;n.ep=!0;const r=e(n);fetch(n.href,r)}})();const Rr="modulepreload",Fr=function(i){return"/"+i},oi={},Ve=function(t,e,s){let n=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));n=Promise.allSettled(e.map(h=>{if(h=Fr(h),h in oi)return;oi[h]=!0;const c=h.endsWith(".css"),l=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${h}"]${l}`))return;const u=document.createElement("link");if(u.rel=c?"stylesheet":Rr,c||(u.as="script"),u.crossOrigin="",u.href=h,a&&u.setAttribute("nonce",a),document.head.appendChild(u),c)return new Promise((f,d)=>{u.addEventListener("load",f),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${h}`)))})}))}function r(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return n.then(o=>{for(const a of o||[])a.status==="rejected"&&r(a.reason);return t().catch(r)})};var rt=(i=>(i.Application="application",i.WebGLPipes="webgl-pipes",i.WebGLPipesAdaptor="webgl-pipes-adaptor",i.WebGLSystem="webgl-system",i.WebGPUPipes="webgpu-pipes",i.WebGPUPipesAdaptor="webgpu-pipes-adaptor",i.WebGPUSystem="webgpu-system",i.CanvasSystem="canvas-system",i.CanvasPipesAdaptor="canvas-pipes-adaptor",i.CanvasPipes="canvas-pipes",i.Asset="asset",i.LoadParser="load-parser",i.ResolveParser="resolve-parser",i.CacheParser="cache-parser",i.DetectionParser="detection-parser",i.MaskEffect="mask-effect",i.BlendMode="blend-mode",i.TextureSource="texture-source",i.Environment="environment",i.ShapeBuilder="shape-builder",i.Batcher="batcher",i))(rt||{});const As=i=>{if(typeof i=="function"||typeof i=="object"&&i.extension){if(!i.extension)throw new Error("Extension class must have an extension object");i={...typeof i.extension!="object"?{type:i.extension}:i.extension,ref:i}}if(typeof i=="object")i={...i};else throw new Error("Invalid extension type");return typeof i.type=="string"&&(i.type=[i.type]),i},ke=(i,t)=>As(i).priority??t,Ft={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...i){return i.map(As).forEach(t=>{t.type.forEach(e=>{var s,n;return(n=(s=this._removeHandlers)[e])==null?void 0:n.call(s,t)})}),this},add(...i){return i.map(As).forEach(t=>{t.type.forEach(e=>{var r,o;const s=this._addHandlers,n=this._queue;s[e]?(o=s[e])==null||o.call(s,t):(n[e]=n[e]||[],(r=n[e])==null||r.push(t))})}),this},handle(i,t,e){var o;const s=this._addHandlers,n=this._removeHandlers;if(s[i]||n[i])throw new Error(`Extension type ${i} already has a handler`);s[i]=t,n[i]=e;const r=this._queue;return r[i]&&((o=r[i])==null||o.forEach(a=>t(a)),delete r[i]),this},handleByMap(i,t){return this.handle(i,e=>{e.name&&(t[e.name]=e.ref)},e=>{e.name&&delete t[e.name]})},handleByNamedList(i,t,e=-1){return this.handle(i,s=>{t.findIndex(r=>r.name===s.name)>=0||(t.push({name:s.name,value:s.ref}),t.sort((r,o)=>ke(o.value,e)-ke(r.value,e)))},s=>{const n=t.findIndex(r=>r.name===s.name);n!==-1&&t.splice(n,1)})},handleByList(i,t,e=-1){return this.handle(i,s=>{t.includes(s.ref)||(t.push(s.ref),t.sort((n,r)=>ke(r,e)-ke(n,e)))},s=>{const n=t.indexOf(s.ref);n!==-1&&t.splice(n,1)})},mixin(i,...t){for(const e of t)Object.defineProperties(i.prototype,Object.getOwnPropertyDescriptors(e))}},Gr={extension:{type:rt.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await Ve(()=>import("./browserAll-Ct8GKbn5.js"),__vite__mapDeps([0,1,2,3]))}},Dr={extension:{type:rt.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await Ve(()=>import("./webworkerAll-C--Xr5LI.js"),__vite__mapDeps([1,2,3]))}};class Tt{constructor(t,e,s){this._x=e||0,this._y=s||0,this._observer=t}clone(t){return new Tt(t??this._observer,this._x,this._y)}set(t=0,e=t){return(this._x!==t||this._y!==e)&&(this._x=t,this._y=e,this._observer._onUpdate(this)),this}copyFrom(t){return(this._x!==t.x||this._y!==t.y)&&(this._x=t.x,this._y=t.y,this._observer._onUpdate(this)),this}copyTo(t){return t.set(this._x,this._y),t}equals(t){return t.x===this._x&&t.y===this._y}toString(){return`[pixi.js/math:ObservablePoint x=0 y=0 scope=${this._observer}]`}get x(){return this._x}set x(t){this._x!==t&&(this._x=t,this._observer._onUpdate(this))}get y(){return this._y}set y(t){this._y!==t&&(this._y=t,this._observer._onUpdate(this))}}function Ns(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Je={exports:{}},ai;function Hr(){return ai||(ai=1,function(i){var t=Object.prototype.hasOwnProperty,e="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(e=!1));function n(h,c,l){this.fn=h,this.context=c,this.once=l||!1}function r(h,c,l,u,f){if(typeof l!="function")throw new TypeError("The listener must be a function");var d=new n(l,u||h,f),x=e?e+c:c;return h._events[x]?h._events[x].fn?h._events[x]=[h._events[x],d]:h._events[x].push(d):(h._events[x]=d,h._eventsCount++),h}function o(h,c){--h._eventsCount===0?h._events=new s:delete h._events[c]}function a(){this._events=new s,this._eventsCount=0}a.prototype.eventNames=function(){var c=[],l,u;if(this._eventsCount===0)return c;for(u in l=this._events)t.call(l,u)&&c.push(e?u.slice(1):u);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(l)):c},a.prototype.listeners=function(c){var l=e?e+c:c,u=this._events[l];if(!u)return[];if(u.fn)return[u.fn];for(var f=0,d=u.length,x=new Array(d);f<d;f++)x[f]=u[f].fn;return x},a.prototype.listenerCount=function(c){var l=e?e+c:c,u=this._events[l];return u?u.fn?1:u.length:0},a.prototype.emit=function(c,l,u,f,d,x){var _=e?e+c:c;if(!this._events[_])return!1;var g=this._events[_],b=arguments.length,w,S;if(g.fn){switch(g.once&&this.removeListener(c,g.fn,void 0,!0),b){case 1:return g.fn.call(g.context),!0;case 2:return g.fn.call(g.context,l),!0;case 3:return g.fn.call(g.context,l,u),!0;case 4:return g.fn.call(g.context,l,u,f),!0;case 5:return g.fn.call(g.context,l,u,f,d),!0;case 6:return g.fn.call(g.context,l,u,f,d,x),!0}for(S=1,w=new Array(b-1);S<b;S++)w[S-1]=arguments[S];g.fn.apply(g.context,w)}else{var M=g.length,I;for(S=0;S<M;S++)switch(g[S].once&&this.removeListener(c,g[S].fn,void 0,!0),b){case 1:g[S].fn.call(g[S].context);break;case 2:g[S].fn.call(g[S].context,l);break;case 3:g[S].fn.call(g[S].context,l,u);break;case 4:g[S].fn.call(g[S].context,l,u,f);break;default:if(!w)for(I=1,w=new Array(b-1);I<b;I++)w[I-1]=arguments[I];g[S].fn.apply(g[S].context,w)}}return!0},a.prototype.on=function(c,l,u){return r(this,c,l,u,!1)},a.prototype.once=function(c,l,u){return r(this,c,l,u,!0)},a.prototype.removeListener=function(c,l,u,f){var d=e?e+c:c;if(!this._events[d])return this;if(!l)return o(this,d),this;var x=this._events[d];if(x.fn)x.fn===l&&(!f||x.once)&&(!u||x.context===u)&&o(this,d);else{for(var _=0,g=[],b=x.length;_<b;_++)(x[_].fn!==l||f&&!x[_].once||u&&x[_].context!==u)&&g.push(x[_]);g.length?this._events[d]=g.length===1?g[0]:g:o(this,d)}return this},a.prototype.removeAllListeners=function(c){var l;return c?(l=e?e+c:c,this._events[l]&&o(this,l)):(this._events=new s,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,i.exports=a}(Je)),Je.exports}var $r=Hr();const Nt=Ns($r),Ur=Math.PI*2,Lr=180/Math.PI,qr=Math.PI/180;class ct{constructor(t=0,e=0){this.x=0,this.y=0,this.x=t,this.y=e}clone(){return new ct(this.x,this.y)}copyFrom(t){return this.set(t.x,t.y),this}copyTo(t){return t.set(this.x,this.y),t}equals(t){return t.x===this.x&&t.y===this.y}set(t=0,e=t){return this.x=t,this.y=e,this}toString(){return`[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return ts.x=0,ts.y=0,ts}}const ts=new ct;class it{constructor(t=1,e=0,s=0,n=1,r=0,o=0){this.array=null,this.a=t,this.b=e,this.c=s,this.d=n,this.tx=r,this.ty=o}fromArray(t){this.a=t[0],this.b=t[1],this.c=t[3],this.d=t[4],this.tx=t[2],this.ty=t[5]}set(t,e,s,n,r,o){return this.a=t,this.b=e,this.c=s,this.d=n,this.tx=r,this.ty=o,this}toArray(t,e){this.array||(this.array=new Float32Array(9));const s=e||this.array;return t?(s[0]=this.a,s[1]=this.b,s[2]=0,s[3]=this.c,s[4]=this.d,s[5]=0,s[6]=this.tx,s[7]=this.ty,s[8]=1):(s[0]=this.a,s[1]=this.c,s[2]=this.tx,s[3]=this.b,s[4]=this.d,s[5]=this.ty,s[6]=0,s[7]=0,s[8]=1),s}apply(t,e){e=e||new ct;const s=t.x,n=t.y;return e.x=this.a*s+this.c*n+this.tx,e.y=this.b*s+this.d*n+this.ty,e}applyInverse(t,e){e=e||new ct;const s=this.a,n=this.b,r=this.c,o=this.d,a=this.tx,h=this.ty,c=1/(s*o+r*-n),l=t.x,u=t.y;return e.x=o*c*l+-r*c*u+(h*r-a*o)*c,e.y=s*c*u+-n*c*l+(-h*s+a*n)*c,e}translate(t,e){return this.tx+=t,this.ty+=e,this}scale(t,e){return this.a*=t,this.d*=e,this.c*=t,this.b*=e,this.tx*=t,this.ty*=e,this}rotate(t){const e=Math.cos(t),s=Math.sin(t),n=this.a,r=this.c,o=this.tx;return this.a=n*e-this.b*s,this.b=n*s+this.b*e,this.c=r*e-this.d*s,this.d=r*s+this.d*e,this.tx=o*e-this.ty*s,this.ty=o*s+this.ty*e,this}append(t){const e=this.a,s=this.b,n=this.c,r=this.d;return this.a=t.a*e+t.b*n,this.b=t.a*s+t.b*r,this.c=t.c*e+t.d*n,this.d=t.c*s+t.d*r,this.tx=t.tx*e+t.ty*n+this.tx,this.ty=t.tx*s+t.ty*r+this.ty,this}appendFrom(t,e){const s=t.a,n=t.b,r=t.c,o=t.d,a=t.tx,h=t.ty,c=e.a,l=e.b,u=e.c,f=e.d;return this.a=s*c+n*u,this.b=s*l+n*f,this.c=r*c+o*u,this.d=r*l+o*f,this.tx=a*c+h*u+e.tx,this.ty=a*l+h*f+e.ty,this}setTransform(t,e,s,n,r,o,a,h,c){return this.a=Math.cos(a+c)*r,this.b=Math.sin(a+c)*r,this.c=-Math.sin(a-h)*o,this.d=Math.cos(a-h)*o,this.tx=t-(s*this.a+n*this.c),this.ty=e-(s*this.b+n*this.d),this}prepend(t){const e=this.tx;if(t.a!==1||t.b!==0||t.c!==0||t.d!==1){const s=this.a,n=this.c;this.a=s*t.a+this.b*t.c,this.b=s*t.b+this.b*t.d,this.c=n*t.a+this.d*t.c,this.d=n*t.b+this.d*t.d}return this.tx=e*t.a+this.ty*t.c+t.tx,this.ty=e*t.b+this.ty*t.d+t.ty,this}decompose(t){const e=this.a,s=this.b,n=this.c,r=this.d,o=t.pivot,a=-Math.atan2(-n,r),h=Math.atan2(s,e),c=Math.abs(a+h);return c<1e-5||Math.abs(Ur-c)<1e-5?(t.rotation=h,t.skew.x=t.skew.y=0):(t.rotation=0,t.skew.x=a,t.skew.y=h),t.scale.x=Math.sqrt(e*e+s*s),t.scale.y=Math.sqrt(n*n+r*r),t.position.x=this.tx+(o.x*e+o.y*n),t.position.y=this.ty+(o.x*s+o.y*r),t}invert(){const t=this.a,e=this.b,s=this.c,n=this.d,r=this.tx,o=t*n-e*s;return this.a=n/o,this.b=-e/o,this.c=-s/o,this.d=t/o,this.tx=(s*this.ty-n*r)/o,this.ty=-(t*this.ty-e*r)/o,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const t=new it;return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyTo(t){return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyFrom(t){return this.a=t.a,this.b=t.b,this.c=t.c,this.d=t.d,this.tx=t.tx,this.ty=t.ty,this}equals(t){return t.a===this.a&&t.b===this.b&&t.c===this.c&&t.d===this.d&&t.tx===this.tx&&t.ty===this.ty}toString(){return`[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return Nr.identity()}static get shared(){return zr.identity()}}const zr=new it,Nr=new it,Qt=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],Jt=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],te=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],ee=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],Ps=[],gn=[],Ie=Math.sign;function Wr(){for(let i=0;i<16;i++){const t=[];Ps.push(t);for(let e=0;e<16;e++){const s=Ie(Qt[i]*Qt[e]+te[i]*Jt[e]),n=Ie(Jt[i]*Qt[e]+ee[i]*Jt[e]),r=Ie(Qt[i]*te[e]+te[i]*ee[e]),o=Ie(Jt[i]*te[e]+ee[i]*ee[e]);for(let a=0;a<16;a++)if(Qt[a]===s&&Jt[a]===n&&te[a]===r&&ee[a]===o){t.push(a);break}}}for(let i=0;i<16;i++){const t=new it;t.set(Qt[i],Jt[i],te[i],ee[i],0,0),gn.push(t)}}Wr();const ft={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:i=>Qt[i],uY:i=>Jt[i],vX:i=>te[i],vY:i=>ee[i],inv:i=>i&8?i&15:-i&7,add:(i,t)=>Ps[i][t],sub:(i,t)=>Ps[i][ft.inv(t)],rotate180:i=>i^4,isVertical:i=>(i&3)===2,byDirection:(i,t)=>Math.abs(i)*2<=Math.abs(t)?t>=0?ft.S:ft.N:Math.abs(t)*2<=Math.abs(i)?i>0?ft.E:ft.W:t>0?i>0?ft.SE:ft.SW:i>0?ft.NE:ft.NW,matrixAppendRotationInv:(i,t,e=0,s=0)=>{const n=gn[ft.inv(t)];n.tx=e,n.ty=s,i.append(n)}},Be=[new ct,new ct,new ct,new ct];class Ct{constructor(t=0,e=0,s=0,n=0){this.type="rectangle",this.x=Number(t),this.y=Number(e),this.width=Number(s),this.height=Number(n)}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new Ct(0,0,0,0)}clone(){return new Ct(this.x,this.y,this.width,this.height)}copyFromBounds(t){return this.x=t.minX,this.y=t.minY,this.width=t.maxX-t.minX,this.height=t.maxY-t.minY,this}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){return this.width<=0||this.height<=0?!1:t>=this.x&&t<this.x+this.width&&e>=this.y&&e<this.y+this.height}strokeContains(t,e,s,n=.5){const{width:r,height:o}=this;if(r<=0||o<=0)return!1;const a=this.x,h=this.y,c=s*(1-n),l=s-c,u=a-c,f=a+r+c,d=h-c,x=h+o+c,_=a+l,g=a+r-l,b=h+l,w=h+o-l;return t>=u&&t<=f&&e>=d&&e<=x&&!(t>_&&t<g&&e>b&&e<w)}intersects(t,e){if(!e){const O=this.x<t.x?t.x:this.x;if((this.right>t.right?t.right:this.right)<=O)return!1;const $=this.y<t.y?t.y:this.y;return(this.bottom>t.bottom?t.bottom:this.bottom)>$}const s=this.left,n=this.right,r=this.top,o=this.bottom;if(n<=s||o<=r)return!1;const a=Be[0].set(t.left,t.top),h=Be[1].set(t.left,t.bottom),c=Be[2].set(t.right,t.top),l=Be[3].set(t.right,t.bottom);if(c.x<=a.x||h.y<=a.y)return!1;const u=Math.sign(e.a*e.d-e.b*e.c);if(u===0||(e.apply(a,a),e.apply(h,h),e.apply(c,c),e.apply(l,l),Math.max(a.x,h.x,c.x,l.x)<=s||Math.min(a.x,h.x,c.x,l.x)>=n||Math.max(a.y,h.y,c.y,l.y)<=r||Math.min(a.y,h.y,c.y,l.y)>=o))return!1;const f=u*(h.y-a.y),d=u*(a.x-h.x),x=f*s+d*r,_=f*n+d*r,g=f*s+d*o,b=f*n+d*o;if(Math.max(x,_,g,b)<=f*a.x+d*a.y||Math.min(x,_,g,b)>=f*l.x+d*l.y)return!1;const w=u*(a.y-c.y),S=u*(c.x-a.x),M=w*s+S*r,I=w*n+S*r,E=w*s+S*o,P=w*n+S*o;return!(Math.max(M,I,E,P)<=w*a.x+S*a.y||Math.min(M,I,E,P)>=w*l.x+S*l.y)}pad(t=0,e=t){return this.x-=t,this.y-=e,this.width+=t*2,this.height+=e*2,this}fit(t){const e=Math.max(this.x,t.x),s=Math.min(this.x+this.width,t.x+t.width),n=Math.max(this.y,t.y),r=Math.min(this.y+this.height,t.y+t.height);return this.x=e,this.width=Math.max(s-e,0),this.y=n,this.height=Math.max(r-n,0),this}ceil(t=1,e=.001){const s=Math.ceil((this.x+this.width-e)*t)/t,n=Math.ceil((this.y+this.height-e)*t)/t;return this.x=Math.floor((this.x+e)*t)/t,this.y=Math.floor((this.y+e)*t)/t,this.width=s-this.x,this.height=n-this.y,this}enlarge(t){const e=Math.min(this.x,t.x),s=Math.max(this.x+this.width,t.x+t.width),n=Math.min(this.y,t.y),r=Math.max(this.y+this.height,t.y+t.height);return this.x=e,this.width=s-e,this.y=n,this.height=r-n,this}getBounds(t){return t||(t=new Ct),t.copyFrom(this),t}containsRect(t){if(this.width<=0||this.height<=0)return!1;const e=t.x,s=t.y,n=t.x+t.width,r=t.y+t.height;return e>=this.x&&e<this.x+this.width&&s>=this.y&&s<this.y+this.height&&n>=this.x&&n<this.x+this.width&&r>=this.y&&r<this.y+this.height}toString(){return`[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const es={default:-1};function _t(i="default"){return es[i]===void 0&&(es[i]=-1),++es[i]}const hi={},mt="8.0.0",Or="8.3.4";function at(i,t,e=3){if(hi[t])return;let s=new Error().stack;typeof s>"u"?console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${i}`):(s=s.split(`
`).splice(e).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${t}
Deprecated since v${i}`),console.warn(s),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${i}`),console.warn(s))),hi[t]=!0}const mn=()=>{};function li(i){return i+=i===0?1:0,--i,i|=i>>>1,i|=i>>>2,i|=i>>>4,i|=i>>>8,i|=i>>>16,i+1}function ci(i){return!(i&i-1)&&!!i}function xn(i){const t={};for(const e in i)i[e]!==void 0&&(t[e]=i[e]);return t}const ui=Object.create(null);function Vr(i){const t=ui[i];return t===void 0&&(ui[i]=_t("resource")),t}const yn=class _n extends Nt{constructor(t={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,t={..._n.defaultOptions,...t},this.addressMode=t.addressMode,this.addressModeU=t.addressModeU??this.addressModeU,this.addressModeV=t.addressModeV??this.addressModeV,this.addressModeW=t.addressModeW??this.addressModeW,this.scaleMode=t.scaleMode,this.magFilter=t.magFilter??this.magFilter,this.minFilter=t.minFilter??this.minFilter,this.mipmapFilter=t.mipmapFilter??this.mipmapFilter,this.lodMinClamp=t.lodMinClamp,this.lodMaxClamp=t.lodMaxClamp,this.compare=t.compare,this.maxAnisotropy=t.maxAnisotropy??1}set addressMode(t){this.addressModeU=t,this.addressModeV=t,this.addressModeW=t}get addressMode(){return this.addressModeU}set wrapMode(t){at(mt,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=t}get wrapMode(){return this.addressMode}set scaleMode(t){this.magFilter=t,this.minFilter=t,this.mipmapFilter=t}get scaleMode(){return this.magFilter}set maxAnisotropy(t){this._maxAnisotropy=Math.min(t,16),this._maxAnisotropy>1&&(this.scaleMode="linear")}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null}_generateResourceId(){const t=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=Vr(t),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners()}};yn.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let Yr=yn;const bn=class wn extends Nt{constructor(t={}){super(),this.options=t,this.uid=_t("textureSource"),this._resourceType="textureSource",this._resourceId=_t("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,t={...wn.defaultOptions,...t},this.label=t.label??"",this.resource=t.resource,this.autoGarbageCollect=t.autoGarbageCollect,this._resolution=t.resolution,t.width?this.pixelWidth=t.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,t.height?this.pixelHeight=t.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=t.format,this.dimension=t.dimensions,this.mipLevelCount=t.mipLevelCount,this.autoGenerateMipmaps=t.autoGenerateMipmaps,this.sampleCount=t.sampleCount,this.antialias=t.antialias,this.alphaMode=t.alphaMode,this.style=new Yr(xn(t)),this.destroyed=!1,this._refreshPOT()}get source(){return this}get style(){return this._style}set style(t){var e,s;this.style!==t&&((e=this._style)==null||e.off("change",this._onStyleChange,this),this._style=t,(s=this._style)==null||s.on("change",this._onStyleChange,this),this._onStyleChange())}get addressMode(){return this._style.addressMode}set addressMode(t){this._style.addressMode=t}get repeatMode(){return this._style.addressMode}set repeatMode(t){this._style.addressMode=t}get magFilter(){return this._style.magFilter}set magFilter(t){this._style.magFilter=t}get minFilter(){return this._style.minFilter}set minFilter(t){this._style.minFilter=t}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(t){this._style.mipmapFilter=t}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(t){this._style.lodMinClamp=t}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(t){this._style.lodMaxClamp=t}_onStyleChange(){this.emit("styleChange",this)}update(){if(this.resource){const t=this._resolution;if(this.resize(this.resourceWidth/t,this.resourceHeight/t))return}this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners()}unload(){this._resourceId=_t("resource"),this.emit("change",this),this.emit("unload",this)}get resourceWidth(){const{resource:t}=this;return t.naturalWidth||t.videoWidth||t.displayWidth||t.width}get resourceHeight(){const{resource:t}=this;return t.naturalHeight||t.videoHeight||t.displayHeight||t.height}get resolution(){return this._resolution}set resolution(t){this._resolution!==t&&(this._resolution=t,this.width=this.pixelWidth/t,this.height=this.pixelHeight/t)}resize(t,e,s){s||(s=this._resolution),t||(t=this.width),e||(e=this.height);const n=Math.round(t*s),r=Math.round(e*s);return this.width=n/s,this.height=r/s,this._resolution=s,this.pixelWidth===n&&this.pixelHeight===r?!1:(this._refreshPOT(),this.pixelWidth=n,this.pixelHeight=r,this.emit("resize",this),this._resourceId=_t("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this)}set wrapMode(t){this._style.wrapMode=t}get wrapMode(){return this._style.wrapMode}set scaleMode(t){this._style.scaleMode=t}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=ci(this.pixelWidth)&&ci(this.pixelHeight)}static test(t){throw new Error("Unimplemented")}};bn.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let Wt=bn;class Ws extends Wt{constructor(t){const e=t.resource||new Float32Array(t.width*t.height*4);let s=t.format;s||(e instanceof Float32Array?s="rgba32float":e instanceof Int32Array||e instanceof Uint32Array?s="rgba32uint":e instanceof Int16Array||e instanceof Uint16Array?s="rgba16uint":(e instanceof Int8Array,s="bgra8unorm")),super({...t,resource:e,format:s}),this.uploadMethodId="buffer"}static test(t){return t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array}}Ws.extension=rt.TextureSource;const di=new it;class Xr{constructor(t,e){this.mapCoord=new it,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof e>"u"?this.clampMargin=t.width<10?0:.5:this.clampMargin=e,this.isSimple=!1,this.texture=t}get texture(){return this._texture}set texture(t){var e;this.texture!==t&&((e=this._texture)==null||e.removeListener("update",this.update,this),this._texture=t,this._texture.addListener("update",this.update,this),this.update())}multiplyUvs(t,e){e===void 0&&(e=t);const s=this.mapCoord;for(let n=0;n<t.length;n+=2){const r=t[n],o=t[n+1];e[n]=r*s.a+o*s.c+s.tx,e[n+1]=r*s.b+o*s.d+s.ty}return e}update(){const t=this._texture;this._updateID++;const e=t.uvs;this.mapCoord.set(e.x1-e.x0,e.y1-e.y0,e.x3-e.x0,e.y3-e.y0,e.x0,e.y0);const s=t.orig,n=t.trim;n&&(di.set(s.width/n.width,0,0,s.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append(di));const r=t.source,o=this.uClampFrame,a=this.clampMargin/r._resolution,h=this.clampOffset/r._resolution;return o[0]=(t.frame.x+a+h)/r.width,o[1]=(t.frame.y+a+h)/r.height,o[2]=(t.frame.x+t.frame.width-a+h)/r.width,o[3]=(t.frame.y+t.frame.height-a+h)/r.height,this.uClampOffset[0]=this.clampOffset/r.pixelWidth,this.uClampOffset[1]=this.clampOffset/r.pixelHeight,this.isSimple=t.frame.width===r.width&&t.frame.height===r.height&&t.rotate===0,!0}}class ht extends Nt{constructor({source:t,label:e,frame:s,orig:n,trim:r,defaultAnchor:o,defaultBorders:a,rotate:h,dynamic:c}={}){if(super(),this.uid=_t("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new Ct,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=e,this.source=(t==null?void 0:t.source)??new Wt,this.noFrame=!s,s)this.frame.copyFrom(s);else{const{width:l,height:u}=this._source;this.frame.width=l,this.frame.height=u}this.orig=n||this.frame,this.trim=r,this.rotate=h??0,this.defaultAnchor=o,this.defaultBorders=a,this.destroyed=!1,this.dynamic=c||!1,this.updateUvs()}set source(t){this._source&&this._source.off("resize",this.update,this),this._source=t,t.on("resize",this.update,this),this.emit("update",this)}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new Xr(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:t,frame:e}=this,{width:s,height:n}=this._source,r=e.x/s,o=e.y/n,a=e.width/s,h=e.height/n;let c=this.rotate;if(c){const l=a/2,u=h/2,f=r+l,d=o+u;c=ft.add(c,ft.NW),t.x0=f+l*ft.uX(c),t.y0=d+u*ft.uY(c),c=ft.add(c,2),t.x1=f+l*ft.uX(c),t.y1=d+u*ft.uY(c),c=ft.add(c,2),t.x2=f+l*ft.uX(c),t.y2=d+u*ft.uY(c),c=ft.add(c,2),t.x3=f+l*ft.uX(c),t.y3=d+u*ft.uY(c)}else t.x0=r,t.y0=o,t.x1=r+a,t.y1=o,t.x2=r+a,t.y2=o+h,t.x3=r,t.y3=o+h}destroy(t=!1){this._source&&t&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners()}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this)}get baseTexture(){return at(mt,"Texture.baseTexture is now Texture.source"),this._source}}ht.EMPTY=new ht({label:"EMPTY",source:new Wt({label:"EMPTY"})});ht.EMPTY.destroy=mn;ht.WHITE=new ht({source:new Ws({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});ht.WHITE.destroy=mn;function jr(i,t,e){const{width:s,height:n}=e.orig,r=e.trim;if(r){const o=r.width,a=r.height;i.minX=r.x-t._x*s,i.maxX=i.minX+o,i.minY=r.y-t._y*n,i.maxY=i.minY+a}else i.minX=-t._x*s,i.maxX=i.minX+s,i.minY=-t._y*n,i.maxY=i.minY+n}const fi=new it;class $t{constructor(t=1/0,e=1/0,s=-1/0,n=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=fi,this.minX=t,this.minY=e,this.maxX=s,this.maxY=n}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new Ct);const t=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(t.x=0,t.y=0,t.width=0,t.height=0):t.copyFromBounds(this),t}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=fi,this}set(t,e,s,n){this.minX=t,this.minY=e,this.maxX=s,this.maxY=n}addFrame(t,e,s,n,r){r||(r=this.matrix);const o=r.a,a=r.b,h=r.c,c=r.d,l=r.tx,u=r.ty;let f=this.minX,d=this.minY,x=this.maxX,_=this.maxY,g=o*t+h*e+l,b=a*t+c*e+u;g<f&&(f=g),b<d&&(d=b),g>x&&(x=g),b>_&&(_=b),g=o*s+h*e+l,b=a*s+c*e+u,g<f&&(f=g),b<d&&(d=b),g>x&&(x=g),b>_&&(_=b),g=o*t+h*n+l,b=a*t+c*n+u,g<f&&(f=g),b<d&&(d=b),g>x&&(x=g),b>_&&(_=b),g=o*s+h*n+l,b=a*s+c*n+u,g<f&&(f=g),b<d&&(d=b),g>x&&(x=g),b>_&&(_=b),this.minX=f,this.minY=d,this.maxX=x,this.maxY=_}addRect(t,e){this.addFrame(t.x,t.y,t.x+t.width,t.y+t.height,e)}addBounds(t,e){this.addFrame(t.minX,t.minY,t.maxX,t.maxY,e)}addBoundsMask(t){this.minX=this.minX>t.minX?this.minX:t.minX,this.minY=this.minY>t.minY?this.minY:t.minY,this.maxX=this.maxX<t.maxX?this.maxX:t.maxX,this.maxY=this.maxY<t.maxY?this.maxY:t.maxY}applyMatrix(t){const e=this.minX,s=this.minY,n=this.maxX,r=this.maxY,{a:o,b:a,c:h,d:c,tx:l,ty:u}=t;let f=o*e+h*s+l,d=a*e+c*s+u;this.minX=f,this.minY=d,this.maxX=f,this.maxY=d,f=o*n+h*s+l,d=a*n+c*s+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY,f=o*e+h*r+l,d=a*e+c*r+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY,f=o*n+h*r+l,d=a*n+c*r+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY}fit(t){return this.minX<t.left&&(this.minX=t.left),this.maxX>t.right&&(this.maxX=t.right),this.minY<t.top&&(this.minY=t.top),this.maxY>t.bottom&&(this.maxY=t.bottom),this}fitBounds(t,e,s,n){return this.minX<t&&(this.minX=t),this.maxX>e&&(this.maxX=e),this.minY<s&&(this.minY=s),this.maxY>n&&(this.maxY=n),this}pad(t,e=t){return this.minX-=t,this.maxX+=t,this.minY-=e,this.maxY+=e,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new $t(this.minX,this.minY,this.maxX,this.maxY)}scale(t,e=t){return this.minX*=t,this.minY*=e,this.maxX*=t,this.maxY*=e,this}get x(){return this.minX}set x(t){const e=this.maxX-this.minX;this.minX=t,this.maxX=t+e}get y(){return this.minY}set y(t){const e=this.maxY-this.minY;this.minY=t,this.maxY=t+e}get width(){return this.maxX-this.minX}set width(t){this.maxX=this.minX+t}get height(){return this.maxY-this.minY}set height(t){this.maxY=this.minY+t}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(t,e,s,n){let r=this.minX,o=this.minY,a=this.maxX,h=this.maxY;n||(n=this.matrix);const c=n.a,l=n.b,u=n.c,f=n.d,d=n.tx,x=n.ty;for(let _=e;_<s;_+=2){const g=t[_],b=t[_+1],w=c*g+u*b+d,S=l*g+f*b+x;r=w<r?w:r,o=S<o?S:o,a=w>a?w:a,h=S>h?S:h}this.minX=r,this.minY=o,this.maxX=a,this.maxY=h}containsPoint(t,e){return this.minX<=t&&this.minY<=e&&this.maxX>=t&&this.maxY>=e}toString(){return`[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(t){return this.minX=t.minX,this.minY=t.minY,this.maxX=t.maxX,this.maxY=t.maxY,this}}var Kr={grad:.9,turn:360,rad:360/(2*Math.PI)},Ot=function(i){return typeof i=="string"?i.length>0:typeof i=="number"},St=function(i,t,e){return t===void 0&&(t=0),e===void 0&&(e=Math.pow(10,t)),Math.round(e*i)/e+0},Rt=function(i,t,e){return t===void 0&&(t=0),e===void 0&&(e=1),i>e?e:i>t?i:t},Sn=function(i){return(i=isFinite(i)?i%360:0)>0?i:i+360},pi=function(i){return{r:Rt(i.r,0,255),g:Rt(i.g,0,255),b:Rt(i.b,0,255),a:Rt(i.a)}},ss=function(i){return{r:St(i.r),g:St(i.g),b:St(i.b),a:St(i.a,3)}},Zr=/^#([0-9a-f]{3,8})$/i,Re=function(i){var t=i.toString(16);return t.length<2?"0"+t:t},Cn=function(i){var t=i.r,e=i.g,s=i.b,n=i.a,r=Math.max(t,e,s),o=r-Math.min(t,e,s),a=o?r===t?(e-s)/o:r===e?2+(s-t)/o:4+(t-e)/o:0;return{h:60*(a<0?a+6:a),s:r?o/r*100:0,v:r/255*100,a:n}},Mn=function(i){var t=i.h,e=i.s,s=i.v,n=i.a;t=t/360*6,e/=100,s/=100;var r=Math.floor(t),o=s*(1-e),a=s*(1-(t-r)*e),h=s*(1-(1-t+r)*e),c=r%6;return{r:255*[s,a,o,o,h,s][c],g:255*[h,s,s,a,o,o][c],b:255*[o,o,h,s,s,a][c],a:n}},gi=function(i){return{h:Sn(i.h),s:Rt(i.s,0,100),l:Rt(i.l,0,100),a:Rt(i.a)}},mi=function(i){return{h:St(i.h),s:St(i.s),l:St(i.l),a:St(i.a,3)}},xi=function(i){return Mn((e=(t=i).s,{h:t.h,s:(e*=((s=t.l)<50?s:100-s)/100)>0?2*e/(s+e)*100:0,v:s+e,a:t.a}));var t,e,s},Ce=function(i){return{h:(t=Cn(i)).h,s:(n=(200-(e=t.s))*(s=t.v)/100)>0&&n<200?e*s/100/(n<=100?n:200-n)*100:0,l:n/2,a:t.a};var t,e,s,n},Qr=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Jr=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,to=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,eo=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Es={string:[[function(i){var t=Zr.exec(i);return t?(i=t[1]).length<=4?{r:parseInt(i[0]+i[0],16),g:parseInt(i[1]+i[1],16),b:parseInt(i[2]+i[2],16),a:i.length===4?St(parseInt(i[3]+i[3],16)/255,2):1}:i.length===6||i.length===8?{r:parseInt(i.substr(0,2),16),g:parseInt(i.substr(2,2),16),b:parseInt(i.substr(4,2),16),a:i.length===8?St(parseInt(i.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(i){var t=to.exec(i)||eo.exec(i);return t?t[2]!==t[4]||t[4]!==t[6]?null:pi({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:t[7]===void 0?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(i){var t=Qr.exec(i)||Jr.exec(i);if(!t)return null;var e,s,n=gi({h:(e=t[1],s=t[2],s===void 0&&(s="deg"),Number(e)*(Kr[s]||1)),s:Number(t[3]),l:Number(t[4]),a:t[5]===void 0?1:Number(t[5])/(t[6]?100:1)});return xi(n)},"hsl"]],object:[[function(i){var t=i.r,e=i.g,s=i.b,n=i.a,r=n===void 0?1:n;return Ot(t)&&Ot(e)&&Ot(s)?pi({r:Number(t),g:Number(e),b:Number(s),a:Number(r)}):null},"rgb"],[function(i){var t=i.h,e=i.s,s=i.l,n=i.a,r=n===void 0?1:n;if(!Ot(t)||!Ot(e)||!Ot(s))return null;var o=gi({h:Number(t),s:Number(e),l:Number(s),a:Number(r)});return xi(o)},"hsl"],[function(i){var t=i.h,e=i.s,s=i.v,n=i.a,r=n===void 0?1:n;if(!Ot(t)||!Ot(e)||!Ot(s))return null;var o=function(a){return{h:Sn(a.h),s:Rt(a.s,0,100),v:Rt(a.v,0,100),a:Rt(a.a)}}({h:Number(t),s:Number(e),v:Number(s),a:Number(r)});return Mn(o)},"hsv"]]},yi=function(i,t){for(var e=0;e<t.length;e++){var s=t[e][0](i);if(s)return[s,t[e][1]]}return[null,void 0]},so=function(i){return typeof i=="string"?yi(i.trim(),Es.string):typeof i=="object"&&i!==null?yi(i,Es.object):[null,void 0]},is=function(i,t){var e=Ce(i);return{h:e.h,s:Rt(e.s+100*t,0,100),l:e.l,a:e.a}},ns=function(i){return(299*i.r+587*i.g+114*i.b)/1e3/255},_i=function(i,t){var e=Ce(i);return{h:e.h,s:e.s,l:Rt(e.l+100*t,0,100),a:e.a}},ks=function(){function i(t){this.parsed=so(t)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return i.prototype.isValid=function(){return this.parsed!==null},i.prototype.brightness=function(){return St(ns(this.rgba),2)},i.prototype.isDark=function(){return ns(this.rgba)<.5},i.prototype.isLight=function(){return ns(this.rgba)>=.5},i.prototype.toHex=function(){return t=ss(this.rgba),e=t.r,s=t.g,n=t.b,o=(r=t.a)<1?Re(St(255*r)):"","#"+Re(e)+Re(s)+Re(n)+o;var t,e,s,n,r,o},i.prototype.toRgb=function(){return ss(this.rgba)},i.prototype.toRgbString=function(){return t=ss(this.rgba),e=t.r,s=t.g,n=t.b,(r=t.a)<1?"rgba("+e+", "+s+", "+n+", "+r+")":"rgb("+e+", "+s+", "+n+")";var t,e,s,n,r},i.prototype.toHsl=function(){return mi(Ce(this.rgba))},i.prototype.toHslString=function(){return t=mi(Ce(this.rgba)),e=t.h,s=t.s,n=t.l,(r=t.a)<1?"hsla("+e+", "+s+"%, "+n+"%, "+r+")":"hsl("+e+", "+s+"%, "+n+"%)";var t,e,s,n,r},i.prototype.toHsv=function(){return t=Cn(this.rgba),{h:St(t.h),s:St(t.s),v:St(t.v),a:St(t.a,3)};var t},i.prototype.invert=function(){return qt({r:255-(t=this.rgba).r,g:255-t.g,b:255-t.b,a:t.a});var t},i.prototype.saturate=function(t){return t===void 0&&(t=.1),qt(is(this.rgba,t))},i.prototype.desaturate=function(t){return t===void 0&&(t=.1),qt(is(this.rgba,-t))},i.prototype.grayscale=function(){return qt(is(this.rgba,-1))},i.prototype.lighten=function(t){return t===void 0&&(t=.1),qt(_i(this.rgba,t))},i.prototype.darken=function(t){return t===void 0&&(t=.1),qt(_i(this.rgba,-t))},i.prototype.rotate=function(t){return t===void 0&&(t=15),this.hue(this.hue()+t)},i.prototype.alpha=function(t){return typeof t=="number"?qt({r:(e=this.rgba).r,g:e.g,b:e.b,a:t}):St(this.rgba.a,3);var e},i.prototype.hue=function(t){var e=Ce(this.rgba);return typeof t=="number"?qt({h:t,s:e.s,l:e.l,a:e.a}):St(e.h)},i.prototype.isEqual=function(t){return this.toHex()===qt(t).toHex()},i}(),qt=function(i){return i instanceof ks?i:new ks(i)},bi=[],io=function(i){i.forEach(function(t){bi.indexOf(t)<0&&(t(ks,Es),bi.push(t))})};function no(i,t){var e={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},s={};for(var n in e)s[e[n]]=n;var r={};i.prototype.toName=function(o){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var a,h,c=s[this.toHex()];if(c)return c;if(o!=null&&o.closest){var l=this.toRgb(),u=1/0,f="black";if(!r.length)for(var d in e)r[d]=new i(e[d]).toRgb();for(var x in e){var _=(a=l,h=r[x],Math.pow(a.r-h.r,2)+Math.pow(a.g-h.g,2)+Math.pow(a.b-h.b,2));_<u&&(u=_,f=x)}return f}},t.string.push([function(o){var a=o.toLowerCase(),h=a==="transparent"?"#0000":e[a];return h?new i(h).toRgb():null},"name"])}io([no]);const fe=class be{constructor(t=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=t}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(t){return this.value=t,this}set value(t){if(t instanceof be)this._value=this._cloneSource(t._value),this._int=t._int,this._components.set(t._components);else{if(t===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,t))&&(this._value=this._cloneSource(t),this._normalize(this._value))}}get value(){return this._value}_cloneSource(t){return typeof t=="string"||typeof t=="number"||t instanceof Number||t===null?t:Array.isArray(t)||ArrayBuffer.isView(t)?t.slice(0):typeof t=="object"&&t!==null?{...t}:t}_isSourceEqual(t,e){const s=typeof t;if(s!==typeof e)return!1;if(s==="number"||s==="string"||t instanceof Number)return t===e;if(Array.isArray(t)&&Array.isArray(e)||ArrayBuffer.isView(t)&&ArrayBuffer.isView(e))return t.length!==e.length?!1:t.every((r,o)=>r===e[o]);if(t!==null&&e!==null){const r=Object.keys(t),o=Object.keys(e);return r.length!==o.length?!1:r.every(a=>t[a]===e[a])}return t===e}toRgba(){const[t,e,s,n]=this._components;return{r:t,g:e,b:s,a:n}}toRgb(){const[t,e,s]=this._components;return{r:t,g:e,b:s}}toRgbaString(){const[t,e,s]=this.toUint8RgbArray();return`rgba(${t},${e},${s},${this.alpha})`}toUint8RgbArray(t){const[e,s,n]=this._components;return this._arrayRgb||(this._arrayRgb=[]),t||(t=this._arrayRgb),t[0]=Math.round(e*255),t[1]=Math.round(s*255),t[2]=Math.round(n*255),t}toArray(t){this._arrayRgba||(this._arrayRgba=[]),t||(t=this._arrayRgba);const[e,s,n,r]=this._components;return t[0]=e,t[1]=s,t[2]=n,t[3]=r,t}toRgbArray(t){this._arrayRgb||(this._arrayRgb=[]),t||(t=this._arrayRgb);const[e,s,n]=this._components;return t[0]=e,t[1]=s,t[2]=n,t}toNumber(){return this._int}toBgrNumber(){const[t,e,s]=this.toUint8RgbArray();return(s<<16)+(e<<8)+t}toLittleEndianNumber(){const t=this._int;return(t>>16)+(t&65280)+((t&255)<<16)}multiply(t){const[e,s,n,r]=be._temp.setValue(t)._components;return this._components[0]*=e,this._components[1]*=s,this._components[2]*=n,this._components[3]*=r,this._refreshInt(),this._value=null,this}premultiply(t,e=!0){return e&&(this._components[0]*=t,this._components[1]*=t,this._components[2]*=t),this._components[3]=t,this._refreshInt(),this._value=null,this}toPremultiplied(t,e=!0){if(t===1)return(255<<24)+this._int;if(t===0)return e?0:this._int;let s=this._int>>16&255,n=this._int>>8&255,r=this._int&255;return e&&(s=s*t+.5|0,n=n*t+.5|0,r=r*t+.5|0),(t*255<<24)+(s<<16)+(n<<8)+r}toHex(){const t=this._int.toString(16);return`#${"000000".substring(0,6-t.length)+t}`}toHexa(){const e=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-e.length)+e}setAlpha(t){return this._components[3]=this._clamp(t),this}_normalize(t){let e,s,n,r;if((typeof t=="number"||t instanceof Number)&&t>=0&&t<=16777215){const o=t;e=(o>>16&255)/255,s=(o>>8&255)/255,n=(o&255)/255,r=1}else if((Array.isArray(t)||t instanceof Float32Array)&&t.length>=3&&t.length<=4)t=this._clamp(t),[e,s,n,r=1]=t;else if((t instanceof Uint8Array||t instanceof Uint8ClampedArray)&&t.length>=3&&t.length<=4)t=this._clamp(t,0,255),[e,s,n,r=255]=t,e/=255,s/=255,n/=255,r/=255;else if(typeof t=="string"||typeof t=="object"){if(typeof t=="string"){const a=be.HEX_PATTERN.exec(t);a&&(t=`#${a[2]}`)}const o=qt(t);o.isValid()&&({r:e,g:s,b:n,a:r}=o.rgba,e/=255,s/=255,n/=255)}if(e!==void 0)this._components[0]=e,this._components[1]=s,this._components[2]=n,this._components[3]=r,this._refreshInt();else throw new Error(`Unable to convert color ${t}`)}_refreshInt(){this._clamp(this._components);const[t,e,s]=this._components;this._int=(t*255<<16)+(e*255<<8)+(s*255|0)}_clamp(t,e=0,s=1){return typeof t=="number"?Math.min(Math.max(t,e),s):(t.forEach((n,r)=>{t[r]=Math.min(Math.max(n,e),s)}),t)}static isColorLike(t){return typeof t=="number"||typeof t=="string"||t instanceof Number||t instanceof be||Array.isArray(t)||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Float32Array||t.r!==void 0&&t.g!==void 0&&t.b!==void 0||t.r!==void 0&&t.g!==void 0&&t.b!==void 0&&t.a!==void 0||t.h!==void 0&&t.s!==void 0&&t.l!==void 0||t.h!==void 0&&t.s!==void 0&&t.l!==void 0&&t.a!==void 0||t.h!==void 0&&t.s!==void 0&&t.v!==void 0||t.h!==void 0&&t.s!==void 0&&t.v!==void 0&&t.a!==void 0}};fe.shared=new fe;fe._temp=new fe;fe.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let Mt=fe;const ro={cullArea:null,cullable:!1,cullableChildren:!0};class Os{constructor(t,e){this._pool=[],this._count=0,this._index=0,this._classType=t,e&&this.prepopulate(e)}prepopulate(t){for(let e=0;e<t;e++)this._pool[this._index++]=new this._classType;this._count+=t}get(t){var s;let e;return this._index>0?e=this._pool[--this._index]:e=new this._classType,(s=e.init)==null||s.call(e,t),e}return(t){var e;(e=t.reset)==null||e.call(t),this._pool[this._index++]=t}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0}}class oo{constructor(){this._poolsByClass=new Map}prepopulate(t,e){this.getPool(t).prepopulate(e)}get(t,e){return this.getPool(t).get(e)}return(t){this.getPool(t.constructor).return(t)}getPool(t){return this._poolsByClass.has(t)||this._poolsByClass.set(t,new Os(t)),this._poolsByClass.get(t)}stats(){const t={};return this._poolsByClass.forEach(e=>{const s=t[e._classType.name]?e._classType.name+e._classType.ID:e._classType.name;t[s]={free:e.totalFree,used:e.totalUsed,size:e.totalSize}}),t}}const Vt=new oo,ao={get isCachedAsTexture(){var i;return!!((i=this.renderGroup)!=null&&i.isCachedAsTexture)},cacheAsTexture(i){typeof i=="boolean"&&i===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(i===!0?{}:i))},updateCacheTexture(){var i;(i=this.renderGroup)==null||i.updateCacheTexture()},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(i){at("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(i)}};function ho(i,t,e){const s=i.length;let n;if(t>=s||e===0)return;e=t+e>s?s-t:e;const r=s-e;for(n=t;n<r;++n)i[n]=i[n+e];i.length=r}const lo={allowChildren:!0,removeChildren(i=0,t){const e=t??this.children.length,s=e-i,n=[];if(s>0&&s<=e){for(let o=e-1;o>=i;o--){const a=this.children[o];a&&(n.push(a),a.parent=null)}ho(this.children,i,e);const r=this.renderGroup||this.parentRenderGroup;r&&r.removeChildren(n);for(let o=0;o<n.length;++o)this.emit("childRemoved",n[o],this,o),n[o].emit("removed",this);return n.length>0&&this._didViewChangeTick++,n}else if(s===0&&this.children.length===0)return n;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(i){const t=this.getChildAt(i);return this.removeChild(t)},getChildAt(i){if(i<0||i>=this.children.length)throw new Error(`getChildAt: Index (${i}) does not exist.`);return this.children[i]},setChildIndex(i,t){if(t<0||t>=this.children.length)throw new Error(`The index ${t} supplied is out of bounds ${this.children.length}`);this.getChildIndex(i),this.addChildAt(i,t)},getChildIndex(i){const t=this.children.indexOf(i);if(t===-1)throw new Error("The supplied Container must be a child of the caller");return t},addChildAt(i,t){this.allowChildren||at(mt,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:e}=this;if(t<0||t>e.length)throw new Error(`${i}addChildAt: The index ${t} supplied is out of bounds ${e.length}`);if(i.parent){const n=i.parent.children.indexOf(i);if(i.parent===this&&n===t)return i;n!==-1&&i.parent.children.splice(n,1)}t===e.length?e.push(i):e.splice(t,0,i),i.parent=this,i.didChange=!0,i._updateFlags=15;const s=this.renderGroup||this.parentRenderGroup;return s&&s.addChild(i),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",i,this,t),i.emit("added",this),i},swapChildren(i,t){if(i===t)return;const e=this.getChildIndex(i),s=this.getChildIndex(t);this.children[e]=t,this.children[s]=i;const n=this.renderGroup||this.parentRenderGroup;n&&(n.structureDidChange=!0),this._didContainerChangeTick++},removeFromParent(){var i;(i=this.parent)==null||i.removeChild(this)},reparentChild(...i){return i.length===1?this.reparentChildAt(i[0],this.children.length):(i.forEach(t=>this.reparentChildAt(t,this.children.length)),i[0])},reparentChildAt(i,t){if(i.parent===this)return this.setChildIndex(i,t),i;const e=i.worldTransform.clone();i.removeFromParent(),this.addChildAt(i,t);const s=this.worldTransform.clone();return s.invert(),e.prepend(s),i.setFromMatrix(e),i}},co={collectRenderables(i,t,e){this.parentRenderLayer&&this.parentRenderLayer!==e||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(i,t,e):this.renderGroup?t.renderPipes.renderGroup.addRenderGroup(this.renderGroup,i):this.collectRenderablesWithEffects(i,t,e))},collectRenderablesSimple(i,t,e){const s=this.children,n=s.length;for(let r=0;r<n;r++)s[r].collectRenderables(i,t,e)},collectRenderablesWithEffects(i,t,e){const{renderPipes:s}=t;for(let n=0;n<this.effects.length;n++){const r=this.effects[n];s[r.pipe].push(r,this,i)}this.collectRenderablesSimple(i,t,e);for(let n=this.effects.length-1;n>=0;n--){const r=this.effects[n];s[r.pipe].pop(r,this,i)}}};class wi{constructor(){this.pipe="filter",this.priority=1}destroy(){for(let t=0;t<this.filters.length;t++)this.filters[t].destroy();this.filters=null,this.filterArea=null}}class uo{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(t=>{this.add({test:t.test,maskClass:t})}))}add(t){this._tests.push(t)}getMaskEffect(t){this._initialized||this.init();for(let e=0;e<this._tests.length;e++){const s=this._tests[e];if(s.test(t))return Vt.get(s.maskClass,t)}return t}returnMaskEffect(t){Vt.return(t)}}const Is=new uo;Ft.handleByList(rt.MaskEffect,Is._effectClasses);const fo={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const i=this.renderGroup||this.parentRenderGroup;i&&(i.structureDidChange=!0)},addEffect(i){this.effects.indexOf(i)===-1&&(this.effects.push(i),this.effects.sort((e,s)=>e.priority-s.priority),this._markStructureAsChanged(),this._updateIsSimple())},removeEffect(i){const t=this.effects.indexOf(i);t!==-1&&(this.effects.splice(t,1),this._markStructureAsChanged(),this._updateIsSimple())},set mask(i){const t=this._maskEffect;(t==null?void 0:t.mask)!==i&&(t&&(this.removeEffect(t),Is.returnMaskEffect(t),this._maskEffect=null),i!=null&&(this._maskEffect=Is.getMaskEffect(i),this.addEffect(this._maskEffect)))},setMask(i){this._maskOptions={...this._maskOptions,...i},i.mask&&(this.mask=i.mask),this._markStructureAsChanged()},get mask(){var i;return(i=this._maskEffect)==null?void 0:i.mask},set filters(i){var r;!Array.isArray(i)&&i&&(i=[i]);const t=this._filterEffect||(this._filterEffect=new wi);i=i;const e=(i==null?void 0:i.length)>0,s=((r=t.filters)==null?void 0:r.length)>0,n=e!==s;i=Array.isArray(i)?i.slice(0):i,t.filters=Object.freeze(i),n&&(e?this.addEffect(t):(this.removeEffect(t),t.filters=i??null))},get filters(){var i;return(i=this._filterEffect)==null?void 0:i.filters},set filterArea(i){this._filterEffect||(this._filterEffect=new wi),this._filterEffect.filterArea=i},get filterArea(){var i;return(i=this._filterEffect)==null?void 0:i.filterArea}},po={label:null,get name(){return at(mt,"Container.name property has been removed, use Container.label instead"),this.label},set name(i){at(mt,"Container.name property has been removed, use Container.label instead"),this.label=i},getChildByName(i,t=!1){return this.getChildByLabel(i,t)},getChildByLabel(i,t=!1){const e=this.children;for(let s=0;s<e.length;s++){const n=e[s];if(n.label===i||i instanceof RegExp&&i.test(n.label))return n}if(t)for(let s=0;s<e.length;s++){const r=e[s].getChildByLabel(i,!0);if(r)return r}return null},getChildrenByLabel(i,t=!1,e=[]){const s=this.children;for(let n=0;n<s.length;n++){const r=s[n];(r.label===i||i instanceof RegExp&&i.test(r.label))&&e.push(r)}if(t)for(let n=0;n<s.length;n++)s[n].getChildrenByLabel(i,!0,e);return e}},vt=new Os(it),Yt=new Os($t),go=new it,mo={getFastGlobalBounds(i,t){t||(t=new $t),t.clear(),this._getGlobalBoundsRecursive(!!i,t,this.parentRenderLayer),t.isValid||t.set(0,0,0,0);const e=this.renderGroup||this.parentRenderGroup;return t.applyMatrix(e.worldTransform),t},_getGlobalBoundsRecursive(i,t,e){let s=t;if(i&&this.parentRenderLayer&&this.parentRenderLayer!==e||this.localDisplayStatus!==7||!this.measurable)return;const n=!!this.effects.length;if((this.renderGroup||n)&&(s=Yt.get().clear()),this.boundsArea)t.addRect(this.boundsArea,this.worldTransform);else{if(this.renderPipeId){const o=this.bounds;s.addFrame(o.minX,o.minY,o.maxX,o.maxY,this.groupTransform)}const r=this.children;for(let o=0;o<r.length;o++)r[o]._getGlobalBoundsRecursive(i,s,e)}if(n){let r=!1;const o=this.renderGroup||this.parentRenderGroup;for(let a=0;a<this.effects.length;a++)this.effects[a].addBounds&&(r||(r=!0,s.applyMatrix(o.worldTransform)),this.effects[a].addBounds(s,!0));r&&(s.applyMatrix(o.worldTransform.copyTo(go).invert()),t.addBounds(s,this.relativeGroupTransform)),t.addBounds(s),Yt.return(s)}else this.renderGroup&&(t.addBounds(s,this.relativeGroupTransform),Yt.return(s))}};function vn(i,t,e){e.clear();let s,n;return i.parent?t?s=i.parent.worldTransform:(n=vt.get().identity(),s=Vs(i,n)):s=it.IDENTITY,Tn(i,e,s,t),n&&vt.return(n),e.isValid||e.set(0,0,0,0),e}function Tn(i,t,e,s){var a,h;if(!i.visible||!i.measurable)return;let n;s?n=i.worldTransform:(i.updateLocalTransform(),n=vt.get(),n.appendFrom(i.localTransform,e));const r=t,o=!!i.effects.length;if(o&&(t=Yt.get().clear()),i.boundsArea)t.addRect(i.boundsArea,n);else{i.bounds&&(t.matrix=n,t.addBounds(i.bounds));for(let c=0;c<i.children.length;c++)Tn(i.children[c],t,n,s)}if(o){for(let c=0;c<i.effects.length;c++)(h=(a=i.effects[c]).addBounds)==null||h.call(a,t);r.addBounds(t,it.IDENTITY),Yt.return(t)}s||vt.return(n)}function Vs(i,t){const e=i.parent;return e&&(Vs(e,t),e.updateLocalTransform(),t.append(e.localTransform)),t}function An(i,t){if(i===16777215||!t)return t;if(t===16777215||!i)return i;const e=i>>16&255,s=i>>8&255,n=i&255,r=t>>16&255,o=t>>8&255,a=t&255,h=e*r/255|0,c=s*o/255|0,l=n*a/255|0;return(h<<16)+(c<<8)+l}const Si=16777215;function Ci(i,t){return i===Si?t:t===Si?i:An(i,t)}function ze(i){return((i&255)<<16)+(i&65280)+(i>>16&255)}const xo={getGlobalAlpha(i){if(i)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let t=this.alpha,e=this.parent;for(;e;)t*=e.alpha,e=e.parent;return t},getGlobalTransform(i,t){if(t)return i.copyFrom(this.worldTransform);this.updateLocalTransform();const e=Vs(this,vt.get().identity());return i.appendFrom(this.localTransform,e),vt.return(e),i},getGlobalTint(i){if(i)return this.renderGroup?ze(this.renderGroup.worldColor):this.parentRenderGroup?ze(Ci(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let t=this.localColor,e=this.parent;for(;e;)t=Ci(t,e.localColor),e=e.parent;return ze(t)}};let rs=0;const Mi=500;function kt(...i){rs!==Mi&&(rs++,rs===Mi?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...i))}function Pn(i,t,e){return t.clear(),e||(e=it.IDENTITY),En(i,t,e,i,!0),t.isValid||t.set(0,0,0,0),t}function En(i,t,e,s,n){var h,c;let r;if(n)r=vt.get(),r=e.copyTo(r);else{if(!i.visible||!i.measurable)return;i.updateLocalTransform();const l=i.localTransform;r=vt.get(),r.appendFrom(l,e)}const o=t,a=!!i.effects.length;if(a&&(t=Yt.get().clear()),i.boundsArea)t.addRect(i.boundsArea,r);else{i.renderPipeId&&(t.matrix=r,t.addBounds(i.bounds));const l=i.children;for(let u=0;u<l.length;u++)En(l[u],t,r,s,!1)}if(a){for(let l=0;l<i.effects.length;l++)(c=(h=i.effects[l]).addLocalBounds)==null||c.call(h,t,s);o.addBounds(t,it.IDENTITY),Yt.return(t)}vt.return(r)}function kn(i,t){const e=i.children;for(let s=0;s<e.length;s++){const n=e[s],r=n.uid,o=(n._didViewChangeTick&65535)<<16|n._didContainerChangeTick&65535,a=t.index;(t.data[a]!==r||t.data[a+1]!==o)&&(t.data[t.index]=r,t.data[t.index+1]=o,t.didChange=!0),t.index=a+2,n.children.length&&kn(n,t)}return t.didChange}const yo=new it,_o={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(i,t){const e=Math.sign(this.scale.x)||1;t!==0?this.scale.x=i/t*e:this.scale.x=e},_setHeight(i,t){const e=Math.sign(this.scale.y)||1;t!==0?this.scale.y=i/t*e:this.scale.y=e},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new $t});const i=this._localBoundsCacheData;return i.index=1,i.didChange=!1,i.data[0]!==this._didViewChangeTick&&(i.didChange=!0,i.data[0]=this._didViewChangeTick),kn(this,i),i.didChange&&Pn(this,i.localBounds,yo),i.localBounds},getBounds(i,t){return vn(this,i,t||new $t)}},bo={_onRender:null,set onRender(i){const t=this.renderGroup||this.parentRenderGroup;if(!i){this._onRender&&(t==null||t.removeOnRender(this)),this._onRender=null;return}this._onRender||t==null||t.addOnRender(this),this._onRender=i},get onRender(){return this._onRender}},wo={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(i){this._zIndex!==i&&(this._zIndex=i,this.depthOfChildModified())},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0)},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(So))}};function So(i,t){return i._zIndex-t._zIndex}const Co={getGlobalPosition(i=new ct,t=!1){return this.parent?this.parent.toGlobal(this._position,i,t):(i.x=this._position.x,i.y=this._position.y),i},toGlobal(i,t,e=!1){const s=this.getGlobalTransform(vt.get(),e);return t=s.apply(i,t),vt.return(s),t},toLocal(i,t,e,s){t&&(i=t.toGlobal(i,e,s));const n=this.getGlobalTransform(vt.get(),s);return e=n.applyInverse(i,e),vt.return(n),e}};class In{constructor(){this.uid=_t("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0}reset(){this.instructionSize=0}add(t){this.instructions[this.instructionSize++]=t}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"])}}let Mo=0;class vo{constructor(t){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=t||{},this.enableFullScreen=!1}createTexture(t,e,s){const n=new Wt({...this.textureOptions,width:t,height:e,resolution:1,antialias:s,autoGarbageCollect:!1});return new ht({source:n,label:`texturePool_${Mo++}`})}getOptimalTexture(t,e,s=1,n){let r=Math.ceil(t*s-1e-6),o=Math.ceil(e*s-1e-6);r=li(r),o=li(o);const a=(r<<17)+(o<<1)+(n?1:0);this._texturePool[a]||(this._texturePool[a]=[]);let h=this._texturePool[a].pop();return h||(h=this.createTexture(r,o,n)),h.source._resolution=s,h.source.width=r/s,h.source.height=o/s,h.source.pixelWidth=r,h.source.pixelHeight=o,h.frame.x=0,h.frame.y=0,h.frame.width=t,h.frame.height=e,h.updateUvs(),this._poolKeyHash[h.uid]=a,h}getSameSizeTexture(t,e=!1){const s=t.source;return this.getOptimalTexture(t.width,t.height,s._resolution,e)}returnTexture(t){const e=this._poolKeyHash[t.uid];this._texturePool[e].push(t)}clear(t){if(t=t!==!1,t)for(const e in this._texturePool){const s=this._texturePool[e];if(s)for(let n=0;n<s.length;n++)s[n].destroy(!0)}this._texturePool={}}}const To=new vo;class Ao{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new it,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new In,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7}init(t){this.root=t,t._onRender&&this.addOnRender(t),t.didChange=!0;const e=t.children;for(let s=0;s<e.length;s++){const n=e[s];n._updateFlags=15,this.addChild(n)}}enableCacheAsTexture(t={}){this.textureOptions=t,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(To.returnTexture(this.texture),this.texture=null)}updateCacheTexture(){this.textureNeedsUpdate=!0}reset(){this.renderGroupChildren.length=0;for(const t in this.childrenToUpdate){const e=this.childrenToUpdate[t];e.list.fill(null),e.index=0}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture()}get localTransform(){return this.root.localTransform}addRenderGroupChild(t){t.renderGroupParent&&t.renderGroupParent._removeRenderGroupChild(t),t.renderGroupParent=this,this.renderGroupChildren.push(t)}_removeRenderGroupChild(t){const e=this.renderGroupChildren.indexOf(t);e>-1&&this.renderGroupChildren.splice(e,1),t.renderGroupParent=null}addChild(t){if(this.structureDidChange=!0,t.parentRenderGroup=this,t.updateTick=-1,t.parent===this.root?t.relativeRenderGroupDepth=1:t.relativeRenderGroupDepth=t.parent.relativeRenderGroupDepth+1,t.didChange=!0,this.onChildUpdate(t),t.renderGroup){this.addRenderGroupChild(t.renderGroup);return}t._onRender&&this.addOnRender(t);const e=t.children;for(let s=0;s<e.length;s++)this.addChild(e[s])}removeChild(t){if(this.structureDidChange=!0,t._onRender&&(t.renderGroup||this.removeOnRender(t)),t.parentRenderGroup=null,t.renderGroup){this._removeRenderGroupChild(t.renderGroup);return}const e=t.children;for(let s=0;s<e.length;s++)this.removeChild(e[s])}removeChildren(t){for(let e=0;e<t.length;e++)this.removeChild(t[e])}onChildUpdate(t){let e=this.childrenToUpdate[t.relativeRenderGroupDepth];e||(e=this.childrenToUpdate[t.relativeRenderGroupDepth]={index:0,list:[]}),e.list[e.index++]=t}updateRenderable(t){t.globalDisplayStatus<7||(this.instructionSet.renderPipes[t.renderPipeId].updateRenderable(t),t.didViewUpdate=!1)}onChildViewUpdate(t){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=t}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(t){this._onRenderContainers.push(t)}removeOnRender(t){this._onRenderContainers.splice(this._onRenderContainers.indexOf(t),1)}runOnRender(t){for(let e=0;e<this._onRenderContainers.length;e++)this._onRenderContainers[e]._onRender(t)}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null}getChildren(t=[]){const e=this.root.children;for(let s=0;s<e.length;s++)this._getChildren(e[s],t);return t}_getChildren(t,e=[]){if(e.push(t),t.renderGroup)return e;const s=t.children;for(let n=0;n<s.length;n++)this._getChildren(s[n],e);return e}invalidateMatrices(){this._matrixDirty=7}get inverseWorldTransform(){return(this._matrixDirty&1)===0?this._inverseWorldTransform:(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new it),this._inverseWorldTransform.copyFrom(this.worldTransform).invert())}get textureOffsetInverseTransform(){return(this._matrixDirty&2)===0?this._textureOffsetInverseTransform:(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new it),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y))}get inverseParentTextureTransform(){if((this._matrixDirty&4)===0)return this._inverseParentTextureTransform;this._matrixDirty&=-5;const t=this._parentCacheAsTextureRenderGroup;return t?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new it),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(t.inverseWorldTransform).translate(-t._textureBounds.x,-t._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function Po(i,t,e={}){for(const s in t)!e[s]&&t[s]!==void 0&&(i[s]=t[s])}const os=new Tt(null),as=new Tt(null),hs=new Tt(null,1,1),vi=1,Eo=2,ls=4;class pt extends Nt{constructor(t={}){var e,s;super(),this.uid=_t("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new it,this.relativeGroupTransform=new it,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new Tt(this,0,0),this._scale=hs,this._pivot=as,this._skew=os,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],Po(this,t,{children:!0,parent:!0,effects:!0}),(e=t.children)==null||e.forEach(n=>this.addChild(n)),(s=t.parent)==null||s.addChild(this)}static mixin(t){at("8.8.0","Container.mixin is deprecated, please use extensions.mixin instead."),Ft.mixin(pt,t)}set _didChangeId(t){this._didViewChangeTick=t>>12&4095,this._didContainerChangeTick=t&4095}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...t){if(this.allowChildren||at(mt,"addChild: Only Containers will be allowed to add children in v8.0.0"),t.length>1){for(let n=0;n<t.length;n++)this.addChild(t[n]);return t[0]}const e=t[0],s=this.renderGroup||this.parentRenderGroup;return e.parent===this?(this.children.splice(this.children.indexOf(e),1),this.children.push(e),s&&(s.structureDidChange=!0),e):(e.parent&&e.parent.removeChild(e),this.children.push(e),this.sortableChildren&&(this.sortDirty=!0),e.parent=this,e.didChange=!0,e._updateFlags=15,s&&s.addChild(e),this.emit("childAdded",e,this,this.children.length-1),e.emit("added",this),this._didViewChangeTick++,e._zIndex!==0&&e.depthOfChildModified(),e)}removeChild(...t){if(t.length>1){for(let n=0;n<t.length;n++)this.removeChild(t[n]);return t[0]}const e=t[0],s=this.children.indexOf(e);return s>-1&&(this._didViewChangeTick++,this.children.splice(s,1),this.renderGroup?this.renderGroup.removeChild(e):this.parentRenderGroup&&this.parentRenderGroup.removeChild(e),e.parentRenderLayer&&e.parentRenderLayer.detach(e),e.parent=null,this.emit("childRemoved",e,this,s),e.emit("removed",this)),e}_onUpdate(t){t&&t===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this))}set isRenderGroup(t){!!this.renderGroup!==t&&(t?this.enableRenderGroup():this.disableRenderGroup())}get isRenderGroup(){return!!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const t=this.parentRenderGroup;t==null||t.removeChild(this),this.renderGroup=Vt.get(Ao,this),this.groupTransform=it.IDENTITY,t==null||t.addChild(this),this._updateIsSimple()}disableRenderGroup(){if(!this.renderGroup)return;const t=this.parentRenderGroup;t==null||t.removeChild(this),Vt.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,t==null||t.addChild(this),this._updateIsSimple()}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0}get worldTransform(){return this._worldTransform||(this._worldTransform=new it),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(t){this._position.x=t}get y(){return this._position.y}set y(t){this._position.y=t}get position(){return this._position}set position(t){this._position.copyFrom(t)}get rotation(){return this._rotation}set rotation(t){this._rotation!==t&&(this._rotation=t,this._onUpdate(this._skew))}get angle(){return this.rotation*Lr}set angle(t){this.rotation=t*qr}get pivot(){return this._pivot===as&&(this._pivot=new Tt(this,0,0)),this._pivot}set pivot(t){this._pivot===as&&(this._pivot=new Tt(this,0,0)),typeof t=="number"?this._pivot.set(t):this._pivot.copyFrom(t)}get skew(){return this._skew===os&&(this._skew=new Tt(this,0,0)),this._skew}set skew(t){this._skew===os&&(this._skew=new Tt(this,0,0)),this._skew.copyFrom(t)}get scale(){return this._scale===hs&&(this._scale=new Tt(this,1,1)),this._scale}set scale(t){this._scale===hs&&(this._scale=new Tt(this,0,0)),typeof t=="number"?this._scale.set(t):this._scale.copyFrom(t)}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(t){const e=this.getLocalBounds().width;this._setWidth(t,e)}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(t){const e=this.getLocalBounds().height;this._setHeight(t,e)}getSize(t){t||(t={});const e=this.getLocalBounds();return t.width=Math.abs(this.scale.x*e.width),t.height=Math.abs(this.scale.y*e.height),t}setSize(t,e){const s=this.getLocalBounds();typeof t=="object"?(e=t.height??t.width,t=t.width):e??(e=t),t!==void 0&&this._setWidth(t,s.width),e!==void 0&&this._setHeight(e,s.height)}_updateSkew(){const t=this._rotation,e=this._skew;this._cx=Math.cos(t+e._y),this._sx=Math.sin(t+e._y),this._cy=-Math.sin(t-e._x),this._sy=Math.cos(t-e._x)}updateTransform(t){return this.position.set(typeof t.x=="number"?t.x:this.position.x,typeof t.y=="number"?t.y:this.position.y),this.scale.set(typeof t.scaleX=="number"?t.scaleX||1:this.scale.x,typeof t.scaleY=="number"?t.scaleY||1:this.scale.y),this.rotation=typeof t.rotation=="number"?t.rotation:this.rotation,this.skew.set(typeof t.skewX=="number"?t.skewX:this.skew.x,typeof t.skewY=="number"?t.skewY:this.skew.y),this.pivot.set(typeof t.pivotX=="number"?t.pivotX:this.pivot.x,typeof t.pivotY=="number"?t.pivotY:this.pivot.y),this}setFromMatrix(t){t.decompose(this)}updateLocalTransform(){const t=this._didContainerChangeTick;if(this._didLocalTransformChangeId===t)return;this._didLocalTransformChangeId=t;const e=this.localTransform,s=this._scale,n=this._pivot,r=this._position,o=s._x,a=s._y,h=n._x,c=n._y;e.a=this._cx*o,e.b=this._sx*o,e.c=this._cy*a,e.d=this._sy*a,e.tx=r._x-(h*e.a+c*e.c),e.ty=r._y-(h*e.b+c*e.d)}set alpha(t){t!==this.localAlpha&&(this.localAlpha=t,this._updateFlags|=vi,this._onUpdate())}get alpha(){return this.localAlpha}set tint(t){const s=Mt.shared.setValue(t??16777215).toBgrNumber();s!==this.localColor&&(this.localColor=s,this._updateFlags|=vi,this._onUpdate())}get tint(){return ze(this.localColor)}set blendMode(t){this.localBlendMode!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Eo,this.localBlendMode=t,this._onUpdate())}get blendMode(){return this.localBlendMode}get visible(){return!!(this.localDisplayStatus&2)}set visible(t){const e=t?2:0;(this.localDisplayStatus&2)!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=ls,this.localDisplayStatus^=2,this._onUpdate())}get culled(){return!(this.localDisplayStatus&4)}set culled(t){const e=t?0:4;(this.localDisplayStatus&4)!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=ls,this.localDisplayStatus^=4,this._onUpdate())}get renderable(){return!!(this.localDisplayStatus&1)}set renderable(t){const e=t?1:0;(this.localDisplayStatus&1)!==e&&(this._updateFlags|=ls,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate())}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(t=!1){var n;if(this.destroyed)return;this.destroyed=!0;let e;if(this.children.length&&(e=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof t=="boolean"?t:t==null?void 0:t.children)&&e)for(let r=0;r<e.length;++r)e[r].destroy(t);(n=this.renderGroup)==null||n.destroy(),this.renderGroup=null}}Ft.mixin(pt,lo,mo,Co,bo,_o,fo,po,wo,ro,ao,xo,co);class Ys extends pt{constructor(t){super(t),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._bounds=new $t(0,1,0,0),this._boundsDirty=!0}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return!!this._roundPixels}set roundPixels(t){this._roundPixels=t?1:0}containsPoint(t){const e=this.bounds,{x:s,y:n}=t;return s>=e.minX&&s<=e.maxX&&n>=e.minY&&n<=e.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const t=this.renderGroup||this.parentRenderGroup;t&&t.onChildViewUpdate(this)}destroy(t){super.destroy(t),this._bounds=null}collectRenderablesSimple(t,e,s){const{renderPipes:n,renderableGC:r}=e;n.blendMode.setBlendMode(this,this.groupBlendMode,t),n[this.renderPipeId].addRenderable(this,t),r.addRenderable(this),this.didViewUpdate=!1;const a=this.children,h=a.length;for(let c=0;c<h;c++)a[c].collectRenderables(t,e,s)}}class It extends Ys{constructor(t=ht.EMPTY){t instanceof ht&&(t={texture:t});const{texture:e=ht.EMPTY,anchor:s,roundPixels:n,width:r,height:o,...a}=t;super({label:"Sprite",...a}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new Tt({_onUpdate:()=>{this.onViewUpdate()}}),s?this.anchor=s:e.defaultAnchor&&(this.anchor=e.defaultAnchor),this.texture=e,this.allowChildren=!1,this.roundPixels=n??!1,r!==void 0&&(this.width=r),o!==void 0&&(this.height=o)}static from(t,e=!1){return t instanceof ht?new It(t):new It(ht.from(t,e))}set texture(t){t||(t=ht.EMPTY);const e=this._texture;e!==t&&(e&&e.dynamic&&e.off("update",this.onViewUpdate,this),t.dynamic&&t.on("update",this.onViewUpdate,this),this._texture=t,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate())}get texture(){return this._texture}get visualBounds(){return jr(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return at("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const t=this._anchor,e=this._texture,s=this._bounds,{width:n,height:r}=e.orig;s.minX=-t._x*n,s.maxX=s.minX+n,s.minY=-t._y*r,s.maxY=s.minY+r}destroy(t=!1){if(super.destroy(t),typeof t=="boolean"?t:t==null?void 0:t.texture){const s=typeof t=="boolean"?t:t==null?void 0:t.textureSource;this._texture.destroy(s)}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null}get anchor(){return this._anchor}set anchor(t){typeof t=="number"?this._anchor.set(t):this._anchor.copyFrom(t)}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(t){this._setWidth(t,this._texture.orig.width),this._width=t}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(t){this._setHeight(t,this._texture.orig.height),this._height=t}getSize(t){return t||(t={}),t.width=Math.abs(this.scale.x)*this._texture.orig.width,t.height=Math.abs(this.scale.y)*this._texture.orig.height,t}setSize(t,e){typeof t=="object"?(e=t.height??t.width,t=t.width):e??(e=t),t!==void 0&&this._setWidth(t,this._texture.orig.width),e!==void 0&&this._setHeight(e,this._texture.orig.height)}}const ko=new $t;function Bn(i,t,e){const s=ko;i.measurable=!0,vn(i,e,s),t.addBoundsMask(s),i.measurable=!1}function Rn(i,t,e){const s=Yt.get();i.measurable=!0;const n=vt.get().identity(),r=Fn(i,e,n);Pn(i,s,r),i.measurable=!1,t.addBoundsMask(s),vt.return(n),Yt.return(s)}function Fn(i,t,e){return i?(i!==t&&(Fn(i.parent,t,e),i.updateLocalTransform(),e.append(i.localTransform)),e):(kt("Mask bounds, renderable is not inside the root container"),e)}class Gn{constructor(t){this.priority=0,this.inverse=!1,this.pipe="alphaMask",t!=null&&t.mask&&this.init(t.mask)}init(t){this.mask=t,this.renderMaskToTexture=!(t instanceof It),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask=null}addBounds(t,e){this.inverse||Bn(this.mask,t,e)}addLocalBounds(t,e){Rn(this.mask,t,e)}containsPoint(t,e){const s=this.mask;return e(s,t)}destroy(){this.reset()}static test(t){return t instanceof It}}Gn.extension=rt.MaskEffect;class Dn{constructor(t){this.priority=0,this.pipe="colorMask",t!=null&&t.mask&&this.init(t.mask)}init(t){this.mask=t}destroy(){}static test(t){return typeof t=="number"}}Dn.extension=rt.MaskEffect;class Hn{constructor(t){this.priority=0,this.pipe="stencilMask",t!=null&&t.mask&&this.init(t.mask)}init(t){this.mask=t,this.mask.includeInBuild=!1,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null}addBounds(t,e){Bn(this.mask,t,e)}addLocalBounds(t,e){Rn(this.mask,t,e)}containsPoint(t,e){const s=this.mask;return e(s,t)}destroy(){this.reset()}static test(t){return t instanceof pt}}Hn.extension=rt.MaskEffect;const Io={createCanvas:(i,t)=>{const e=document.createElement("canvas");return e.width=i,e.height=t,e},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(i,t)=>fetch(i,t),parseXML:i=>new DOMParser().parseFromString(i,"text/xml")};let Ti=Io;const Ht={get(){return Ti},set(i){Ti=i}};class $n extends Wt{constructor(t){t.resource||(t.resource=Ht.get().createCanvas()),t.width||(t.width=t.resource.width,t.autoDensity||(t.width/=t.resolution)),t.height||(t.height=t.resource.height,t.autoDensity||(t.height/=t.resolution)),super(t),this.uploadMethodId="image",this.autoDensity=t.autoDensity,this.resizeCanvas(),this.transparent=!!t.transparent}resizeCanvas(){this.autoDensity&&"style"in this.resource&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight)}resize(t=this.width,e=this.height,s=this._resolution){const n=super.resize(t,e,s);return n&&this.resizeCanvas(),n}static test(t){return globalThis.HTMLCanvasElement&&t instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&t instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}$n.extension=rt.TextureSource;class Ye extends Wt{constructor(t){if(t.resource&&globalThis.HTMLImageElement&&t.resource instanceof HTMLImageElement){const e=Ht.get().createCanvas(t.resource.width,t.resource.height);e.getContext("2d").drawImage(t.resource,0,0,t.resource.width,t.resource.height),t.resource=e,kt("ImageSource: Image element passed, converting to canvas. Use CanvasSource instead.")}super(t),this.uploadMethodId="image",this.autoGarbageCollect=!0}static test(t){return globalThis.HTMLImageElement&&t instanceof HTMLImageElement||typeof ImageBitmap<"u"&&t instanceof ImageBitmap||globalThis.VideoFrame&&t instanceof VideoFrame}}Ye.extension=rt.TextureSource;var Bs=(i=>(i[i.INTERACTION=50]="INTERACTION",i[i.HIGH=25]="HIGH",i[i.NORMAL=0]="NORMAL",i[i.LOW=-25]="LOW",i[i.UTILITY=-50]="UTILITY",i))(Bs||{});class cs{constructor(t,e=null,s=0,n=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=t,this._context=e,this.priority=s,this._once=n}match(t,e=null){return this._fn===t&&this._context===e}emit(t){this._fn&&(this._context?this._fn.call(this._context,t):this._fn(t));const e=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),e}connect(t){this.previous=t,t.next&&(t.next.previous=this),this.next=t.next,t.next=this}destroy(t=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const e=this.next;return this.next=t?null:e,this.previous=null,e}}const Un=class Et{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new cs(null,null,1/0),this.deltaMS=1/Et.targetFPMS,this.elapsedMS=1/Et.targetFPMS,this._tick=t=>{this._requestId=null,this.started&&(this.update(t),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(t,e,s=Bs.NORMAL){return this._addListener(new cs(t,e,s))}addOnce(t,e,s=Bs.NORMAL){return this._addListener(new cs(t,e,s,!0))}_addListener(t){let e=this._head.next,s=this._head;if(!e)t.connect(s);else{for(;e;){if(t.priority>e.priority){t.connect(s);break}s=e,e=e.next}t.previous||t.connect(s)}return this._startIfPossible(),this}remove(t,e){let s=this._head.next;for(;s;)s.match(t,e)?s=s.destroy():s=s.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let t=0,e=this._head;for(;e=e.next;)t++;return t}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let t=this._head.next;for(;t;)t=t.destroy(!0);this._head.destroy(),this._head=null}}update(t=performance.now()){let e;if(t>this.lastTime){if(e=this.elapsedMS=t-this.lastTime,e>this._maxElapsedMS&&(e=this._maxElapsedMS),e*=this.speed,this._minElapsedMS){const r=t-this._lastFrame|0;if(r<this._minElapsedMS)return;this._lastFrame=t-r%this._minElapsedMS}this.deltaMS=e,this.deltaTime=this.deltaMS*Et.targetFPMS;const s=this._head;let n=s.next;for(;n;)n=n.emit(this);s.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=t}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(t){const e=Math.min(this.maxFPS,t),s=Math.min(Math.max(0,e)/1e3,Et.targetFPMS);this._maxElapsedMS=1/s}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(t){if(t===0)this._minElapsedMS=0;else{const e=Math.max(this.minFPS,t);this._minElapsedMS=1/(e/1e3)}}static get shared(){if(!Et._shared){const t=Et._shared=new Et;t.autoStart=!0,t._protected=!0}return Et._shared}static get system(){if(!Et._system){const t=Et._system=new Et;t.autoStart=!0,t._protected=!0}return Et._system}};Un.targetFPMS=.06;let Fe=Un,us;async function Bo(){return us??(us=(async()=>{var o;const t=document.createElement("canvas").getContext("webgl");if(!t)return"premultiply-alpha-on-upload";const e=await new Promise(a=>{const h=document.createElement("video");h.onloadeddata=()=>a(h),h.onerror=()=>a(null),h.autoplay=!1,h.crossOrigin="anonymous",h.preload="auto",h.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",h.load()});if(!e)return"premultiply-alpha-on-upload";const s=t.createTexture();t.bindTexture(t.TEXTURE_2D,s);const n=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,n),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,s,0),t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),t.pixelStorei(t.UNPACK_COLORSPACE_CONVERSION_WEBGL,t.NONE),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,e);const r=new Uint8Array(4);return t.readPixels(0,0,1,1,t.RGBA,t.UNSIGNED_BYTE,r),t.deleteFramebuffer(n),t.deleteTexture(s),(o=t.getExtension("WEBGL_lose_context"))==null||o.loseContext(),r[0]<=r[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),us}const Ke=class Ln extends Wt{constructor(t){super(t),this.isReady=!1,this.uploadMethodId="video",t={...Ln.defaultOptions,...t},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=t.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=t.autoPlay!==!1,this.alphaMode=t.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),t.autoLoad!==!1&&this.load()}updateFrame(){if(!this.destroyed){if(this._updateFPS){const t=Fe.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-t)}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update()}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback)}get isValid(){return!!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const t=this.resource,e=this.options;return(t.readyState===t.HAVE_ENOUGH_DATA||t.readyState===t.HAVE_FUTURE_DATA)&&t.width&&t.height&&(t.complete=!0),t.addEventListener("play",this._onPlayStart),t.addEventListener("pause",this._onPlayStop),t.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(e.preload||t.addEventListener("canplay",this._onCanPlay),t.addEventListener("canplaythrough",this._onCanPlayThrough),t.addEventListener("error",this._onError,!0)),this.alphaMode=await Bo(),this._load=new Promise((s,n)=>{this.isValid?s(this):(this._resolve=s,this._reject=n,e.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${e.preloadTimeoutMs}ms`))})),t.load())}),this._load}_onError(t){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",t),this._reject&&(this._reject(t),this._reject=null,this._resolve=null)}_isSourcePlaying(){const t=this.resource;return!t.paused&&!t.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0)}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady()}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady()}_mediaReady(){const t=this.resource;this.isValid&&(this.isReady=!0,this.resize(t.videoWidth,t.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play()}destroy(){this._configureAutoUpdate();const t=this.resource;t&&(t.removeEventListener("play",this._onPlayStart),t.removeEventListener("pause",this._onPlayStop),t.removeEventListener("seeked",this._onSeeked),t.removeEventListener("canplay",this._onCanPlay),t.removeEventListener("canplaythrough",this._onCanPlayThrough),t.removeEventListener("error",this._onError,!0),t.pause(),t.src="",t.load()),super.destroy()}get autoUpdate(){return this._autoUpdate}set autoUpdate(t){t!==this._autoUpdate&&(this._autoUpdate=t,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(t){t!==this._updateFPS&&(this._updateFPS=t,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(Fe.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(Fe.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(Fe.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(t){return globalThis.HTMLVideoElement&&t instanceof HTMLVideoElement}};Ke.extension=rt.TextureSource;Ke.defaultOptions={...Wt.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};Ke.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let Ro=Ke;const ce=(i,t,e=!1)=>(Array.isArray(i)||(i=[i]),t?i.map(s=>typeof s=="string"||e?t(s):s):i);class Fo{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(t){return this._cache.has(t)}get(t){const e=this._cache.get(t);return e||kt(`[Assets] Asset id ${t} was not found in the Cache`),e}set(t,e){const s=ce(t);let n;for(let h=0;h<this.parsers.length;h++){const c=this.parsers[h];if(c.test(e)){n=c.getCacheableAssets(s,e);break}}const r=new Map(Object.entries(n||{}));n||s.forEach(h=>{r.set(h,e)});const o=[...r.keys()],a={cacheKeys:o,keys:s};s.forEach(h=>{this._cacheMap.set(h,a)}),o.forEach(h=>{const c=n?n[h]:e;this._cache.has(h)&&this._cache.get(h)!==c&&kt("[Cache] already has key:",h),this._cache.set(h,r.get(h))})}remove(t){if(!this._cacheMap.has(t)){kt(`[Assets] Asset id ${t} was not found in the Cache`);return}const e=this._cacheMap.get(t);e.cacheKeys.forEach(n=>{this._cache.delete(n)}),e.keys.forEach(n=>{this._cacheMap.delete(n)})}get parsers(){return this._parsers}}const ue=new Fo,Rs=[];Ft.handleByList(rt.TextureSource,Rs);function qn(i={}){const t=i&&i.resource,e=t?i.resource:i,s=t?i:{resource:i};for(let n=0;n<Rs.length;n++){const r=Rs[n];if(r.test(e))return new r(s)}throw new Error(`Could not find a source type for resource: ${s.resource}`)}function Go(i={},t=!1){const e=i&&i.resource,s=e?i.resource:i,n=e?i:{resource:i};if(!t&&ue.has(s))return ue.get(s);const r=new ht({source:qn(n)});return r.on("destroy",()=>{ue.has(s)&&ue.remove(s)}),t||ue.set(s,r),r}function Do(i,t=!1){return typeof i=="string"?ue.get(i):i instanceof Wt?new ht({source:i}):Go(i,t)}ht.from=Do;Wt.from=qn;Ft.add(Gn,Dn,Hn,Ro,Ye,$n,Ws);var zn=(i=>(i[i.Low=0]="Low",i[i.Normal=1]="Normal",i[i.High=2]="High",i))(zn||{});function Gt(i){if(typeof i!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(i)}`)}function ge(i){return i.split("?")[0].split("#")[0]}function Ho(i){return i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function $o(i,t,e){return i.replace(new RegExp(Ho(t),"g"),e)}function Uo(i,t){let e="",s=0,n=-1,r=0,o=-1;for(let a=0;a<=i.length;++a){if(a<i.length)o=i.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(n===a-1||r===1))if(n!==a-1&&r===2){if(e.length<2||s!==2||e.charCodeAt(e.length-1)!==46||e.charCodeAt(e.length-2)!==46){if(e.length>2){const h=e.lastIndexOf("/");if(h!==e.length-1){h===-1?(e="",s=0):(e=e.slice(0,h),s=e.length-1-e.lastIndexOf("/")),n=a,r=0;continue}}else if(e.length===2||e.length===1){e="",s=0,n=a,r=0;continue}}}else e.length>0?e+=`/${i.slice(n+1,a)}`:e=i.slice(n+1,a),s=a-n-1;n=a,r=0}else o===46&&r!==-1?++r:r=-1}return e}const Te={toPosix(i){return $o(i,"\\","/")},isUrl(i){return/^https?:/.test(this.toPosix(i))},isDataUrl(i){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(i)},isBlobUrl(i){return i.startsWith("blob:")},hasProtocol(i){return/^[^/:]+:/.test(this.toPosix(i))},getProtocol(i){Gt(i),i=this.toPosix(i);const t=/^file:\/\/\//.exec(i);if(t)return t[0];const e=/^[^/:]+:\/{0,2}/.exec(i);return e?e[0]:""},toAbsolute(i,t,e){if(Gt(i),this.isDataUrl(i)||this.isBlobUrl(i))return i;const s=ge(this.toPosix(t??Ht.get().getBaseUrl())),n=ge(this.toPosix(e??this.rootname(s)));return i=this.toPosix(i),i.startsWith("/")?Te.join(n,i.slice(1)):this.isAbsolute(i)?i:this.join(s,i)},normalize(i){if(Gt(i),i.length===0)return".";if(this.isDataUrl(i)||this.isBlobUrl(i))return i;i=this.toPosix(i);let t="";const e=i.startsWith("/");this.hasProtocol(i)&&(t=this.rootname(i),i=i.slice(t.length));const s=i.endsWith("/");return i=Uo(i),i.length>0&&s&&(i+="/"),e?`/${i}`:t+i},isAbsolute(i){return Gt(i),i=this.toPosix(i),this.hasProtocol(i)?!0:i.startsWith("/")},join(...i){if(i.length===0)return".";let t;for(let e=0;e<i.length;++e){const s=i[e];if(Gt(s),s.length>0)if(t===void 0)t=s;else{const n=i[e-1]??"";this.joinExtensions.includes(this.extname(n).toLowerCase())?t+=`/../${s}`:t+=`/${s}`}}return t===void 0?".":this.normalize(t)},dirname(i){if(Gt(i),i.length===0)return".";i=this.toPosix(i);let t=i.charCodeAt(0);const e=t===47;let s=-1,n=!0;const r=this.getProtocol(i),o=i;i=i.slice(r.length);for(let a=i.length-1;a>=1;--a)if(t=i.charCodeAt(a),t===47){if(!n){s=a;break}}else n=!1;return s===-1?e?"/":this.isUrl(o)?r+i:r:e&&s===1?"//":r+i.slice(0,s)},rootname(i){Gt(i),i=this.toPosix(i);let t="";if(i.startsWith("/")?t="/":t=this.getProtocol(i),this.isUrl(i)){const e=i.indexOf("/",t.length);e!==-1?t=i.slice(0,e):t=i,t.endsWith("/")||(t+="/")}return t},basename(i,t){Gt(i),t&&Gt(t),i=ge(this.toPosix(i));let e=0,s=-1,n=!0,r;if(t!==void 0&&t.length>0&&t.length<=i.length){if(t.length===i.length&&t===i)return"";let o=t.length-1,a=-1;for(r=i.length-1;r>=0;--r){const h=i.charCodeAt(r);if(h===47){if(!n){e=r+1;break}}else a===-1&&(n=!1,a=r+1),o>=0&&(h===t.charCodeAt(o)?--o===-1&&(s=r):(o=-1,s=a))}return e===s?s=a:s===-1&&(s=i.length),i.slice(e,s)}for(r=i.length-1;r>=0;--r)if(i.charCodeAt(r)===47){if(!n){e=r+1;break}}else s===-1&&(n=!1,s=r+1);return s===-1?"":i.slice(e,s)},extname(i){Gt(i),i=ge(this.toPosix(i));let t=-1,e=0,s=-1,n=!0,r=0;for(let o=i.length-1;o>=0;--o){const a=i.charCodeAt(o);if(a===47){if(!n){e=o+1;break}continue}s===-1&&(n=!1,s=o+1),a===46?t===-1?t=o:r!==1&&(r=1):t!==-1&&(r=-1)}return t===-1||s===-1||r===0||r===1&&t===s-1&&t===e+1?"":i.slice(t,s)},parse(i){Gt(i);const t={root:"",dir:"",base:"",ext:"",name:""};if(i.length===0)return t;i=ge(this.toPosix(i));let e=i.charCodeAt(0);const s=this.isAbsolute(i);let n;t.root=this.rootname(i),s||this.hasProtocol(i)?n=1:n=0;let r=-1,o=0,a=-1,h=!0,c=i.length-1,l=0;for(;c>=n;--c){if(e=i.charCodeAt(c),e===47){if(!h){o=c+1;break}continue}a===-1&&(h=!1,a=c+1),e===46?r===-1?r=c:l!==1&&(l=1):r!==-1&&(l=-1)}return r===-1||a===-1||l===0||l===1&&r===a-1&&r===o+1?a!==-1&&(o===0&&s?t.base=t.name=i.slice(1,a):t.base=t.name=i.slice(o,a)):(o===0&&s?(t.name=i.slice(1,r),t.base=i.slice(1,a)):(t.name=i.slice(o,r),t.base=i.slice(o,a)),t.ext=i.slice(r,a)),t.dir=this.dirname(i),t},sep:"/",delimiter:":",joinExtensions:[".html"]};function Nn(i,t,e,s,n){const r=t[e];for(let o=0;o<r.length;o++){const a=r[o];e<t.length-1?Nn(i.replace(s[e],a),t,e+1,s,n):n.push(i.replace(s[e],a))}}function Lo(i){const t=/\{(.*?)\}/g,e=i.match(t),s=[];if(e){const n=[];e.forEach(r=>{const o=r.substring(1,r.length-1).split(",");n.push(o)}),Nn(i,n,0,e,s)}else s.push(i);return s}const Ai=i=>!Array.isArray(i);class Wn{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(t,e)=>`${t}${this._bundleIdConnector}${e}`,extractAssetIdFromBundle:(t,e)=>e.replace(`${t}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(t){if(this._bundleIdConnector=t.connector??this._bundleIdConnector,this._createBundleAssetId=t.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=t.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...t){t.forEach(e=>{this._preferredOrder.push(e),e.priority||(e.priority=Object.keys(e.params))}),this._resolverHash={}}set basePath(t){this._basePath=t}get basePath(){return this._basePath}set rootPath(t){this._rootPath=t}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(t){if(typeof t=="string")this._defaultSearchParams=t;else{const e=t;this._defaultSearchParams=Object.keys(e).map(s=>`${encodeURIComponent(s)}=${encodeURIComponent(e[s])}`).join("&")}}getAlias(t){const{alias:e,src:s}=t;return ce(e||s,r=>typeof r=="string"?r:Array.isArray(r)?r.map(o=>(o==null?void 0:o.src)??o):r!=null&&r.src?r.src:r,!0)}addManifest(t){this._manifest&&kt("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=t,t.bundles.forEach(e=>{this.addBundle(e.name,e.assets)})}addBundle(t,e){const s=[];let n=e;Array.isArray(e)||(n=Object.entries(e).map(([r,o])=>typeof o=="string"||Array.isArray(o)?{alias:r,src:o}:{alias:r,...o})),n.forEach(r=>{const o=r.src,a=r.alias;let h;if(typeof a=="string"){const c=this._createBundleAssetId(t,a);s.push(c),h=[a,c]}else{const c=a.map(l=>this._createBundleAssetId(t,l));s.push(...c),h=[...a,...c]}this.add({...r,alias:h,src:o})}),this._bundles[t]=s}add(t){const e=[];Array.isArray(t)?e.push(...t):e.push(t);let s;s=r=>{this.hasKey(r)&&kt(`[Resolver] already has key: ${r} overwriting`)},ce(e).forEach(r=>{const{src:o}=r;let{data:a,format:h,loadParser:c}=r;const l=ce(o).map(d=>typeof d=="string"?Lo(d):Array.isArray(d)?d:[d]),u=this.getAlias(r);Array.isArray(u)?u.forEach(s):s(u);const f=[];l.forEach(d=>{d.forEach(x=>{let _={};if(typeof x!="object"){_.src=x;for(let g=0;g<this._parsers.length;g++){const b=this._parsers[g];if(b.test(x)){_=b.parse(x);break}}}else a=x.data??a,h=x.format??h,c=x.loadParser??c,_={..._,...x};if(!u)throw new Error(`[Resolver] alias is undefined for this asset: ${_.src}`);_=this._buildResolvedAsset(_,{aliases:u,data:a,format:h,loadParser:c}),f.push(_)})}),u.forEach(d=>{this._assetMap[d]=f})})}resolveBundle(t){const e=Ai(t);t=ce(t);const s={};return t.forEach(n=>{const r=this._bundles[n];if(r){const o=this.resolve(r),a={};for(const h in o){const c=o[h];a[this._extractAssetIdFromBundle(n,h)]=c}s[n]=a}}),e?s[t[0]]:s}resolveUrl(t){const e=this.resolve(t);if(typeof t!="string"){const s={};for(const n in e)s[n]=e[n].src;return s}return e.src}resolve(t){const e=Ai(t);t=ce(t);const s={};return t.forEach(n=>{if(!this._resolverHash[n])if(this._assetMap[n]){let r=this._assetMap[n];const o=this._getPreferredOrder(r);o==null||o.priority.forEach(a=>{o.params[a].forEach(h=>{const c=r.filter(l=>l[a]?l[a]===h:!1);c.length&&(r=c)})}),this._resolverHash[n]=r[0]}else this._resolverHash[n]=this._buildResolvedAsset({alias:[n],src:n},{});s[n]=this._resolverHash[n]}),e?s[t[0]]:s}hasKey(t){return!!this._assetMap[t]}hasBundle(t){return!!this._bundles[t]}_getPreferredOrder(t){for(let e=0;e<t.length;e++){const s=t[e],n=this._preferredOrder.find(r=>r.params.format.includes(s.format));if(n)return n}return this._preferredOrder[0]}_appendDefaultSearchParams(t){if(!this._defaultSearchParams)return t;const e=/\?/.test(t)?"&":"?";return`${t}${e}${this._defaultSearchParams}`}_buildResolvedAsset(t,e){const{aliases:s,data:n,loadParser:r,format:o}=e;return(this._basePath||this._rootPath)&&(t.src=Te.toAbsolute(t.src,this._basePath,this._rootPath)),t.alias=s??t.alias??[t.src],t.src=this._appendDefaultSearchParams(t.src),t.data={...n||{},...t.data},t.loadParser=r??t.loadParser,t.format=o??t.format??qo(t.src),t}}Wn.RETINA_PREFIX=/@([0-9\.]+)x/;function qo(i){return i.split(".").pop().split("?").shift().split("#").shift()}const Pi=(i,t)=>{const e=t.split("?")[1];return e&&(i+=`?${e}`),i},On=class we{constructor(t,e){this.linkedSheets=[],this._texture=t instanceof ht?t:null,this.textureSource=t.source,this.textures={},this.animations={},this.data=e;const s=parseFloat(e.meta.scale);s?(this.resolution=s,t.source.resolution=this.resolution):this.resolution=t.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}parse(){return new Promise(t=>{this._callback=t,this._batchIndex=0,this._frameKeys.length<=we.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(t){let e=t;const s=we.BATCH_SIZE;for(;e-t<s&&e<this._frameKeys.length;){const n=this._frameKeys[e],r=this._frames[n],o=r.frame;if(o){let a=null,h=null;const c=r.trimmed!==!1&&r.sourceSize?r.sourceSize:r.frame,l=new Ct(0,0,Math.floor(c.w)/this.resolution,Math.floor(c.h)/this.resolution);r.rotated?a=new Ct(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.h)/this.resolution,Math.floor(o.w)/this.resolution):a=new Ct(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution),r.trimmed!==!1&&r.spriteSourceSize&&(h=new Ct(Math.floor(r.spriteSourceSize.x)/this.resolution,Math.floor(r.spriteSourceSize.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution)),this.textures[n]=new ht({source:this.textureSource,frame:a,orig:l,trim:h,rotate:r.rotated?2:0,defaultAnchor:r.anchor,defaultBorders:r.borders,label:n.toString()})}e++}}_processAnimations(){const t=this.data.animations||{};for(const e in t){this.animations[e]=[];for(let s=0;s<t[e].length;s++){const n=t[e][s];this.animations[e].push(this.textures[n])}}}_parseComplete(){const t=this._callback;this._callback=null,this._batchIndex=0,t.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*we.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*we.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(t=!1){var e;for(const s in this.textures)this.textures[s].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,t&&((e=this._texture)==null||e.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[]}};On.BATCH_SIZE=1e3;let Ei=On;const zo=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function Vn(i,t,e){const s={};if(i.forEach(n=>{s[n]=t}),Object.keys(t.textures).forEach(n=>{s[n]=t.textures[n]}),!e){const n=Te.dirname(i[0]);t.linkedSheets.forEach((r,o)=>{const a=Vn([`${n}/${t.data.meta.related_multi_packs[o]}`],r,!0);Object.assign(s,a)})}return s}const No={extension:rt.Asset,cache:{test:i=>i instanceof Ei,getCacheableAssets:(i,t)=>Vn(i,t,!1)},resolver:{extension:{type:rt.ResolveParser,name:"resolveSpritesheet"},test:i=>{const e=i.split("?")[0].split("."),s=e.pop(),n=e.pop();return s==="json"&&zo.includes(n)},parse:i=>{var e;const t=i.split(".");return{resolution:parseFloat(((e=Wn.RETINA_PREFIX.exec(i))==null?void 0:e[1])??"1"),format:t[t.length-2],src:i}}},loader:{name:"spritesheetLoader",extension:{type:rt.LoadParser,priority:zn.Normal,name:"spritesheetLoader"},async testParse(i,t){return Te.extname(t.src).toLowerCase()===".json"&&!!i.frames},async parse(i,t,e){var l,u;const{texture:s,imageFilename:n,textureOptions:r}=(t==null?void 0:t.data)??{};let o=Te.dirname(t.src);o&&o.lastIndexOf("/")!==o.length-1&&(o+="/");let a;if(s instanceof ht)a=s;else{const f=Pi(o+(n??i.meta.image),t.src);a=(await e.load([{src:f,data:r}]))[f]}const h=new Ei(a.source,i);await h.parse();const c=(l=i==null?void 0:i.meta)==null?void 0:l.related_multi_packs;if(Array.isArray(c)){const f=[];for(const x of c){if(typeof x!="string")continue;let _=o+x;(u=t.data)!=null&&u.ignoreMultiPack||(_=Pi(_,t.src),f.push(e.load({src:_,data:{textureOptions:r,ignoreMultiPack:!0}})))}const d=await Promise.all(f);h.linkedSheets=d,d.forEach(x=>{x.linkedSheets=[h].concat(h.linkedSheets.filter(_=>_!==x))})}return h},async unload(i,t,e){await e.unload(i.textureSource._sourceOrigin),i.destroy(!1)}}};Ft.add(No);const ds=Object.create(null),ki=Object.create(null);function Xs(i,t){let e=ki[i];return e===void 0&&(ds[t]===void 0&&(ds[t]=1),ki[i]=e=ds[t]++),e}let ae;function Yn(){return(!ae||ae!=null&&ae.isContextLost())&&(ae=Ht.get().createCanvas().getContext("webgl",{})),ae}let Ge;function Wo(){if(!Ge){Ge="mediump";const i=Yn();i&&i.getShaderPrecisionFormat&&(Ge=i.getShaderPrecisionFormat(i.FRAGMENT_SHADER,i.HIGH_FLOAT).precision?"highp":"mediump")}return Ge}function Oo(i,t,e){return t?i:e?(i=i.replace("out vec4 finalColor;",""),`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in varying
        #define finalColor gl_FragColor
        #define texture texture2D
        #endif
        ${i}
        `):`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in attribute
        #define out varying
        #endif
        ${i}
        `}function Vo(i,t,e){const s=e?t.maxSupportedFragmentPrecision:t.maxSupportedVertexPrecision;if(i.substring(0,9)!=="precision"){let n=e?t.requestedFragmentPrecision:t.requestedVertexPrecision;return n==="highp"&&s!=="highp"&&(n="mediump"),`precision ${n} float;
${i}`}else if(s!=="highp"&&i.substring(0,15)==="precision highp")return i.replace("precision highp","precision mediump");return i}function Yo(i,t){return t?`#version 300 es
${i}`:i}const Xo={},jo={};function Ko(i,{name:t="pixi-program"},e=!0){t=t.replace(/\s+/g,"-"),t+=e?"-fragment":"-vertex";const s=e?Xo:jo;return s[t]?(s[t]++,t+=`-${s[t]}`):s[t]=1,i.indexOf("#define SHADER_NAME")!==-1?i:`${`#define SHADER_NAME ${t}`}
${i}`}function Zo(i,t){return t?i.replace("#version 300 es",""):i}const fs={stripVersion:Zo,ensurePrecision:Vo,addProgramDefines:Oo,setProgramName:Ko,insertVersion:Yo},ps=Object.create(null),Xn=class Fs{constructor(t){t={...Fs.defaultOptions,...t};const e=t.fragment.indexOf("#version 300 es")!==-1,s={stripVersion:e,ensurePrecision:{requestedFragmentPrecision:t.preferredFragmentPrecision,requestedVertexPrecision:t.preferredVertexPrecision,maxSupportedVertexPrecision:"highp",maxSupportedFragmentPrecision:Wo()},setProgramName:{name:t.name},addProgramDefines:e,insertVersion:e};let n=t.fragment,r=t.vertex;Object.keys(fs).forEach(o=>{const a=s[o];n=fs[o](n,a,!0),r=fs[o](r,a,!1)}),this.fragment=n,this.vertex=r,this.transformFeedbackVaryings=t.transformFeedbackVaryings,this._key=Xs(`${this.vertex}:${this.fragment}`,"gl-program")}destroy(){this.fragment=null,this.vertex=null,this._attributeData=null,this._uniformData=null,this._uniformBlockData=null,this.transformFeedbackVaryings=null}static from(t){const e=`${t.vertex}:${t.fragment}`;return ps[e]||(ps[e]=new Fs(t)),ps[e]}};Xn.defaultOptions={preferredVertexPrecision:"highp",preferredFragmentPrecision:"mediump"};let jn=Xn;const Ii={uint8x2:{size:2,stride:2,normalised:!1},uint8x4:{size:4,stride:4,normalised:!1},sint8x2:{size:2,stride:2,normalised:!1},sint8x4:{size:4,stride:4,normalised:!1},unorm8x2:{size:2,stride:2,normalised:!0},unorm8x4:{size:4,stride:4,normalised:!0},snorm8x2:{size:2,stride:2,normalised:!0},snorm8x4:{size:4,stride:4,normalised:!0},uint16x2:{size:2,stride:4,normalised:!1},uint16x4:{size:4,stride:8,normalised:!1},sint16x2:{size:2,stride:4,normalised:!1},sint16x4:{size:4,stride:8,normalised:!1},unorm16x2:{size:2,stride:4,normalised:!0},unorm16x4:{size:4,stride:8,normalised:!0},snorm16x2:{size:2,stride:4,normalised:!0},snorm16x4:{size:4,stride:8,normalised:!0},float16x2:{size:2,stride:4,normalised:!1},float16x4:{size:4,stride:8,normalised:!1},float32:{size:1,stride:4,normalised:!1},float32x2:{size:2,stride:8,normalised:!1},float32x3:{size:3,stride:12,normalised:!1},float32x4:{size:4,stride:16,normalised:!1},uint32:{size:1,stride:4,normalised:!1},uint32x2:{size:2,stride:8,normalised:!1},uint32x3:{size:3,stride:12,normalised:!1},uint32x4:{size:4,stride:16,normalised:!1},sint32:{size:1,stride:4,normalised:!1},sint32x2:{size:2,stride:8,normalised:!1},sint32x3:{size:3,stride:12,normalised:!1},sint32x4:{size:4,stride:16,normalised:!1}};function Qo(i){return Ii[i]??Ii.float32}const Jo={f32:"float32","vec2<f32>":"float32x2","vec3<f32>":"float32x3","vec4<f32>":"float32x4",vec2f:"float32x2",vec3f:"float32x3",vec4f:"float32x4",i32:"sint32","vec2<i32>":"sint32x2","vec3<i32>":"sint32x3","vec4<i32>":"sint32x4",u32:"uint32","vec2<u32>":"uint32x2","vec3<u32>":"uint32x3","vec4<u32>":"uint32x4",bool:"uint32","vec2<bool>":"uint32x2","vec3<bool>":"uint32x3","vec4<bool>":"uint32x4"};function ta({source:i,entryPoint:t}){const e={},s=i.indexOf(`fn ${t}`);if(s!==-1){const n=i.indexOf("->",s);if(n!==-1){const r=i.substring(s,n),o=/@location\((\d+)\)\s+([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>]+)(?:,|\s|$)/g;let a;for(;(a=o.exec(r))!==null;){const h=Jo[a[3]]??"float32";e[a[2]]={location:parseInt(a[1],10),format:h,stride:Qo(h).stride,offset:0,instance:!1,start:0}}}}return e}function gs(i){var u,f;const t=/(^|[^/])@(group|binding)\(\d+\)[^;]+;/g,e=/@group\((\d+)\)/,s=/@binding\((\d+)\)/,n=/var(<[^>]+>)? (\w+)/,r=/:\s*(\w+)/,o=/struct\s+(\w+)\s*{([^}]+)}/g,a=/(\w+)\s*:\s*([\w\<\>]+)/g,h=/struct\s+(\w+)/,c=(u=i.match(t))==null?void 0:u.map(d=>({group:parseInt(d.match(e)[1],10),binding:parseInt(d.match(s)[1],10),name:d.match(n)[2],isUniform:d.match(n)[1]==="<uniform>",type:d.match(r)[1]}));if(!c)return{groups:[],structs:[]};const l=((f=i.match(o))==null?void 0:f.map(d=>{const x=d.match(h)[1],_=d.match(a).reduce((g,b)=>{const[w,S]=b.split(":");return g[w.trim()]=S.trim(),g},{});return _?{name:x,members:_}:null}).filter(({name:d})=>c.some(x=>x.type===d)))??[];return{groups:c,structs:l}}var Se=(i=>(i[i.VERTEX=1]="VERTEX",i[i.FRAGMENT=2]="FRAGMENT",i[i.COMPUTE=4]="COMPUTE",i))(Se||{});function ea({groups:i}){const t=[];for(let e=0;e<i.length;e++){const s=i[e];t[s.group]||(t[s.group]=[]),s.isUniform?t[s.group].push({binding:s.binding,visibility:Se.VERTEX|Se.FRAGMENT,buffer:{type:"uniform"}}):s.type==="sampler"?t[s.group].push({binding:s.binding,visibility:Se.FRAGMENT,sampler:{type:"filtering"}}):s.type==="texture_2d"&&t[s.group].push({binding:s.binding,visibility:Se.FRAGMENT,texture:{sampleType:"float",viewDimension:"2d",multisampled:!1}})}return t}function sa({groups:i}){const t=[];for(let e=0;e<i.length;e++){const s=i[e];t[s.group]||(t[s.group]={}),t[s.group][s.name]=s.binding}return t}function ia(i,t){const e=new Set,s=new Set,n=[...i.structs,...t.structs].filter(o=>e.has(o.name)?!1:(e.add(o.name),!0)),r=[...i.groups,...t.groups].filter(o=>{const a=`${o.name}-${o.binding}`;return s.has(a)?!1:(s.add(a),!0)});return{structs:n,groups:r}}const ms=Object.create(null);class Ze{constructor(t){var a,h;this._layoutKey=0,this._attributeLocationsKey=0;const{fragment:e,vertex:s,layout:n,gpuLayout:r,name:o}=t;if(this.name=o,this.fragment=e,this.vertex=s,e.source===s.source){const c=gs(e.source);this.structsAndGroups=c}else{const c=gs(s.source),l=gs(e.source);this.structsAndGroups=ia(c,l)}this.layout=n??sa(this.structsAndGroups),this.gpuLayout=r??ea(this.structsAndGroups),this.autoAssignGlobalUniforms=((a=this.layout[0])==null?void 0:a.globalUniforms)!==void 0,this.autoAssignLocalUniforms=((h=this.layout[1])==null?void 0:h.localUniforms)!==void 0,this._generateProgramKey()}_generateProgramKey(){const{vertex:t,fragment:e}=this,s=t.source+e.source+t.entryPoint+e.entryPoint;this._layoutKey=Xs(s,"program")}get attributeData(){return this._attributeData??(this._attributeData=ta(this.vertex)),this._attributeData}destroy(){this.gpuLayout=null,this.layout=null,this.structsAndGroups=null,this.fragment=null,this.vertex=null}static from(t){const e=`${t.vertex.source}:${t.fragment.source}:${t.fragment.entryPoint}:${t.vertex.entryPoint}`;return ms[e]||(ms[e]=new Ze(t)),ms[e]}}const Kn=["f32","i32","vec2<f32>","vec3<f32>","vec4<f32>","mat2x2<f32>","mat3x3<f32>","mat4x4<f32>","mat3x2<f32>","mat4x2<f32>","mat2x3<f32>","mat4x3<f32>","mat2x4<f32>","mat3x4<f32>","vec2<i32>","vec3<i32>","vec4<i32>"],na=Kn.reduce((i,t)=>(i[t]=!0,i),{});function ra(i,t){switch(i){case"f32":return 0;case"vec2<f32>":return new Float32Array(2*t);case"vec3<f32>":return new Float32Array(3*t);case"vec4<f32>":return new Float32Array(4*t);case"mat2x2<f32>":return new Float32Array([1,0,0,1]);case"mat3x3<f32>":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4x4<f32>":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const Zn=class Qn{constructor(t,e){this._touched=0,this.uid=_t("uniform"),this._resourceType="uniformGroup",this._resourceId=_t("resource"),this.isUniformGroup=!0,this._dirtyId=0,this.destroyed=!1,e={...Qn.defaultOptions,...e},this.uniformStructures=t;const s={};for(const n in t){const r=t[n];if(r.name=n,r.size=r.size??1,!na[r.type])throw new Error(`Uniform type ${r.type} is not supported. Supported uniform types are: ${Kn.join(", ")}`);r.value??(r.value=ra(r.type,r.size)),s[n]=r.value}this.uniforms=s,this._dirtyId=1,this.ubo=e.ubo,this.isStatic=e.isStatic,this._signature=Xs(Object.keys(s).map(n=>`${n}-${t[n].type}`).join("-"),"uniform-group")}update(){this._dirtyId++}};Zn.defaultOptions={ubo:!1,isStatic:!1};let Jn=Zn;class Ne{constructor(t){this.resources=Object.create(null),this._dirty=!0;let e=0;for(const s in t){const n=t[s];this.setResource(n,e++)}this._updateKey()}_updateKey(){if(!this._dirty)return;this._dirty=!1;const t=[];let e=0;for(const s in this.resources)t[e++]=this.resources[s]._resourceId;this._key=t.join("|")}setResource(t,e){var n,r;const s=this.resources[e];t!==s&&(s&&((n=t.off)==null||n.call(t,"change",this.onResourceChange,this)),(r=t.on)==null||r.call(t,"change",this.onResourceChange,this),this.resources[e]=t,this._dirty=!0)}getResource(t){return this.resources[t]}_touch(t){const e=this.resources;for(const s in e)e[s]._touched=t}destroy(){var e;const t=this.resources;for(const s in t){const n=t[s];(e=n.off)==null||e.call(n,"change",this.onResourceChange,this)}this.resources=null}onResourceChange(t){if(this._dirty=!0,t.destroyed){const e=this.resources;for(const s in e)e[s]===t&&(e[s]=null)}else this._updateKey()}}var Gs=(i=>(i[i.WEBGL=1]="WEBGL",i[i.WEBGPU=2]="WEBGPU",i[i.BOTH=3]="BOTH",i))(Gs||{});class js extends Nt{constructor(t){super(),this.uid=_t("shader"),this._uniformBindMap=Object.create(null),this._ownedBindGroups=[];let{gpuProgram:e,glProgram:s,groups:n,resources:r,compatibleRenderers:o,groupMap:a}=t;this.gpuProgram=e,this.glProgram=s,o===void 0&&(o=0,e&&(o|=Gs.WEBGPU),s&&(o|=Gs.WEBGL)),this.compatibleRenderers=o;const h={};if(!r&&!n&&(r={}),r&&n)throw new Error("[Shader] Cannot have both resources and groups");if(!e&&n&&!a)throw new Error("[Shader] No group map or WebGPU shader provided - consider using resources instead.");if(!e&&n&&a)for(const c in a)for(const l in a[c]){const u=a[c][l];h[u]={group:c,binding:l,name:u}}else if(e&&n&&!a){const c=e.structsAndGroups.groups;a={},c.forEach(l=>{a[l.group]=a[l.group]||{},a[l.group][l.binding]=l.name,h[l.name]=l})}else if(r){n={},a={},e&&e.structsAndGroups.groups.forEach(u=>{a[u.group]=a[u.group]||{},a[u.group][u.binding]=u.name,h[u.name]=u});let c=0;for(const l in r)h[l]||(n[99]||(n[99]=new Ne,this._ownedBindGroups.push(n[99])),h[l]={group:99,binding:c,name:l},a[99]=a[99]||{},a[99][c]=l,c++);for(const l in r){const u=l;let f=r[l];!f.source&&!f._resourceType&&(f=new Jn(f));const d=h[u];d&&(n[d.group]||(n[d.group]=new Ne,this._ownedBindGroups.push(n[d.group])),n[d.group].setResource(f,d.binding))}}this.groups=n,this._uniformBindMap=a,this.resources=this._buildResourceAccessor(n,h)}addResource(t,e,s){var n,r;(n=this._uniformBindMap)[e]||(n[e]={}),(r=this._uniformBindMap[e])[s]||(r[s]=t),this.groups[e]||(this.groups[e]=new Ne,this._ownedBindGroups.push(this.groups[e]))}_buildResourceAccessor(t,e){const s={};for(const n in e){const r=e[n];Object.defineProperty(s,r.name,{get(){return t[r.group].getResource(r.binding)},set(o){t[r.group].setResource(o,r.binding)}})}return s}destroy(t=!1){var e,s;this.emit("destroy",this),t&&((e=this.gpuProgram)==null||e.destroy(),(s=this.glProgram)==null||s.destroy()),this.gpuProgram=null,this.glProgram=null,this.removeAllListeners(),this._uniformBindMap=null,this._ownedBindGroups.forEach(n=>{n.destroy()}),this._ownedBindGroups=null,this.resources=null,this.groups=null}static from(t){const{gpu:e,gl:s,...n}=t;let r,o;return e&&(r=Ze.from(e)),s&&(o=jn.from(s)),new js({gpuProgram:r,glProgram:o,...n})}}const Ds=[];Ft.handleByNamedList(rt.Environment,Ds);async function oa(i){if(!i)for(let t=0;t<Ds.length;t++){const e=Ds[t];if(e.value.test()){await e.value.load();return}}}let me;function aa(){if(typeof me=="boolean")return me;try{me=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{me=!1}return me}var De={exports:{}},Bi;function ha(){if(Bi)return De.exports;Bi=1,De.exports=i,De.exports.default=i;function i(p,m,y){y=y||2;var C=m&&m.length,v=C?m[0]*y:p.length,T=t(p,0,v,y,!0),A=[];if(!T||T.next===T.prev)return A;var k,F,q,Z,X,j,lt;if(C&&(T=h(p,m,T,y)),p.length>80*y){k=q=p[0],F=Z=p[1];for(var tt=y;tt<v;tt+=y)X=p[tt],j=p[tt+1],X<k&&(k=X),j<F&&(F=j),X>q&&(q=X),j>Z&&(Z=j);lt=Math.max(q-k,Z-F),lt=lt!==0?32767/lt:0}return s(T,A,y,k,F,lt,0),A}function t(p,m,y,C,v){var T,A;if(v===z(p,m,y,C)>0)for(T=m;T<y;T+=C)A=nt(T,p[T],p[T+1],A);else for(T=y-C;T>=m;T-=C)A=nt(T,p[T],p[T+1],A);return A&&M(A,A.next)&&(st(A),A=A.next),A}function e(p,m){if(!p)return p;m||(m=p);var y=p,C;do if(C=!1,!y.steiner&&(M(y,y.next)||S(y.prev,y,y.next)===0)){if(st(y),y=m=y.prev,y===y.next)break;C=!0}else y=y.next;while(C||y!==m);return m}function s(p,m,y,C,v,T,A){if(p){!A&&T&&d(p,C,v,T);for(var k=p,F,q;p.prev!==p.next;){if(F=p.prev,q=p.next,T?r(p,C,v,T):n(p)){m.push(F.i/y|0),m.push(p.i/y|0),m.push(q.i/y|0),st(p),p=q.next,k=q.next;continue}if(p=q,p===k){A?A===1?(p=o(e(p),m,y),s(p,m,y,C,v,T,2)):A===2&&a(p,m,y,C,v,T):s(e(p),m,y,C,v,T,1);break}}}}function n(p){var m=p.prev,y=p,C=p.next;if(S(m,y,C)>=0)return!1;for(var v=m.x,T=y.x,A=C.x,k=m.y,F=y.y,q=C.y,Z=v<T?v<A?v:A:T<A?T:A,X=k<F?k<q?k:q:F<q?F:q,j=v>T?v>A?v:A:T>A?T:A,lt=k>F?k>q?k:q:F>q?F:q,tt=C.next;tt!==m;){if(tt.x>=Z&&tt.x<=j&&tt.y>=X&&tt.y<=lt&&b(v,k,T,F,A,q,tt.x,tt.y)&&S(tt.prev,tt,tt.next)>=0)return!1;tt=tt.next}return!0}function r(p,m,y,C){var v=p.prev,T=p,A=p.next;if(S(v,T,A)>=0)return!1;for(var k=v.x,F=T.x,q=A.x,Z=v.y,X=T.y,j=A.y,lt=k<F?k<q?k:q:F<q?F:q,tt=Z<X?Z<j?Z:j:X<j?X:j,Pt=k>F?k>q?k:q:F>q?F:q,bt=Z>X?Z>j?Z:j:X>j?X:j,B=_(lt,tt,m,y,C),L=_(Pt,bt,m,y,C),G=p.prevZ,H=p.nextZ;G&&G.z>=B&&H&&H.z<=L;){if(G.x>=lt&&G.x<=Pt&&G.y>=tt&&G.y<=bt&&G!==v&&G!==A&&b(k,Z,F,X,q,j,G.x,G.y)&&S(G.prev,G,G.next)>=0||(G=G.prevZ,H.x>=lt&&H.x<=Pt&&H.y>=tt&&H.y<=bt&&H!==v&&H!==A&&b(k,Z,F,X,q,j,H.x,H.y)&&S(H.prev,H,H.next)>=0))return!1;H=H.nextZ}for(;G&&G.z>=B;){if(G.x>=lt&&G.x<=Pt&&G.y>=tt&&G.y<=bt&&G!==v&&G!==A&&b(k,Z,F,X,q,j,G.x,G.y)&&S(G.prev,G,G.next)>=0)return!1;G=G.prevZ}for(;H&&H.z<=L;){if(H.x>=lt&&H.x<=Pt&&H.y>=tt&&H.y<=bt&&H!==v&&H!==A&&b(k,Z,F,X,q,j,H.x,H.y)&&S(H.prev,H,H.next)>=0)return!1;H=H.nextZ}return!0}function o(p,m,y){var C=p;do{var v=C.prev,T=C.next.next;!M(v,T)&&I(v,C,C.next,T)&&W(v,T)&&W(T,v)&&(m.push(v.i/y|0),m.push(C.i/y|0),m.push(T.i/y|0),st(C),st(C.next),C=p=T),C=C.next}while(C!==p);return e(C)}function a(p,m,y,C,v,T){var A=p;do{for(var k=A.next.next;k!==A.prev;){if(A.i!==k.i&&w(A,k)){var F=U(A,k);A=e(A,A.next),F=e(F,F.next),s(A,m,y,C,v,T,0),s(F,m,y,C,v,T,0);return}k=k.next}A=A.next}while(A!==p)}function h(p,m,y,C){var v=[],T,A,k,F,q;for(T=0,A=m.length;T<A;T++)k=m[T]*C,F=T<A-1?m[T+1]*C:p.length,q=t(p,k,F,C,!1),q===q.next&&(q.steiner=!0),v.push(g(q));for(v.sort(c),T=0;T<v.length;T++)y=l(v[T],y);return y}function c(p,m){return p.x-m.x}function l(p,m){var y=u(p,m);if(!y)return m;var C=U(y,p);return e(C,C.next),e(y,y.next)}function u(p,m){var y=m,C=p.x,v=p.y,T=-1/0,A;do{if(v<=y.y&&v>=y.next.y&&y.next.y!==y.y){var k=y.x+(v-y.y)*(y.next.x-y.x)/(y.next.y-y.y);if(k<=C&&k>T&&(T=k,A=y.x<y.next.x?y:y.next,k===C))return A}y=y.next}while(y!==m);if(!A)return null;var F=A,q=A.x,Z=A.y,X=1/0,j;y=A;do C>=y.x&&y.x>=q&&C!==y.x&&b(v<Z?C:T,v,q,Z,v<Z?T:C,v,y.x,y.y)&&(j=Math.abs(v-y.y)/(C-y.x),W(y,p)&&(j<X||j===X&&(y.x>A.x||y.x===A.x&&f(A,y)))&&(A=y,X=j)),y=y.next;while(y!==F);return A}function f(p,m){return S(p.prev,p,m.prev)<0&&S(m.next,p,p.next)<0}function d(p,m,y,C){var v=p;do v.z===0&&(v.z=_(v.x,v.y,m,y,C)),v.prevZ=v.prev,v.nextZ=v.next,v=v.next;while(v!==p);v.prevZ.nextZ=null,v.prevZ=null,x(v)}function x(p){var m,y,C,v,T,A,k,F,q=1;do{for(y=p,p=null,T=null,A=0;y;){for(A++,C=y,k=0,m=0;m<q&&(k++,C=C.nextZ,!!C);m++);for(F=q;k>0||F>0&&C;)k!==0&&(F===0||!C||y.z<=C.z)?(v=y,y=y.nextZ,k--):(v=C,C=C.nextZ,F--),T?T.nextZ=v:p=v,v.prevZ=T,T=v;y=C}T.nextZ=null,q*=2}while(A>1);return p}function _(p,m,y,C,v){return p=(p-y)*v|0,m=(m-C)*v|0,p=(p|p<<8)&16711935,p=(p|p<<4)&252645135,p=(p|p<<2)&858993459,p=(p|p<<1)&1431655765,m=(m|m<<8)&16711935,m=(m|m<<4)&252645135,m=(m|m<<2)&858993459,m=(m|m<<1)&1431655765,p|m<<1}function g(p){var m=p,y=p;do(m.x<y.x||m.x===y.x&&m.y<y.y)&&(y=m),m=m.next;while(m!==p);return y}function b(p,m,y,C,v,T,A,k){return(v-A)*(m-k)>=(p-A)*(T-k)&&(p-A)*(C-k)>=(y-A)*(m-k)&&(y-A)*(T-k)>=(v-A)*(C-k)}function w(p,m){return p.next.i!==m.i&&p.prev.i!==m.i&&!O(p,m)&&(W(p,m)&&W(m,p)&&$(p,m)&&(S(p.prev,p,m.prev)||S(p,m.prev,m))||M(p,m)&&S(p.prev,p,p.next)>0&&S(m.prev,m,m.next)>0)}function S(p,m,y){return(m.y-p.y)*(y.x-m.x)-(m.x-p.x)*(y.y-m.y)}function M(p,m){return p.x===m.x&&p.y===m.y}function I(p,m,y,C){var v=P(S(p,m,y)),T=P(S(p,m,C)),A=P(S(y,C,p)),k=P(S(y,C,m));return!!(v!==T&&A!==k||v===0&&E(p,y,m)||T===0&&E(p,C,m)||A===0&&E(y,p,C)||k===0&&E(y,m,C))}function E(p,m,y){return m.x<=Math.max(p.x,y.x)&&m.x>=Math.min(p.x,y.x)&&m.y<=Math.max(p.y,y.y)&&m.y>=Math.min(p.y,y.y)}function P(p){return p>0?1:p<0?-1:0}function O(p,m){var y=p;do{if(y.i!==p.i&&y.next.i!==p.i&&y.i!==m.i&&y.next.i!==m.i&&I(y,y.next,p,m))return!0;y=y.next}while(y!==p);return!1}function W(p,m){return S(p.prev,p,p.next)<0?S(p,m,p.next)>=0&&S(p,p.prev,m)>=0:S(p,m,p.prev)<0||S(p,p.next,m)<0}function $(p,m){var y=p,C=!1,v=(p.x+m.x)/2,T=(p.y+m.y)/2;do y.y>T!=y.next.y>T&&y.next.y!==y.y&&v<(y.next.x-y.x)*(T-y.y)/(y.next.y-y.y)+y.x&&(C=!C),y=y.next;while(y!==p);return C}function U(p,m){var y=new xt(p.i,p.x,p.y),C=new xt(m.i,m.x,m.y),v=p.next,T=m.prev;return p.next=m,m.prev=p,y.next=v,v.prev=y,C.next=y,y.prev=C,T.next=C,C.prev=T,C}function nt(p,m,y,C){var v=new xt(p,m,y);return C?(v.next=C.next,v.prev=C,C.next.prev=v,C.next=v):(v.prev=v,v.next=v),v}function st(p){p.next.prev=p.prev,p.prev.next=p.next,p.prevZ&&(p.prevZ.nextZ=p.nextZ),p.nextZ&&(p.nextZ.prevZ=p.prevZ)}function xt(p,m,y){this.i=p,this.x=m,this.y=y,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}i.deviation=function(p,m,y,C){var v=m&&m.length,T=v?m[0]*y:p.length,A=Math.abs(z(p,0,T,y));if(v)for(var k=0,F=m.length;k<F;k++){var q=m[k]*y,Z=k<F-1?m[k+1]*y:p.length;A-=Math.abs(z(p,q,Z,y))}var X=0;for(k=0;k<C.length;k+=3){var j=C[k]*y,lt=C[k+1]*y,tt=C[k+2]*y;X+=Math.abs((p[j]-p[tt])*(p[lt+1]-p[j+1])-(p[j]-p[lt])*(p[tt+1]-p[j+1]))}return A===0&&X===0?0:Math.abs((X-A)/A)};function z(p,m,y,C){for(var v=0,T=m,A=y-C;T<y;T+=C)v+=(p[A]-p[T])*(p[T+1]+p[A+1]),A=T;return v}return i.flatten=function(p){for(var m=p[0][0].length,y={vertices:[],holes:[],dimensions:m},C=0,v=0;v<p.length;v++){for(var T=0;T<p[v].length;T++)for(var A=0;A<m;A++)y.vertices.push(p[v][T][A]);v>0&&(C+=p[v-1].length,y.holes.push(C))}return y},De.exports}var la=ha();const ca=Ns(la);var tr=(i=>(i[i.NONE=0]="NONE",i[i.COLOR=16384]="COLOR",i[i.STENCIL=1024]="STENCIL",i[i.DEPTH=256]="DEPTH",i[i.COLOR_DEPTH=16640]="COLOR_DEPTH",i[i.COLOR_STENCIL=17408]="COLOR_STENCIL",i[i.DEPTH_STENCIL=1280]="DEPTH_STENCIL",i[i.ALL=17664]="ALL",i))(tr||{});class ua{constructor(t){this.items=[],this._name=t}emit(t,e,s,n,r,o,a,h){const{name:c,items:l}=this;for(let u=0,f=l.length;u<f;u++)l[u][c](t,e,s,n,r,o,a,h);return this}add(t){return t[this._name]&&(this.remove(t),this.items.push(t)),this}remove(t){const e=this.items.indexOf(t);return e!==-1&&this.items.splice(e,1),this}contains(t){return this.items.indexOf(t)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const da=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],er=class sr extends Nt{constructor(t){super(),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=t.type,this.name=t.name,this.config=t;const e=[...da,...this.config.runners??[]];this._addRunners(...e),this._unsafeEvalCheck()}async init(t={}){const e=t.skipExtensionImports===!0?!0:t.manageImports===!1;await oa(e),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const s in this._systemsHash)t={...this._systemsHash[s].constructor.defaultOptions,...t};t={...sr.defaultOptions,...t},this._roundPixels=t.roundPixels?1:0;for(let s=0;s<this.runners.init.items.length;s++)await this.runners.init.items[s].init(t);this._initOptions=t}render(t,e){let s=t;if(s instanceof pt&&(s={container:s},e&&(at(mt,"passing a second argument is deprecated, please use render options instead"),s.target=e.renderTexture)),s.target||(s.target=this.view.renderTarget),s.target===this.view.renderTarget&&(this._lastObjectRendered=s.container,s.clearColor??(s.clearColor=this.background.colorRgba),s.clear??(s.clear=this.background.clearBeforeRender)),s.clearColor){const n=Array.isArray(s.clearColor)&&s.clearColor.length===4;s.clearColor=n?s.clearColor:Mt.shared.setValue(s.clearColor).toArray()}s.transform||(s.container.updateLocalTransform(),s.transform=s.container.localTransform),s.container.enableRenderGroup(),this.runners.prerender.emit(s),this.runners.renderStart.emit(s),this.runners.render.emit(s),this.runners.renderEnd.emit(s),this.runners.postrender.emit(s)}resize(t,e,s){const n=this.view.resolution;this.view.resize(t,e,s),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),s!==void 0&&s!==n&&this.runners.resolutionChange.emit(s)}clear(t={}){const e=this;t.target||(t.target=e.renderTarget.renderTarget),t.clearColor||(t.clearColor=this.background.colorRgba),t.clear??(t.clear=tr.ALL);const{clear:s,clearColor:n,target:r}=t;Mt.shared.setValue(n??this.background.colorRgba),e.renderTarget.clear(r,s,Mt.shared.toArray())}get resolution(){return this.view.resolution}set resolution(t){this.view.resolution=t,this.runners.resolutionChange.emit(t)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...t){t.forEach(e=>{this.runners[e]=new ua(e)})}_addSystems(t){let e;for(e in t){const s=t[e];this._addSystem(s.value,s.name)}}_addSystem(t,e){const s=new t(this);if(this[e])throw new Error(`Whoops! The name "${e}" is already in use`);this[e]=s,this._systemsHash[e]=s;for(const n in this.runners)this.runners[n].add(s);return this}_addPipes(t,e){const s=e.reduce((n,r)=>(n[r.name]=r.value,n),{});t.forEach(n=>{const r=n.value,o=n.name,a=s[o];this.renderPipes[o]=new r(this,a?new a:null)})}destroy(t=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(t),Object.values(this.runners).forEach(e=>{e.destroy()}),this._systemsHash=null,this.renderPipes=null}generateTexture(t){return this.textureGenerator.generateTexture(t)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!aa())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};er.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let ir=er,He;function fa(i){return He!==void 0||(He=(()=>{var e;const t={stencil:!0,failIfMajorPerformanceCaveat:i??ir.defaultOptions.failIfMajorPerformanceCaveat};try{if(!Ht.get().getWebGLRenderingContext())return!1;let n=Ht.get().createCanvas().getContext("webgl",t);const r=!!((e=n==null?void 0:n.getContextAttributes())!=null&&e.stencil);if(n){const o=n.getExtension("WEBGL_lose_context");o&&o.loseContext()}return n=null,r}catch{return!1}})()),He}let $e;async function pa(i={}){return $e!==void 0||($e=await(async()=>{const t=Ht.get().getNavigator().gpu;if(!t)return!1;try{return await(await t.requestAdapter(i)).requestDevice(),!0}catch{return!1}})()),$e}const Ri=["webgl","webgpu","canvas"];async function ga(i){let t=[];i.preference?(t.push(i.preference),Ri.forEach(r=>{r!==i.preference&&t.push(r)})):t=Ri.slice();let e,s={};for(let r=0;r<t.length;r++){const o=t[r];if(o==="webgpu"&&await pa()){const{WebGPURenderer:a}=await Ve(async()=>{const{WebGPURenderer:h}=await import("./WebGPURenderer-DwwCZVVK.js");return{WebGPURenderer:h}},__vite__mapDeps([4,2,5,3]));e=a,s={...i,...i.webgpu};break}else if(o==="webgl"&&fa(i.failIfMajorPerformanceCaveat??ir.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:a}=await Ve(async()=>{const{WebGLRenderer:h}=await import("./WebGLRenderer-FBNzJiI7.js");return{WebGLRenderer:h}},__vite__mapDeps([6,2,5]));e=a,s={...i,...i.webgl};break}else if(o==="canvas")throw s={...i},new Error("CanvasRenderer is not yet implemented")}if(delete s.webgpu,delete s.webgl,!e)throw new Error("No available renderer for the current environment");const n=new e;return await n.init(s),n}const nr="8.9.1";class rr{static init(){var t;(t=globalThis.__PIXI_APP_INIT__)==null||t.call(globalThis,this,nr)}static destroy(){}}rr.extension=rt.Application;class ma{constructor(t){this._renderer=t}init(){var t;(t=globalThis.__PIXI_RENDERER_INIT__)==null||t.call(globalThis,this._renderer,nr)}destroy(){this._renderer=null}}ma.extension={type:[rt.WebGLSystem,rt.WebGPUSystem],name:"initHook",priority:-10};const or=class Hs{constructor(...t){this.stage=new pt,t[0]!==void 0&&at(mt,"Application constructor options are deprecated, please use Application.init() instead.")}async init(t){t={...t},this.renderer=await ga(t),Hs._plugins.forEach(e=>{e.init.call(this,t)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return at(mt,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(t=!1,e=!1){const s=Hs._plugins.slice(0);s.reverse(),s.forEach(n=>{n.destroy.call(this)}),this.stage.destroy(e),this.stage=null,this.renderer.destroy(t),this.renderer=null}};or._plugins=[];let ar=or;Ft.handleByList(rt.Application,ar._plugins);Ft.add(rr);const Fi=[{offset:0,color:"white"},{offset:1,color:"black"}],Ks=class $s{constructor(...t){this.uid=_t("fillGradient"),this.type="linear",this.colorStops=[];let e=xa(t);e={...e.type==="radial"?$s.defaultRadialOptions:$s.defaultLinearOptions,...xn(e)},this._textureSize=e.textureSize,e.type==="radial"?(this.center=e.center,this.outerCenter=e.outerCenter??this.center,this.innerRadius=e.innerRadius,this.outerRadius=e.outerRadius,this.scale=e.scale,this.rotation=e.rotation):(this.start=e.start,this.end=e.end),this.textureSpace=e.textureSpace,this.type=e.type,e.colorStops.forEach(n=>{this.addColorStop(n.offset,n.color)})}addColorStop(t,e){return this.colorStops.push({offset:t,color:Mt.shared.setValue(e).toHexa()}),this}buildLinearGradient(){if(this.texture)return;const t=this.colorStops.length?this.colorStops:Fi,e=this._textureSize,{canvas:s,context:n}=Di(e,1),r=n.createLinearGradient(0,0,this._textureSize,0);Gi(r,t),n.fillStyle=r,n.fillRect(0,0,e,1),this.texture=new ht({source:new Ye({resource:s})});const{x:o,y:a}=this.start,{x:h,y:c}=this.end,l=new it,u=h-o,f=c-a,d=Math.sqrt(u*u+f*f),x=Math.atan2(f,u);l.scale(d/e,1),l.rotate(x),l.translate(o,a),this.textureSpace==="local"&&l.scale(e,e),this.transform=l}buildGradient(){this.type==="linear"?this.buildLinearGradient():this.buildRadialGradient()}buildRadialGradient(){if(this.texture)return;const t=this.colorStops.length?this.colorStops:Fi,e=this._textureSize,{canvas:s,context:n}=Di(e,e),{x:r,y:o}=this.center,{x:a,y:h}=this.outerCenter,c=this.innerRadius,l=this.outerRadius,u=a-l,f=h-l,d=e/(l*2),x=(r-u)*d,_=(o-f)*d,g=n.createRadialGradient(x,_,c*d,(a-u)*d,(h-f)*d,l*d);Gi(g,t),n.fillStyle=t[t.length-1].color,n.fillRect(0,0,e,e),n.fillStyle=g,n.translate(x,_),n.rotate(this.rotation),n.scale(1,this.scale),n.translate(-x,-_),n.fillRect(0,0,e,e),this.texture=new ht({source:new Ye({resource:s,addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"})});const b=new it;b.scale(1/d,1/d),b.translate(u,f),this.textureSpace==="local"&&b.scale(e,e),this.transform=b}get styleKey(){return this.uid}destroy(){var t;(t=this.texture)==null||t.destroy(!0),this.texture=null}};Ks.defaultLinearOptions={start:{x:0,y:0},end:{x:0,y:1},colorStops:[],textureSpace:"local",type:"linear",textureSize:256};Ks.defaultRadialOptions={center:{x:.5,y:.5},innerRadius:0,outerRadius:.5,colorStops:[],scale:1,textureSpace:"local",type:"radial",textureSize:256};let ie=Ks;function Gi(i,t){for(let e=0;e<t.length;e++){const s=t[e];i.addColorStop(s.offset,s.color)}}function Di(i,t){const e=Ht.get().createCanvas(i,t),s=e.getContext("2d");return{canvas:e,context:s}}function xa(i){let t=i[0]??{};return(typeof t=="number"||i[1])&&(at("8.5.2","use options object instead"),t={type:"linear",start:{x:i[0],y:i[1]},end:{x:i[2],y:i[3]},textureSpace:i[4],textureSize:i[5]??ie.defaultLinearOptions.textureSize}),t}const Hi={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class Zs{constructor(t,e){this.uid=_t("fillPattern"),this.transform=new it,this._styleKey=null,this.texture=t,this.transform.scale(1/t.frame.width,1/t.frame.height),e&&(t.source.style.addressModeU=Hi[e].addressModeU,t.source.style.addressModeV=Hi[e].addressModeV)}setTransform(t){const e=this.texture;this.transform.copyFrom(t),this.transform.invert(),this.transform.scale(1/e.frame.width,1/e.frame.height),this._styleKey=null}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}}var xs,$i;function ya(){if($i)return xs;$i=1,xs=e;var i={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},t=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function e(r){var o=[];return r.replace(t,function(a,h,c){var l=h.toLowerCase();for(c=n(c),l=="m"&&c.length>2&&(o.push([h].concat(c.splice(0,2))),l="l",h=h=="m"?"l":"L");;){if(c.length==i[l])return c.unshift(h),o.push(c);if(c.length<i[l])throw new Error("malformed path data");o.push([h].concat(c.splice(0,i[l])))}}),o}var s=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function n(r){var o=r.match(s);return o?o.map(Number):[]}return xs}var _a=ya();const ba=Ns(_a);function wa(i,t){const e=ba(i),s=[];let n=null,r=0,o=0;for(let a=0;a<e.length;a++){const h=e[a],c=h[0],l=h;switch(c){case"M":r=l[1],o=l[2],t.moveTo(r,o);break;case"m":r+=l[1],o+=l[2],t.moveTo(r,o);break;case"H":r=l[1],t.lineTo(r,o);break;case"h":r+=l[1],t.lineTo(r,o);break;case"V":o=l[1],t.lineTo(r,o);break;case"v":o+=l[1],t.lineTo(r,o);break;case"L":r=l[1],o=l[2],t.lineTo(r,o);break;case"l":r+=l[1],o+=l[2],t.lineTo(r,o);break;case"C":r=l[5],o=l[6],t.bezierCurveTo(l[1],l[2],l[3],l[4],r,o);break;case"c":t.bezierCurveTo(r+l[1],o+l[2],r+l[3],o+l[4],r+l[5],o+l[6]),r+=l[5],o+=l[6];break;case"S":r=l[3],o=l[4],t.bezierCurveToShort(l[1],l[2],r,o);break;case"s":t.bezierCurveToShort(r+l[1],o+l[2],r+l[3],o+l[4]),r+=l[3],o+=l[4];break;case"Q":r=l[3],o=l[4],t.quadraticCurveTo(l[1],l[2],r,o);break;case"q":t.quadraticCurveTo(r+l[1],o+l[2],r+l[3],o+l[4]),r+=l[3],o+=l[4];break;case"T":r=l[1],o=l[2],t.quadraticCurveToShort(r,o);break;case"t":r+=l[1],o+=l[2],t.quadraticCurveToShort(r,o);break;case"A":r=l[6],o=l[7],t.arcToSvg(l[1],l[2],l[3],l[4],l[5],r,o);break;case"a":r+=l[6],o+=l[7],t.arcToSvg(l[1],l[2],l[3],l[4],l[5],r,o);break;case"Z":case"z":t.closePath(),s.length>0&&(n=s.pop(),n?(r=n.startX,o=n.startY):(r=0,o=0)),n=null;break;default:kt(`Unknown SVG path command: ${c}`)}c!=="Z"&&c!=="z"&&n===null&&(n={startX:r,startY:o},s.push(n))}return t}class Qs{constructor(t=0,e=0,s=0){this.type="circle",this.x=t,this.y=e,this.radius=s}clone(){return new Qs(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return!1;const s=this.radius*this.radius;let n=this.x-t,r=this.y-e;return n*=n,r*=r,n+r<=s}strokeContains(t,e,s,n=.5){if(this.radius===0)return!1;const r=this.x-t,o=this.y-e,a=this.radius,h=(1-n)*s,c=Math.sqrt(r*r+o*o);return c<=a+h&&c>a-(s-h)}getBounds(t){return t||(t=new Ct),t.x=this.x-this.radius,t.y=this.y-this.radius,t.width=this.radius*2,t.height=this.radius*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.radius=t.radius,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class Js{constructor(t=0,e=0,s=0,n=0){this.type="ellipse",this.x=t,this.y=e,this.halfWidth=s,this.halfHeight=n}clone(){return new Js(this.x,this.y,this.halfWidth,this.halfHeight)}contains(t,e){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let s=(t-this.x)/this.halfWidth,n=(e-this.y)/this.halfHeight;return s*=s,n*=n,s+n<=1}strokeContains(t,e,s,n=.5){const{halfWidth:r,halfHeight:o}=this;if(r<=0||o<=0)return!1;const a=s*(1-n),h=s-a,c=r-h,l=o-h,u=r+a,f=o+a,d=t-this.x,x=e-this.y,_=d*d/(c*c)+x*x/(l*l),g=d*d/(u*u)+x*x/(f*f);return _>1&&g<=1}getBounds(t){return t||(t=new Ct),t.x=this.x-this.halfWidth,t.y=this.y-this.halfHeight,t.width=this.halfWidth*2,t.height=this.halfHeight*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.halfWidth=t.halfWidth,this.halfHeight=t.halfHeight,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function Sa(i,t,e,s,n,r){const o=i-e,a=t-s,h=n-e,c=r-s,l=o*h+a*c,u=h*h+c*c;let f=-1;u!==0&&(f=l/u);let d,x;f<0?(d=e,x=s):f>1?(d=n,x=r):(d=e+f*h,x=s+f*c);const _=i-d,g=t-x;return _*_+g*g}let Ca,Ma;class Me{constructor(...t){this.type="polygon";let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const s=[];for(let n=0,r=e.length;n<r;n++)s.push(e[n].x,e[n].y);e=s}this.points=e,this.closePath=!0}isClockwise(){let t=0;const e=this.points,s=e.length;for(let n=0;n<s;n+=2){const r=e[n],o=e[n+1],a=e[(n+2)%s],h=e[(n+3)%s];t+=(a-r)*(h+o)}return t<0}containsPolygon(t){const e=this.getBounds(Ca),s=t.getBounds(Ma);if(!e.containsRect(s))return!1;const n=t.points;for(let r=0;r<n.length;r+=2){const o=n[r],a=n[r+1];if(!this.contains(o,a))return!1}return!0}clone(){const t=this.points.slice(),e=new Me(t);return e.closePath=this.closePath,e}contains(t,e){let s=!1;const n=this.points.length/2;for(let r=0,o=n-1;r<n;o=r++){const a=this.points[r*2],h=this.points[r*2+1],c=this.points[o*2],l=this.points[o*2+1];h>e!=l>e&&t<(c-a)*((e-h)/(l-h))+a&&(s=!s)}return s}strokeContains(t,e,s,n=.5){const r=s*s,o=r*(1-n),a=r-o,{points:h}=this,c=h.length-(this.closePath?0:2);for(let l=0;l<c;l+=2){const u=h[l],f=h[l+1],d=h[(l+2)%h.length],x=h[(l+3)%h.length],_=Sa(t,e,u,f,d,x),g=Math.sign((d-u)*(e-f)-(x-f)*(t-u));if(_<=(g<0?a:o))return!0}return!1}getBounds(t){t||(t=new Ct);const e=this.points;let s=1/0,n=-1/0,r=1/0,o=-1/0;for(let a=0,h=e.length;a<h;a+=2){const c=e[a],l=e[a+1];s=c<s?c:s,n=c>n?c:n,r=l<r?l:r,o=l>o?l:o}return t.x=s,t.width=n-s,t.y=r,t.height=o-r,t}copyFrom(t){return this.points=t.points.slice(),this.closePath=t.closePath,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((t,e)=>`${t}, ${e}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return this.points[this.points.length-2]}get y(){return this.points[this.points.length-1]}}const Ue=(i,t,e,s,n,r,o)=>{const a=i-e,h=t-s,c=Math.sqrt(a*a+h*h);return c>=n-r&&c<=n+o};class ti{constructor(t=0,e=0,s=0,n=0,r=20){this.type="roundedRectangle",this.x=t,this.y=e,this.width=s,this.height=n,this.radius=r}getBounds(t){return t||(t=new Ct),t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}clone(){return new ti(this.x,this.y,this.width,this.height,this.radius)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const s=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+s&&e<=this.y+this.height-s||t>=this.x+s&&t<=this.x+this.width-s)return!0;let n=t-(this.x+s),r=e-(this.y+s);const o=s*s;if(n*n+r*r<=o||(n=t-(this.x+this.width-s),n*n+r*r<=o)||(r=e-(this.y+this.height-s),n*n+r*r<=o)||(n=t-(this.x+s),n*n+r*r<=o))return!0}return!1}strokeContains(t,e,s,n=.5){const{x:r,y:o,width:a,height:h,radius:c}=this,l=s*(1-n),u=s-l,f=r+c,d=o+c,x=a-c*2,_=h-c*2,g=r+a,b=o+h;return(t>=r-l&&t<=r+u||t>=g-u&&t<=g+l)&&e>=d&&e<=d+_||(e>=o-l&&e<=o+u||e>=b-u&&e<=b+l)&&t>=f&&t<=f+x?!0:t<f&&e<d&&Ue(t,e,f,d,c,u,l)||t>g-c&&e<d&&Ue(t,e,g-c,d,c,u,l)||t>g-c&&e>b-c&&Ue(t,e,g-c,b-c,c,u,l)||t<f&&e>b-c&&Ue(t,e,f,b-c,c,u,l)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}const va=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function Ta(i){let t="";for(let e=0;e<i;++e)e>0&&(t+=`
else `),e<i-1&&(t+=`if(test == ${e}.0){}`);return t}function Aa(i,t){if(i===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const e=t.createShader(t.FRAGMENT_SHADER);try{for(;;){const s=va.replace(/%forloop%/gi,Ta(i));if(t.shaderSource(e,s),t.compileShader(e),!t.getShaderParameter(e,t.COMPILE_STATUS))i=i/2|0;else break}}finally{t.deleteShader(e)}return i}let he=null;function hr(){var t;if(he)return he;const i=Yn();return he=i.getParameter(i.MAX_TEXTURE_IMAGE_UNITS),he=Aa(he,i),(t=i.getExtension("WEBGL_lose_context"))==null||t.loseContext(),he}const lr={};function Pa(i,t){let e=2166136261;for(let s=0;s<t;s++)e^=i[s].uid,e=Math.imul(e,16777619),e>>>=0;return lr[e]||Ea(i,t,e)}let ys=0;function Ea(i,t,e){const s={};let n=0;ys||(ys=hr());for(let o=0;o<ys;o++){const a=o<t?i[o]:ht.EMPTY.source;s[n++]=a.source,s[n++]=a.style}const r=new Ne(s);return lr[e]=r,r}class Ui{constructor(t){typeof t=="number"?this.rawBinaryData=new ArrayBuffer(t):t instanceof Uint8Array?this.rawBinaryData=t.buffer:this.rawBinaryData=t,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData),this.size=this.rawBinaryData.byteLength}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}get float64View(){return this._float64Array||(this._float64Array=new Float64Array(this.rawBinaryData)),this._float64Array}get bigUint64View(){return this._bigUint64Array||(this._bigUint64Array=new BigUint64Array(this.rawBinaryData)),this._bigUint64Array}view(t){return this[`${t}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this.uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(t){switch(t){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${t} isn't a valid view type`)}}}function Li(i,t){const e=i.byteLength/8|0,s=new Float64Array(i,0,e);new Float64Array(t,0,e).set(s);const r=i.byteLength-e*8;if(r>0){const o=new Uint8Array(i,e*8,r);new Uint8Array(t,e*8,r).set(o)}}const ka={normal:"normal-npm",add:"add-npm",screen:"screen-npm"};var Ia=(i=>(i[i.DISABLED=0]="DISABLED",i[i.RENDERING_MASK_ADD=1]="RENDERING_MASK_ADD",i[i.MASK_ACTIVE=2]="MASK_ACTIVE",i[i.INVERSE_MASK_ACTIVE=3]="INVERSE_MASK_ACTIVE",i[i.RENDERING_MASK_REMOVE=4]="RENDERING_MASK_REMOVE",i[i.NONE=5]="NONE",i))(Ia||{});function qi(i,t){return t.alphaMode==="no-premultiply-alpha"&&ka[i]||i}class Ba{constructor(){this.ids=Object.create(null),this.textures=[],this.count=0}clear(){for(let t=0;t<this.count;t++){const e=this.textures[t];this.textures[t]=null,this.ids[e.uid]=null}this.count=0}}class Ra{constructor(){this.renderPipeId="batch",this.action="startBatch",this.start=0,this.size=0,this.textures=new Ba,this.blendMode="normal",this.topology="triangle-strip",this.canBundle=!0}destroy(){this.textures=null,this.gpuBindGroup=null,this.bindGroup=null,this.batcher=null}}const cr=[];let Us=0;function zi(){return Us>0?cr[--Us]:new Ra}function Ni(i){cr[Us++]=i}let xe=0;const ur=class We{constructor(t={}){this.uid=_t("batcher"),this.dirty=!0,this.batchIndex=0,this.batches=[],this._elements=[],We.defaultOptions.maxTextures=We.defaultOptions.maxTextures??hr(),t={...We.defaultOptions,...t};const{maxTextures:e,attributesInitialSize:s,indicesInitialSize:n}=t;this.attributeBuffer=new Ui(s*4),this.indexBuffer=new Uint16Array(n),this.maxTextures=e}begin(){this.elementSize=0,this.elementStart=0,this.indexSize=0,this.attributeSize=0;for(let t=0;t<this.batchIndex;t++)Ni(this.batches[t]);this.batchIndex=0,this._batchIndexStart=0,this._batchIndexSize=0,this.dirty=!0}add(t){this._elements[this.elementSize++]=t,t._indexStart=this.indexSize,t._attributeStart=this.attributeSize,t._batcher=this,this.indexSize+=t.indexSize,this.attributeSize+=t.attributeSize*this.vertexSize}checkAndUpdateTexture(t,e){const s=t._batch.textures.ids[e._source.uid];return!s&&s!==0?!1:(t._textureId=s,t.texture=e,!0)}updateElement(t){this.dirty=!0;const e=this.attributeBuffer;t.packAsQuad?this.packQuadAttributes(t,e.float32View,e.uint32View,t._attributeStart,t._textureId):this.packAttributes(t,e.float32View,e.uint32View,t._attributeStart,t._textureId)}break(t){const e=this._elements;if(!e[this.elementStart])return;let s=zi(),n=s.textures;n.clear();const r=e[this.elementStart];let o=qi(r.blendMode,r.texture._source),a=r.topology;this.attributeSize*4>this.attributeBuffer.size&&this._resizeAttributeBuffer(this.attributeSize*4),this.indexSize>this.indexBuffer.length&&this._resizeIndexBuffer(this.indexSize);const h=this.attributeBuffer.float32View,c=this.attributeBuffer.uint32View,l=this.indexBuffer;let u=this._batchIndexSize,f=this._batchIndexStart,d="startBatch";const x=this.maxTextures;for(let _=this.elementStart;_<this.elementSize;++_){const g=e[_];e[_]=null;const w=g.texture._source,S=qi(g.blendMode,w),M=o!==S||a!==g.topology;if(w._batchTick===xe&&!M){g._textureId=w._textureBindLocation,u+=g.indexSize,g.packAsQuad?(this.packQuadAttributes(g,h,c,g._attributeStart,g._textureId),this.packQuadIndex(l,g._indexStart,g._attributeStart/this.vertexSize)):(this.packAttributes(g,h,c,g._attributeStart,g._textureId),this.packIndex(g,l,g._indexStart,g._attributeStart/this.vertexSize)),g._batch=s;continue}w._batchTick=xe,(n.count>=x||M)&&(this._finishBatch(s,f,u-f,n,o,a,t,d),d="renderBatch",f=u,o=S,a=g.topology,s=zi(),n=s.textures,n.clear(),++xe),g._textureId=w._textureBindLocation=n.count,n.ids[w.uid]=n.count,n.textures[n.count++]=w,g._batch=s,u+=g.indexSize,g.packAsQuad?(this.packQuadAttributes(g,h,c,g._attributeStart,g._textureId),this.packQuadIndex(l,g._indexStart,g._attributeStart/this.vertexSize)):(this.packAttributes(g,h,c,g._attributeStart,g._textureId),this.packIndex(g,l,g._indexStart,g._attributeStart/this.vertexSize))}n.count>0&&(this._finishBatch(s,f,u-f,n,o,a,t,d),f=u,++xe),this.elementStart=this.elementSize,this._batchIndexStart=f,this._batchIndexSize=u}_finishBatch(t,e,s,n,r,o,a,h){t.gpuBindGroup=null,t.bindGroup=null,t.action=h,t.batcher=this,t.textures=n,t.blendMode=r,t.topology=o,t.start=e,t.size=s,++xe,this.batches[this.batchIndex++]=t,a.add(t)}finish(t){this.break(t)}ensureAttributeBuffer(t){t*4<=this.attributeBuffer.size||this._resizeAttributeBuffer(t*4)}ensureIndexBuffer(t){t<=this.indexBuffer.length||this._resizeIndexBuffer(t)}_resizeAttributeBuffer(t){const e=Math.max(t,this.attributeBuffer.size*2),s=new Ui(e);Li(this.attributeBuffer.rawBinaryData,s.rawBinaryData),this.attributeBuffer=s}_resizeIndexBuffer(t){const e=this.indexBuffer;let s=Math.max(t,e.length*1.5);s+=s%2;const n=s>65535?new Uint32Array(s):new Uint16Array(s);if(n.BYTES_PER_ELEMENT!==e.BYTES_PER_ELEMENT)for(let r=0;r<e.length;r++)n[r]=e[r];else Li(e.buffer,n.buffer);this.indexBuffer=n}packQuadIndex(t,e,s){t[e]=s+0,t[e+1]=s+1,t[e+2]=s+2,t[e+3]=s+0,t[e+4]=s+2,t[e+5]=s+3}packIndex(t,e,s,n){const r=t.indices,o=t.indexSize,a=t.indexOffset,h=t.attributeOffset;for(let c=0;c<o;c++)e[s++]=n+r[c+a]-h}destroy(){for(let t=0;t<this.batches.length;t++)Ni(this.batches[t]);this.batches=null;for(let t=0;t<this._elements.length;t++)this._elements[t]._batch=null;this._elements=null,this.indexBuffer=null,this.attributeBuffer.destroy(),this.attributeBuffer=null}};ur.defaultOptions={maxTextures:null,attributesInitialSize:4,indicesInitialSize:6};let Fa=ur;var At=(i=>(i[i.MAP_READ=1]="MAP_READ",i[i.MAP_WRITE=2]="MAP_WRITE",i[i.COPY_SRC=4]="COPY_SRC",i[i.COPY_DST=8]="COPY_DST",i[i.INDEX=16]="INDEX",i[i.VERTEX=32]="VERTEX",i[i.UNIFORM=64]="UNIFORM",i[i.STORAGE=128]="STORAGE",i[i.INDIRECT=256]="INDIRECT",i[i.QUERY_RESOLVE=512]="QUERY_RESOLVE",i[i.STATIC=1024]="STATIC",i))(At||{});class Ae extends Nt{constructor(t){let{data:e,size:s}=t;const{usage:n,label:r,shrinkToFit:o}=t;super(),this.uid=_t("buffer"),this._resourceType="buffer",this._resourceId=_t("resource"),this._touched=0,this._updateID=1,this._dataInt32=null,this.shrinkToFit=!0,this.destroyed=!1,e instanceof Array&&(e=new Float32Array(e)),this._data=e,s??(s=e==null?void 0:e.byteLength);const a=!!e;this.descriptor={size:s,usage:n,mappedAtCreation:a,label:r},this.shrinkToFit=o??!0}get data(){return this._data}set data(t){this.setDataWithSize(t,t.length,!0)}get dataInt32(){return this._dataInt32||(this._dataInt32=new Int32Array(this.data.buffer)),this._dataInt32}get static(){return!!(this.descriptor.usage&At.STATIC)}set static(t){t?this.descriptor.usage|=At.STATIC:this.descriptor.usage&=~At.STATIC}setDataWithSize(t,e,s){if(this._updateID++,this._updateSize=e*t.BYTES_PER_ELEMENT,this._data===t){s&&this.emit("update",this);return}const n=this._data;if(this._data=t,this._dataInt32=null,!n||n.length!==t.length){!this.shrinkToFit&&n&&t.byteLength<n.byteLength?s&&this.emit("update",this):(this.descriptor.size=t.byteLength,this._resourceId=_t("resource"),this.emit("change",this));return}s&&this.emit("update",this)}update(t){this._updateSize=t??this._updateSize,this._updateID++,this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._data=null,this.descriptor=null,this.removeAllListeners()}}function dr(i,t){if(!(i instanceof Ae)){let e=t?At.INDEX:At.VERTEX;i instanceof Array&&(t?(i=new Uint32Array(i),e=At.INDEX|At.COPY_DST):(i=new Float32Array(i),e=At.VERTEX|At.COPY_DST)),i=new Ae({data:i,label:t?"index-mesh-buffer":"vertex-mesh-buffer",usage:e})}return i}function Ga(i,t,e){const s=i.getAttribute(t);if(!s)return e.minX=0,e.minY=0,e.maxX=0,e.maxY=0,e;const n=s.buffer.data;let r=1/0,o=1/0,a=-1/0,h=-1/0;const c=n.BYTES_PER_ELEMENT,l=(s.offset||0)/c,u=(s.stride||2*4)/c;for(let f=l;f<n.length;f+=u){const d=n[f],x=n[f+1];d>a&&(a=d),x>h&&(h=x),d<r&&(r=d),x<o&&(o=x)}return e.minX=r,e.minY=o,e.maxX=a,e.maxY=h,e}function Da(i){return(i instanceof Ae||Array.isArray(i)||i.BYTES_PER_ELEMENT)&&(i={buffer:i}),i.buffer=dr(i.buffer,!1),i}class Ha extends Nt{constructor(t={}){super(),this.uid=_t("geometry"),this._layoutKey=0,this.instanceCount=1,this._bounds=new $t,this._boundsDirty=!0;const{attributes:e,indexBuffer:s,topology:n}=t;if(this.buffers=[],this.attributes={},e)for(const r in e)this.addAttribute(r,e[r]);this.instanceCount=t.instanceCount??1,s&&this.addIndex(s),this.topology=n||"triangle-list"}onBufferUpdate(){this._boundsDirty=!0,this.emit("update",this)}getAttribute(t){return this.attributes[t]}getIndex(){return this.indexBuffer}getBuffer(t){return this.getAttribute(t).buffer}getSize(){for(const t in this.attributes){const e=this.attributes[t];return e.buffer.data.length/(e.stride/4||e.size)}return 0}addAttribute(t,e){const s=Da(e);this.buffers.indexOf(s.buffer)===-1&&(this.buffers.push(s.buffer),s.buffer.on("update",this.onBufferUpdate,this),s.buffer.on("change",this.onBufferUpdate,this)),this.attributes[t]=s}addIndex(t){this.indexBuffer=dr(t,!0),this.buffers.push(this.indexBuffer)}get bounds(){return this._boundsDirty?(this._boundsDirty=!1,Ga(this,"aPosition",this._bounds)):this._bounds}destroy(t=!1){this.emit("destroy",this),this.removeAllListeners(),t&&this.buffers.forEach(e=>e.destroy()),this.attributes=null,this.buffers=null,this.indexBuffer=null,this._bounds=null}}const $a=new Float32Array(1),Ua=new Uint32Array(1);class La extends Ha{constructor(){const e=new Ae({data:$a,label:"attribute-batch-buffer",usage:At.VERTEX|At.COPY_DST,shrinkToFit:!1}),s=new Ae({data:Ua,label:"index-batch-buffer",usage:At.INDEX|At.COPY_DST,shrinkToFit:!1}),n=6*4;super({attributes:{aPosition:{buffer:e,format:"float32x2",stride:n,offset:0},aUV:{buffer:e,format:"float32x2",stride:n,offset:2*4},aColor:{buffer:e,format:"unorm8x4",stride:n,offset:4*4},aTextureIdAndRound:{buffer:e,format:"uint16x2",stride:n,offset:5*4}},indexBuffer:s})}}function Wi(i,t,e){if(i)for(const s in i){const n=s.toLocaleLowerCase(),r=t[n];if(r){let o=i[s];s==="header"&&(o=o.replace(/@in\s+[^;]+;\s*/g,"").replace(/@out\s+[^;]+;\s*/g,"")),e&&r.push(`//----${e}----//`),r.push(o)}else kt(`${s} placement hook does not exist in shader`)}}const qa=/\{\{(.*?)\}\}/g;function Oi(i){var s;const t={};return(((s=i.match(qa))==null?void 0:s.map(n=>n.replace(/[{()}]/g,"")))??[]).forEach(n=>{t[n]=[]}),t}function Vi(i,t){let e;const s=/@in\s+([^;]+);/g;for(;(e=s.exec(i))!==null;)t.push(e[1])}function Yi(i,t,e=!1){const s=[];Vi(t,s),i.forEach(a=>{a.header&&Vi(a.header,s)});const n=s;e&&n.sort();const r=n.map((a,h)=>`       @location(${h}) ${a},`).join(`
`);let o=t.replace(/@in\s+[^;]+;\s*/g,"");return o=o.replace("{{in}}",`
${r}
`),o}function Xi(i,t){let e;const s=/@out\s+([^;]+);/g;for(;(e=s.exec(i))!==null;)t.push(e[1])}function za(i){const e=/\b(\w+)\s*:/g.exec(i);return e?e[1]:""}function Na(i){const t=/@.*?\s+/g;return i.replace(t,"")}function Wa(i,t){const e=[];Xi(t,e),i.forEach(h=>{h.header&&Xi(h.header,e)});let s=0;const n=e.sort().map(h=>h.indexOf("builtin")>-1?h:`@location(${s++}) ${h}`).join(`,
`),r=e.sort().map(h=>`       var ${Na(h)};`).join(`
`),o=`return VSOutput(
            ${e.sort().map(h=>` ${za(h)}`).join(`,
`)});`;let a=t.replace(/@out\s+[^;]+;\s*/g,"");return a=a.replace("{{struct}}",`
${n}
`),a=a.replace("{{start}}",`
${r}
`),a=a.replace("{{return}}",`
${o}
`),a}function ji(i,t){let e=i;for(const s in t){const n=t[s];n.join(`
`).length?e=e.replace(`{{${s}}}`,`//-----${s} START-----//
${n.join(`
`)}
//----${s} FINISH----//`):e=e.replace(`{{${s}}}`,"")}return e}const jt=Object.create(null),_s=new Map;let Oa=0;function Va({template:i,bits:t}){const e=fr(i,t);if(jt[e])return jt[e];const{vertex:s,fragment:n}=Xa(i,t);return jt[e]=pr(s,n,t),jt[e]}function Ya({template:i,bits:t}){const e=fr(i,t);return jt[e]||(jt[e]=pr(i.vertex,i.fragment,t)),jt[e]}function Xa(i,t){const e=t.map(o=>o.vertex).filter(o=>!!o),s=t.map(o=>o.fragment).filter(o=>!!o);let n=Yi(e,i.vertex,!0);n=Wa(e,n);const r=Yi(s,i.fragment,!0);return{vertex:n,fragment:r}}function fr(i,t){return t.map(e=>(_s.has(e)||_s.set(e,Oa++),_s.get(e))).sort((e,s)=>e-s).join("-")+i.vertex+i.fragment}function pr(i,t,e){const s=Oi(i),n=Oi(t);return e.forEach(r=>{Wi(r.vertex,s,r.name),Wi(r.fragment,n,r.name)}),{vertex:ji(i,s),fragment:ji(t,n)}}const ja=`
    @in aPosition: vec2<f32>;
    @in aUV: vec2<f32>;

    @out @builtin(position) vPosition: vec4<f32>;
    @out vUV : vec2<f32>;
    @out vColor : vec4<f32>;

    {{header}}

    struct VSOutput {
        {{struct}}
    };

    @vertex
    fn main( {{in}} ) -> VSOutput {

        var worldTransformMatrix = globalUniforms.uWorldTransformMatrix;
        var modelMatrix = mat3x3<f32>(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        var position = aPosition;
        var uv = aUV;

        {{start}}
        
        vColor = vec4<f32>(1., 1., 1., 1.);

        {{main}}

        vUV = uv;

        var modelViewProjectionMatrix = globalUniforms.uProjectionMatrix * worldTransformMatrix * modelMatrix;

        vPosition =  vec4<f32>((modelViewProjectionMatrix *  vec3<f32>(position, 1.0)).xy, 0.0, 1.0);
       
        vColor *= globalUniforms.uWorldColorAlpha;

        {{end}}

        {{return}}
    };
`,Ka=`
    @in vUV : vec2<f32>;
    @in vColor : vec4<f32>;
   
    {{header}}

    @fragment
    fn main(
        {{in}}
      ) -> @location(0) vec4<f32> {
        
        {{start}}

        var outColor:vec4<f32>;
      
        {{main}}
        
        var finalColor:vec4<f32> = outColor * vColor;

        {{end}}

        return finalColor;
      };
`,Za=`
    in vec2 aPosition;
    in vec2 aUV;

    out vec4 vColor;
    out vec2 vUV;

    {{header}}

    void main(void){

        mat3 worldTransformMatrix = uWorldTransformMatrix;
        mat3 modelMatrix = mat3(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        vec2 position = aPosition;
        vec2 uv = aUV;
        
        {{start}}
        
        vColor = vec4(1.);
        
        {{main}}
        
        vUV = uv;
        
        mat3 modelViewProjectionMatrix = uProjectionMatrix * worldTransformMatrix * modelMatrix;

        gl_Position = vec4((modelViewProjectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);

        vColor *= uWorldColorAlpha;

        {{end}}
    }
`,Qa=`
   
    in vec4 vColor;
    in vec2 vUV;

    out vec4 finalColor;

    {{header}}

    void main(void) {
        
        {{start}}

        vec4 outColor;
      
        {{main}}
        
        finalColor = outColor * vColor;
        
        {{end}}
    }
`,Ja={name:"global-uniforms-bit",vertex:{header:`
        struct GlobalUniforms {
            uProjectionMatrix:mat3x3<f32>,
            uWorldTransformMatrix:mat3x3<f32>,
            uWorldColorAlpha: vec4<f32>,
            uResolution: vec2<f32>,
        }

        @group(0) @binding(0) var<uniform> globalUniforms : GlobalUniforms;
        `}},th={name:"global-uniforms-bit",vertex:{header:`
          uniform mat3 uProjectionMatrix;
          uniform mat3 uWorldTransformMatrix;
          uniform vec4 uWorldColorAlpha;
          uniform vec2 uResolution;
        `}};function eh({bits:i,name:t}){const e=Va({template:{fragment:Ka,vertex:ja},bits:[Ja,...i]});return Ze.from({name:t,vertex:{source:e.vertex,entryPoint:"main"},fragment:{source:e.fragment,entryPoint:"main"}})}function sh({bits:i,name:t}){return new jn({name:t,...Ya({template:{vertex:Za,fragment:Qa},bits:[th,...i]})})}const ih={name:"color-bit",vertex:{header:`
            @in aColor: vec4<f32>;
        `,main:`
            vColor *= vec4<f32>(aColor.rgb * aColor.a, aColor.a);
        `}},nh={name:"color-bit",vertex:{header:`
            in vec4 aColor;
        `,main:`
            vColor *= vec4(aColor.rgb * aColor.a, aColor.a);
        `}},bs={};function rh(i){const t=[];if(i===1)t.push("@group(1) @binding(0) var textureSource1: texture_2d<f32>;"),t.push("@group(1) @binding(1) var textureSampler1: sampler;");else{let e=0;for(let s=0;s<i;s++)t.push(`@group(1) @binding(${e++}) var textureSource${s+1}: texture_2d<f32>;`),t.push(`@group(1) @binding(${e++}) var textureSampler${s+1}: sampler;`)}return t.join(`
`)}function oh(i){const t=[];if(i===1)t.push("outColor = textureSampleGrad(textureSource1, textureSampler1, vUV, uvDx, uvDy);");else{t.push("switch vTextureId {");for(let e=0;e<i;e++)e===i-1?t.push("  default:{"):t.push(`  case ${e}:{`),t.push(`      outColor = textureSampleGrad(textureSource${e+1}, textureSampler${e+1}, vUV, uvDx, uvDy);`),t.push("      break;}");t.push("}")}return t.join(`
`)}function ah(i){return bs[i]||(bs[i]={name:"texture-batch-bit",vertex:{header:`
                @in aTextureIdAndRound: vec2<u32>;
                @out @interpolate(flat) vTextureId : u32;
            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1)
                {
                    vPosition = vec4<f32>(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
                }
            `},fragment:{header:`
                @in @interpolate(flat) vTextureId: u32;

                ${rh(i)}
            `,main:`
                var uvDx = dpdx(vUV);
                var uvDy = dpdy(vUV);

                ${oh(i)}
            `}}),bs[i]}const ws={};function hh(i){const t=[];for(let e=0;e<i;e++)e>0&&t.push("else"),e<i-1&&t.push(`if(vTextureId < ${e}.5)`),t.push("{"),t.push(`	outColor = texture(uTextures[${e}], vUV);`),t.push("}");return t.join(`
`)}function lh(i){return ws[i]||(ws[i]={name:"texture-batch-bit",vertex:{header:`
                in vec2 aTextureIdAndRound;
                out float vTextureId;

            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1.)
                {
                    gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
                }
            `},fragment:{header:`
                in float vTextureId;

                uniform sampler2D uTextures[${i}];

            `,main:`

                ${hh(i)}
            `}}),ws[i]}const ch={name:"round-pixels-bit",vertex:{header:`
            fn roundPixels(position: vec2<f32>, targetSize: vec2<f32>) -> vec2<f32> 
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},uh={name:"round-pixels-bit",vertex:{header:`   
            vec2 roundPixels(vec2 position, vec2 targetSize)
            {       
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Ki={};function dh(i){let t=Ki[i];if(t)return t;const e=new Int32Array(i);for(let s=0;s<i;s++)e[s]=s;return t=Ki[i]=new Jn({uTextures:{value:e,type:"i32",size:i}},{isStatic:!0}),t}class fh extends js{constructor(t){const e=sh({name:"batch",bits:[nh,lh(t),uh]}),s=eh({name:"batch",bits:[ih,ah(t),ch]});super({glProgram:e,gpuProgram:s,resources:{batchSamplers:dh(t)}})}}let Zi=null;const gr=class mr extends Fa{constructor(){super(...arguments),this.geometry=new La,this.shader=Zi||(Zi=new fh(this.maxTextures)),this.name=mr.extension.name,this.vertexSize=6}packAttributes(t,e,s,n,r){const o=r<<16|t.roundPixels&65535,a=t.transform,h=a.a,c=a.b,l=a.c,u=a.d,f=a.tx,d=a.ty,{positions:x,uvs:_}=t,g=t.color,b=t.attributeOffset,w=b+t.attributeSize;for(let S=b;S<w;S++){const M=S*2,I=x[M],E=x[M+1];e[n++]=h*I+l*E+f,e[n++]=u*E+c*I+d,e[n++]=_[M],e[n++]=_[M+1],s[n++]=g,s[n++]=o}}packQuadAttributes(t,e,s,n,r){const o=t.texture,a=t.transform,h=a.a,c=a.b,l=a.c,u=a.d,f=a.tx,d=a.ty,x=t.bounds,_=x.maxX,g=x.minX,b=x.maxY,w=x.minY,S=o.uvs,M=t.color,I=r<<16|t.roundPixels&65535;e[n+0]=h*g+l*w+f,e[n+1]=u*w+c*g+d,e[n+2]=S.x0,e[n+3]=S.y0,s[n+4]=M,s[n+5]=I,e[n+6]=h*_+l*w+f,e[n+7]=u*w+c*_+d,e[n+8]=S.x1,e[n+9]=S.y1,s[n+10]=M,s[n+11]=I,e[n+12]=h*_+l*b+f,e[n+13]=u*b+c*_+d,e[n+14]=S.x2,e[n+15]=S.y2,s[n+16]=M,s[n+17]=I,e[n+18]=h*g+l*b+f,e[n+19]=u*b+c*g+d,e[n+20]=S.x3,e[n+21]=S.y3,s[n+22]=M,s[n+23]=I}};gr.extension={type:[rt.Batcher],name:"default"};let ph=gr;function gh(i,t,e,s,n,r,o,a=null){let h=0;e*=t,n*=r;const c=a.a,l=a.b,u=a.c,f=a.d,d=a.tx,x=a.ty;for(;h<o;){const _=i[e],g=i[e+1];s[n]=c*_+u*g+d,s[n+1]=l*_+f*g+x,n+=r,e+=t,h++}}function mh(i,t,e,s){let n=0;for(t*=e;n<s;)i[t]=0,i[t+1]=0,t+=e,n++}function xr(i,t,e,s,n){const r=t.a,o=t.b,a=t.c,h=t.d,c=t.tx,l=t.ty;e||(e=0),s||(s=2),n||(n=i.length/s-e);let u=e*s;for(let f=0;f<n;f++){const d=i[u],x=i[u+1];i[u]=r*d+a*x+c,i[u+1]=o*d+h*x+l,u+=s}}const xh=new it;class yr{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const t=this.baseColor,e=t>>16|t&65280|(t&255)<<16,s=this.renderable;return s?An(e,s.groupColor)+(this.alpha*s.groupAlpha*255<<24):e+(this.alpha*255<<24)}get transform(){var t;return((t=this.renderable)==null?void 0:t.groupTransform)||xh}copyTo(t){t.indexOffset=this.indexOffset,t.indexSize=this.indexSize,t.attributeOffset=this.attributeOffset,t.attributeSize=this.attributeSize,t.baseColor=this.baseColor,t.alpha=this.alpha,t.texture=this.texture,t.geometryData=this.geometryData,t.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}}const Pe={extension:{type:rt.ShapeBuilder,name:"circle"},build(i,t){let e,s,n,r,o,a;if(i.type==="circle"){const M=i;e=M.x,s=M.y,o=a=M.radius,n=r=0}else if(i.type==="ellipse"){const M=i;e=M.x,s=M.y,o=M.halfWidth,a=M.halfHeight,n=r=0}else{const M=i,I=M.width/2,E=M.height/2;e=M.x+I,s=M.y+E,o=a=Math.max(0,Math.min(M.radius,Math.min(I,E))),n=I-o,r=E-a}if(!(o>=0&&a>=0&&n>=0&&r>=0))return t;const h=Math.ceil(2.3*Math.sqrt(o+a)),c=h*8+(n?4:0)+(r?4:0);if(c===0)return t;if(h===0)return t[0]=t[6]=e+n,t[1]=t[3]=s+r,t[2]=t[4]=e-n,t[5]=t[7]=s-r,t;let l=0,u=h*4+(n?2:0)+2,f=u,d=c,x=n+o,_=r,g=e+x,b=e-x,w=s+_;if(t[l++]=g,t[l++]=w,t[--u]=w,t[--u]=b,r){const M=s-_;t[f++]=b,t[f++]=M,t[--d]=M,t[--d]=g}for(let M=1;M<h;M++){const I=Math.PI/2*(M/h),E=n+Math.cos(I)*o,P=r+Math.sin(I)*a,O=e+E,W=e-E,$=s+P,U=s-P;t[l++]=O,t[l++]=$,t[--u]=$,t[--u]=W,t[f++]=W,t[f++]=U,t[--d]=U,t[--d]=O}x=n,_=r+a,g=e+x,b=e-x,w=s+_;const S=s-_;return t[l++]=g,t[l++]=w,t[--d]=S,t[--d]=g,n&&(t[l++]=b,t[l++]=w,t[--d]=S,t[--d]=b),t},triangulate(i,t,e,s,n,r){if(i.length===0)return;let o=0,a=0;for(let l=0;l<i.length;l+=2)o+=i[l],a+=i[l+1];o/=i.length/2,a/=i.length/2;let h=s;t[h*e]=o,t[h*e+1]=a;const c=h++;for(let l=0;l<i.length;l+=2)t[h*e]=i[l],t[h*e+1]=i[l+1],l>0&&(n[r++]=h,n[r++]=c,n[r++]=h-1),h++;n[r++]=c+1,n[r++]=c,n[r++]=h-1}},yh={...Pe,extension:{...Pe.extension,name:"ellipse"}},_h={...Pe,extension:{...Pe.extension,name:"roundedRectangle"}},_r=1e-4,Qi=1e-4;function bh(i){const t=i.length;if(t<6)return 1;let e=0;for(let s=0,n=i[t-2],r=i[t-1];s<t;s+=2){const o=i[s],a=i[s+1];e+=(o-n)*(a+r),n=o,r=a}return e<0?-1:1}function Ji(i,t,e,s,n,r,o,a){const h=i-e*n,c=t-s*n,l=i+e*r,u=t+s*r;let f,d;o?(f=s,d=-e):(f=-s,d=e);const x=h+f,_=c+d,g=l+f,b=u+d;return a.push(x,_),a.push(g,b),2}function Zt(i,t,e,s,n,r,o,a){const h=e-i,c=s-t;let l=Math.atan2(h,c),u=Math.atan2(n-i,r-t);a&&l<u?l+=Math.PI*2:!a&&l>u&&(u+=Math.PI*2);let f=l;const d=u-l,x=Math.abs(d),_=Math.sqrt(h*h+c*c),g=(15*x*Math.sqrt(_)/Math.PI>>0)+1,b=d/g;if(f+=b,a){o.push(i,t),o.push(e,s);for(let w=1,S=f;w<g;w++,S+=b)o.push(i,t),o.push(i+Math.sin(S)*_,t+Math.cos(S)*_);o.push(i,t),o.push(n,r)}else{o.push(e,s),o.push(i,t);for(let w=1,S=f;w<g;w++,S+=b)o.push(i+Math.sin(S)*_,t+Math.cos(S)*_),o.push(i,t);o.push(n,r),o.push(i,t)}return g*2}function wh(i,t,e,s,n,r){const o=_r;if(i.length===0)return;const a=t;let h=a.alignment;if(t.alignment!==.5){let C=bh(i);h=(h-.5)*C+.5}const c=new ct(i[0],i[1]),l=new ct(i[i.length-2],i[i.length-1]),u=s,f=Math.abs(c.x-l.x)<o&&Math.abs(c.y-l.y)<o;if(u){i=i.slice(),f&&(i.pop(),i.pop(),l.set(i[i.length-2],i[i.length-1]));const C=(c.x+l.x)*.5,v=(l.y+c.y)*.5;i.unshift(C,v),i.push(C,v)}const d=n,x=i.length/2;let _=i.length;const g=d.length/2,b=a.width/2,w=b*b,S=a.miterLimit*a.miterLimit;let M=i[0],I=i[1],E=i[2],P=i[3],O=0,W=0,$=-(I-P),U=M-E,nt=0,st=0,xt=Math.sqrt($*$+U*U);$/=xt,U/=xt,$*=b,U*=b;const z=h,p=(1-z)*2,m=z*2;u||(a.cap==="round"?_+=Zt(M-$*(p-m)*.5,I-U*(p-m)*.5,M-$*p,I-U*p,M+$*m,I+U*m,d,!0)+2:a.cap==="square"&&(_+=Ji(M,I,$,U,p,m,!0,d))),d.push(M-$*p,I-U*p),d.push(M+$*m,I+U*m);for(let C=1;C<x-1;++C){M=i[(C-1)*2],I=i[(C-1)*2+1],E=i[C*2],P=i[C*2+1],O=i[(C+1)*2],W=i[(C+1)*2+1],$=-(I-P),U=M-E,xt=Math.sqrt($*$+U*U),$/=xt,U/=xt,$*=b,U*=b,nt=-(P-W),st=E-O,xt=Math.sqrt(nt*nt+st*st),nt/=xt,st/=xt,nt*=b,st*=b;const v=E-M,T=I-P,A=E-O,k=W-P,F=v*A+T*k,q=T*A-k*v,Z=q<0;if(Math.abs(q)<.001*Math.abs(F)){d.push(E-$*p,P-U*p),d.push(E+$*m,P+U*m),F>=0&&(a.join==="round"?_+=Zt(E,P,E-$*p,P-U*p,E-nt*p,P-st*p,d,!1)+4:_+=2,d.push(E-nt*m,P-st*m),d.push(E+nt*p,P+st*p));continue}const X=(-$+M)*(-U+P)-(-$+E)*(-U+I),j=(-nt+O)*(-st+P)-(-nt+E)*(-st+W),lt=(v*j-A*X)/q,tt=(k*X-T*j)/q,Pt=(lt-E)*(lt-E)+(tt-P)*(tt-P),bt=E+(lt-E)*p,B=P+(tt-P)*p,L=E-(lt-E)*m,G=P-(tt-P)*m,H=Math.min(v*v+T*T,A*A+k*k),J=Z?p:m,et=H+J*J*w;Pt<=et?a.join==="bevel"||Pt/w>S?(Z?(d.push(bt,B),d.push(E+$*m,P+U*m),d.push(bt,B),d.push(E+nt*m,P+st*m)):(d.push(E-$*p,P-U*p),d.push(L,G),d.push(E-nt*p,P-st*p),d.push(L,G)),_+=2):a.join==="round"?Z?(d.push(bt,B),d.push(E+$*m,P+U*m),_+=Zt(E,P,E+$*m,P+U*m,E+nt*m,P+st*m,d,!0)+4,d.push(bt,B),d.push(E+nt*m,P+st*m)):(d.push(E-$*p,P-U*p),d.push(L,G),_+=Zt(E,P,E-$*p,P-U*p,E-nt*p,P-st*p,d,!1)+4,d.push(E-nt*p,P-st*p),d.push(L,G)):(d.push(bt,B),d.push(L,G)):(d.push(E-$*p,P-U*p),d.push(E+$*m,P+U*m),a.join==="round"?Z?_+=Zt(E,P,E+$*m,P+U*m,E+nt*m,P+st*m,d,!0)+2:_+=Zt(E,P,E-$*p,P-U*p,E-nt*p,P-st*p,d,!1)+2:a.join==="miter"&&Pt/w<=S&&(Z?(d.push(L,G),d.push(L,G)):(d.push(bt,B),d.push(bt,B)),_+=2),d.push(E-nt*p,P-st*p),d.push(E+nt*m,P+st*m),_+=2)}M=i[(x-2)*2],I=i[(x-2)*2+1],E=i[(x-1)*2],P=i[(x-1)*2+1],$=-(I-P),U=M-E,xt=Math.sqrt($*$+U*U),$/=xt,U/=xt,$*=b,U*=b,d.push(E-$*p,P-U*p),d.push(E+$*m,P+U*m),u||(a.cap==="round"?_+=Zt(E-$*(p-m)*.5,P-U*(p-m)*.5,E-$*p,P-U*p,E+$*m,P+U*m,d,!1)+2:a.cap==="square"&&(_+=Ji(E,P,$,U,p,m,!1,d)));const y=Qi*Qi;for(let C=g;C<_+g-2;++C)M=d[C*2],I=d[C*2+1],E=d[(C+1)*2],P=d[(C+1)*2+1],O=d[(C+2)*2],W=d[(C+2)*2+1],!(Math.abs(M*(P-W)+E*(W-I)+O*(I-P))<y)&&r.push(C,C+1,C+2)}function Sh(i,t,e,s){const n=_r;if(i.length===0)return;const r=i[0],o=i[1],a=i[i.length-2],h=i[i.length-1],c=t||Math.abs(r-a)<n&&Math.abs(o-h)<n,l=e,u=i.length/2,f=l.length/2;for(let d=0;d<u;d++)l.push(i[d*2]),l.push(i[d*2+1]);for(let d=0;d<u-1;d++)s.push(f+d,f+d+1);c&&s.push(f+u-1,f)}function br(i,t,e,s,n,r,o){const a=ca(i,t,2);if(!a)return;for(let c=0;c<a.length;c+=3)r[o++]=a[c]+n,r[o++]=a[c+1]+n,r[o++]=a[c+2]+n;let h=n*s;for(let c=0;c<i.length;c+=2)e[h]=i[c],e[h+1]=i[c+1],h+=s}const Ch=[],Mh={extension:{type:rt.ShapeBuilder,name:"polygon"},build(i,t){for(let e=0;e<i.points.length;e++)t[e]=i.points[e];return t},triangulate(i,t,e,s,n,r){br(i,Ch,t,e,s,n,r)}},vh={extension:{type:rt.ShapeBuilder,name:"rectangle"},build(i,t){const e=i,s=e.x,n=e.y,r=e.width,o=e.height;return r>=0&&o>=0&&(t[0]=s,t[1]=n,t[2]=s+r,t[3]=n,t[4]=s+r,t[5]=n+o,t[6]=s,t[7]=n+o),t},triangulate(i,t,e,s,n,r){let o=0;s*=e,t[s+o]=i[0],t[s+o+1]=i[1],o+=e,t[s+o]=i[2],t[s+o+1]=i[3],o+=e,t[s+o]=i[6],t[s+o+1]=i[7],o+=e,t[s+o]=i[4],t[s+o+1]=i[5],o+=e;const a=s/e;n[r++]=a,n[r++]=a+1,n[r++]=a+2,n[r++]=a+1,n[r++]=a+3,n[r++]=a+2}},Th={extension:{type:rt.ShapeBuilder,name:"triangle"},build(i,t){return t[0]=i.x,t[1]=i.y,t[2]=i.x2,t[3]=i.y2,t[4]=i.x3,t[5]=i.y3,t},triangulate(i,t,e,s,n,r){let o=0;s*=e,t[s+o]=i[0],t[s+o+1]=i[1],o+=e,t[s+o]=i[2],t[s+o+1]=i[3],o+=e,t[s+o]=i[4],t[s+o+1]=i[5];const a=s/e;n[r++]=a,n[r++]=a+1,n[r++]=a+2}},Ah=new it,Ph=new Ct;function Eh(i,t,e,s){const n=t.matrix?i.copyFrom(t.matrix).invert():i.identity();if(t.textureSpace==="local"){const r=e.getBounds(Ph);n.translate(-r.x,-r.y),n.scale(1/r.width,1/r.height)}else{n.translate(t.texture.frame.x,t.texture.frame.y),n.scale(1/t.texture.source.width,1/t.texture.source.height);const r=t.texture.source.style;r.addressMode==="clamp-to-edge"&&(r.addressMode="repeat",r.update())}return s&&n.append(Ah.copyFrom(s).invert()),n}const Qe={};Ft.handleByMap(rt.ShapeBuilder,Qe);Ft.add(vh,Mh,Th,Pe,yh,_h);const kh=new Ct,Ih=new it;function Bh(i,t){const{geometryData:e,batches:s}=t;s.length=0,e.indices.length=0,e.vertices.length=0,e.uvs.length=0;for(let n=0;n<i.instructions.length;n++){const r=i.instructions[n];if(r.action==="texture")Rh(r.data,s,e);else if(r.action==="fill"||r.action==="stroke"){const o=r.action==="stroke",a=r.data.path.shapePath,h=r.data.style,c=r.data.hole;o&&c&&tn(c.shapePath,h,!0,s,e),c&&(a.shapePrimitives[a.shapePrimitives.length-1].holes=c.shapePath.shapePrimitives),tn(a,h,o,s,e)}}}function Rh(i,t,e){const{vertices:s,uvs:n,indices:r}=e,o=r.length,a=s.length/2,h=[],c=Qe.rectangle,l=kh,u=i.image;l.x=i.dx,l.y=i.dy,l.width=i.dw,l.height=i.dh;const f=i.transform;c.build(l,h),f&&xr(h,f),c.triangulate(h,s,2,a,r,o);const d=u.uvs;n.push(d.x0,d.y0,d.x1,d.y1,d.x3,d.y3,d.x2,d.y2);const x=Vt.get(yr);x.indexOffset=o,x.indexSize=r.length-o,x.attributeOffset=a,x.attributeSize=s.length/2-a,x.baseColor=i.style,x.alpha=i.alpha,x.texture=u,x.geometryData=e,t.push(x)}function tn(i,t,e,s,n){const{vertices:r,uvs:o,indices:a}=n;i.shapePrimitives.forEach(({shape:h,transform:c,holes:l})=>{const u=a.length,f=r.length/2,d=[],x=Qe[h.type];let _="triangle-list";if(x.build(h,d),c&&xr(d,c),e){const S=h.closePath??!0,M=t;M.pixelLine?(Sh(d,S,r,a),_="line-list"):wh(d,M,!1,S,r,a)}else if(l){const S=[],M=d.slice();Fh(l).forEach(E=>{S.push(M.length/2),M.push(...E)}),br(M,S,r,2,f,a,u)}else x.triangulate(d,r,2,f,a,u);const g=o.length/2,b=t.texture;if(b!==ht.WHITE){const S=Eh(Ih,t,h,c);gh(r,2,f,o,g,2,r.length/2-f,S)}else mh(o,g,2,r.length/2-f);const w=Vt.get(yr);w.indexOffset=u,w.indexSize=a.length-u,w.attributeOffset=f,w.attributeSize=r.length/2-f,w.baseColor=t.color,w.alpha=t.alpha,w.texture=b,w.geometryData=n,w.topology=_,s.push(w)})}function Fh(i){const t=[];for(let e=0;e<i.length;e++){const s=i[e].shape,n=[];Qe[s.type].build(s,n),t.push(n)}return t}class Gh{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class Dh{constructor(){this.batcher=new ph,this.instructions=new In}init(){this.instructions.reset()}get geometry(){return at(Or,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const ei=class Ls{constructor(t){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),t.renderableGC.addManagedHash(this,"_gpuContextHash"),t.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(t){Ls.defaultOptions.bezierSmoothness=(t==null?void 0:t.bezierSmoothness)??Ls.defaultOptions.bezierSmoothness}getContextRenderData(t){return this._graphicsDataContextHash[t.uid]||this._initContextRenderData(t)}updateGpuContext(t){let e=this._gpuContextHash[t.uid]||this._initContext(t);if(t.dirty){e?this._cleanGraphicsContextData(t):e=this._initContext(t),Bh(t,e);const s=t.batchMode;t.customShader||s==="no-batch"?e.isBatchable=!1:s==="auto"&&(e.isBatchable=e.geometryData.vertices.length<400),t.dirty=!1}return e}getGpuContext(t){return this._gpuContextHash[t.uid]||this._initContext(t)}_initContextRenderData(t){const e=Vt.get(Dh),{batches:s,geometryData:n}=this._gpuContextHash[t.uid],r=n.vertices.length,o=n.indices.length;for(let l=0;l<s.length;l++)s[l].applyTransform=!1;const a=e.batcher;a.ensureAttributeBuffer(r),a.ensureIndexBuffer(o),a.begin();for(let l=0;l<s.length;l++){const u=s[l];a.add(u)}a.finish(e.instructions);const h=a.geometry;h.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),h.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const c=a.batches;for(let l=0;l<c.length;l++){const u=c[l];u.bindGroup=Pa(u.textures.textures,u.textures.count)}return this._graphicsDataContextHash[t.uid]=e,e}_initContext(t){const e=new Gh;return e.context=t,this._gpuContextHash[t.uid]=e,t.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]}onGraphicsContextDestroy(t){this._cleanGraphicsContextData(t),t.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]=null}_cleanGraphicsContextData(t){const e=this._gpuContextHash[t.uid];e.isBatchable||this._graphicsDataContextHash[t.uid]&&(Vt.return(this.getContextRenderData(t)),this._graphicsDataContextHash[t.uid]=null),e.batches&&e.batches.forEach(s=>{Vt.return(s)})}destroy(){for(const t in this._gpuContextHash)this._gpuContextHash[t]&&this.onGraphicsContextDestroy(this._gpuContextHash[t].context)}};ei.extension={type:[rt.WebGLSystem,rt.WebGPUSystem,rt.CanvasSystem],name:"graphicsContext"};ei.defaultOptions={bezierSmoothness:.5};let wr=ei;const Hh=8,Le=11920929e-14,$h=1;function Sr(i,t,e,s,n,r,o,a,h,c){const u=Math.min(.99,Math.max(0,c??wr.defaultOptions.bezierSmoothness));let f=($h-u)/1;return f*=f,Uh(t,e,s,n,r,o,a,h,i,f),i}function Uh(i,t,e,s,n,r,o,a,h,c){qs(i,t,e,s,n,r,o,a,h,c,0),h.push(o,a)}function qs(i,t,e,s,n,r,o,a,h,c,l){if(l>Hh)return;const u=(i+e)/2,f=(t+s)/2,d=(e+n)/2,x=(s+r)/2,_=(n+o)/2,g=(r+a)/2,b=(u+d)/2,w=(f+x)/2,S=(d+_)/2,M=(x+g)/2,I=(b+S)/2,E=(w+M)/2;if(l>0){let P=o-i,O=a-t;const W=Math.abs((e-o)*O-(s-a)*P),$=Math.abs((n-o)*O-(r-a)*P);if(W>Le&&$>Le){if((W+$)*(W+$)<=c*(P*P+O*O)){h.push(I,E);return}}else if(W>Le){if(W*W<=c*(P*P+O*O)){h.push(I,E);return}}else if($>Le){if($*$<=c*(P*P+O*O)){h.push(I,E);return}}else if(P=I-(i+o)/2,O=E-(t+a)/2,P*P+O*O<=c){h.push(I,E);return}}qs(i,t,u,f,b,w,I,E,h,c,l+1),qs(I,E,S,M,_,g,o,a,h,c,l+1)}const Lh=8,qh=11920929e-14,zh=1;function Nh(i,t,e,s,n,r,o,a){const c=Math.min(.99,Math.max(0,a??wr.defaultOptions.bezierSmoothness));let l=(zh-c)/1;return l*=l,Wh(t,e,s,n,r,o,i,l),i}function Wh(i,t,e,s,n,r,o,a){zs(o,i,t,e,s,n,r,a,0),o.push(n,r)}function zs(i,t,e,s,n,r,o,a,h){if(h>Lh)return;const c=(t+s)/2,l=(e+n)/2,u=(s+r)/2,f=(n+o)/2,d=(c+u)/2,x=(l+f)/2;let _=r-t,g=o-e;const b=Math.abs((s-r)*g-(n-o)*_);if(b>qh){if(b*b<=a*(_*_+g*g)){i.push(d,x);return}}else if(_=d-(t+r)/2,g=x-(e+o)/2,_*_+g*g<=a){i.push(d,x);return}zs(i,t,e,c,l,d,x,a,h+1),zs(i,d,x,u,f,r,o,a,h+1)}function Cr(i,t,e,s,n,r,o,a){let h=Math.abs(n-r);(!o&&n>r||o&&r>n)&&(h=2*Math.PI-h),a||(a=Math.max(6,Math.floor(6*Math.pow(s,1/3)*(h/Math.PI)))),a=Math.max(a,3);let c=h/a,l=n;c*=o?-1:1;for(let u=0;u<a+1;u++){const f=Math.cos(l),d=Math.sin(l),x=t+f*s,_=e+d*s;i.push(x,_),l+=c}}function Oh(i,t,e,s,n,r){const o=i[i.length-2],h=i[i.length-1]-e,c=o-t,l=n-e,u=s-t,f=Math.abs(h*u-c*l);if(f<1e-8||r===0){(i[i.length-2]!==t||i[i.length-1]!==e)&&i.push(t,e);return}const d=h*h+c*c,x=l*l+u*u,_=h*l+c*u,g=r*Math.sqrt(d)/f,b=r*Math.sqrt(x)/f,w=g*_/d,S=b*_/x,M=g*u+b*c,I=g*l+b*h,E=c*(b+w),P=h*(b+w),O=u*(g+S),W=l*(g+S),$=Math.atan2(P-I,E-M),U=Math.atan2(W-I,O-M);Cr(i,M+t,I+e,r,$,U,c*l>u*h)}const ve=Math.PI*2,Ss={centerX:0,centerY:0,ang1:0,ang2:0},Cs=({x:i,y:t},e,s,n,r,o,a,h)=>{i*=e,t*=s;const c=n*i-r*t,l=r*i+n*t;return h.x=c+o,h.y=l+a,h};function Vh(i,t){const e=t===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(t/4),s=t===1.5707963267948966?.551915024494:e,n=Math.cos(i),r=Math.sin(i),o=Math.cos(i+t),a=Math.sin(i+t);return[{x:n-r*s,y:r+n*s},{x:o+a*s,y:a-o*s},{x:o,y:a}]}const en=(i,t,e,s)=>{const n=i*s-t*e<0?-1:1;let r=i*e+t*s;return r>1&&(r=1),r<-1&&(r=-1),n*Math.acos(r)},Yh=(i,t,e,s,n,r,o,a,h,c,l,u,f)=>{const d=Math.pow(n,2),x=Math.pow(r,2),_=Math.pow(l,2),g=Math.pow(u,2);let b=d*x-d*g-x*_;b<0&&(b=0),b/=d*g+x*_,b=Math.sqrt(b)*(o===a?-1:1);const w=b*n/r*u,S=b*-r/n*l,M=c*w-h*S+(i+e)/2,I=h*w+c*S+(t+s)/2,E=(l-w)/n,P=(u-S)/r,O=(-l-w)/n,W=(-u-S)/r,$=en(1,0,E,P);let U=en(E,P,O,W);a===0&&U>0&&(U-=ve),a===1&&U<0&&(U+=ve),f.centerX=M,f.centerY=I,f.ang1=$,f.ang2=U};function Xh(i,t,e,s,n,r,o,a=0,h=0,c=0){if(r===0||o===0)return;const l=Math.sin(a*ve/360),u=Math.cos(a*ve/360),f=u*(t-s)/2+l*(e-n)/2,d=-l*(t-s)/2+u*(e-n)/2;if(f===0&&d===0)return;r=Math.abs(r),o=Math.abs(o);const x=Math.pow(f,2)/Math.pow(r,2)+Math.pow(d,2)/Math.pow(o,2);x>1&&(r*=Math.sqrt(x),o*=Math.sqrt(x)),Yh(t,e,s,n,r,o,h,c,l,u,f,d,Ss);let{ang1:_,ang2:g}=Ss;const{centerX:b,centerY:w}=Ss;let S=Math.abs(g)/(ve/4);Math.abs(1-S)<1e-7&&(S=1);const M=Math.max(Math.ceil(S),1);g/=M;let I=i[i.length-2],E=i[i.length-1];const P={x:0,y:0};for(let O=0;O<M;O++){const W=Vh(_,g),{x:$,y:U}=Cs(W[0],r,o,u,l,b,w,P),{x:nt,y:st}=Cs(W[1],r,o,u,l,b,w,P),{x:xt,y:z}=Cs(W[2],r,o,u,l,b,w,P);Sr(i,I,E,$,U,nt,st,xt,z),I=xt,E=z,_+=g}}function jh(i,t,e){const s=(o,a)=>{const h=a.x-o.x,c=a.y-o.y,l=Math.sqrt(h*h+c*c),u=h/l,f=c/l;return{len:l,nx:u,ny:f}},n=(o,a)=>{o===0?i.moveTo(a.x,a.y):i.lineTo(a.x,a.y)};let r=t[t.length-1];for(let o=0;o<t.length;o++){const a=t[o%t.length],h=a.radius??e;if(h<=0){n(o,a),r=a;continue}const c=t[(o+1)%t.length],l=s(a,r),u=s(a,c);if(l.len<1e-4||u.len<1e-4){n(o,a),r=a;continue}let f=Math.asin(l.nx*u.ny-l.ny*u.nx),d=1,x=!1;l.nx*u.nx-l.ny*-u.ny<0?f<0?f=Math.PI+f:(f=Math.PI-f,d=-1,x=!0):f>0&&(d=-1,x=!0);const _=f/2;let g,b=Math.abs(Math.cos(_)*h/Math.sin(_));b>Math.min(l.len/2,u.len/2)?(b=Math.min(l.len/2,u.len/2),g=Math.abs(b*Math.sin(_)/Math.cos(_))):g=h;const w=a.x+u.nx*b+-u.ny*g*d,S=a.y+u.ny*b+u.nx*g*d,M=Math.atan2(l.ny,l.nx)+Math.PI/2*d,I=Math.atan2(u.ny,u.nx)-Math.PI/2*d;o===0&&i.moveTo(w+Math.cos(M)*g,S+Math.sin(M)*g),i.arc(w,S,g,M,I,x),r=a}}function Kh(i,t,e,s){const n=(a,h)=>Math.sqrt((a.x-h.x)**2+(a.y-h.y)**2),r=(a,h,c)=>({x:a.x+(h.x-a.x)*c,y:a.y+(h.y-a.y)*c}),o=t.length;for(let a=0;a<o;a++){const h=t[(a+1)%o],c=h.radius??e;if(c<=0){a===0?i.moveTo(h.x,h.y):i.lineTo(h.x,h.y);continue}const l=t[a],u=t[(a+2)%o],f=n(l,h);let d;if(f<1e-4)d=h;else{const g=Math.min(f/2,c);d=r(h,l,g/f)}const x=n(u,h);let _;if(x<1e-4)_=h;else{const g=Math.min(x/2,c);_=r(h,u,g/x)}a===0?i.moveTo(d.x,d.y):i.lineTo(d.x,d.y),i.quadraticCurveTo(h.x,h.y,_.x,_.y,s)}}const Zh=new Ct;class Qh{constructor(t){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new $t,this._graphicsPath2D=t,this.signed=t.checkForHoles}moveTo(t,e){return this.startPoly(t,e),this}lineTo(t,e){this._ensurePoly();const s=this._currentPoly.points,n=s[s.length-2],r=s[s.length-1];return(n!==t||r!==e)&&s.push(t,e),this}arc(t,e,s,n,r,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Cr(a,t,e,s,n,r,o),this}arcTo(t,e,s,n,r){this._ensurePoly();const o=this._currentPoly.points;return Oh(o,t,e,s,n,r),this}arcToSvg(t,e,s,n,r,o,a){const h=this._currentPoly.points;return Xh(h,this._currentPoly.lastX,this._currentPoly.lastY,o,a,t,e,s,n,r),this}bezierCurveTo(t,e,s,n,r,o,a){this._ensurePoly();const h=this._currentPoly;return Sr(this._currentPoly.points,h.lastX,h.lastY,t,e,s,n,r,o,a),this}quadraticCurveTo(t,e,s,n,r){this._ensurePoly();const o=this._currentPoly;return Nh(this._currentPoly.points,o.lastX,o.lastY,t,e,s,n,r),this}closePath(){return this.endPoly(!0),this}addPath(t,e){this.endPoly(),e&&!e.isIdentity()&&(t=t.clone(!0),t.transform(e));const s=this.shapePrimitives,n=s.length;for(let r=0;r<t.instructions.length;r++){const o=t.instructions[r];this[o.action](...o.data)}if(t.checkForHoles&&s.length-n>1){let r=null;for(let o=n;o<s.length;o++){const a=s[o];if(a.shape.type==="polygon"){const h=a.shape,c=r==null?void 0:r.shape;c&&c.containsPolygon(h)?(r.holes||(r.holes=[]),r.holes.push(a),s.copyWithin(o,o+1),s.length--,o--):r=a}}}return this}finish(t=!1){this.endPoly(t)}rect(t,e,s,n,r){return this.drawShape(new Ct(t,e,s,n),r),this}circle(t,e,s,n){return this.drawShape(new Qs(t,e,s),n),this}poly(t,e,s){const n=new Me(t);return n.closePath=e,this.drawShape(n,s),this}regularPoly(t,e,s,n,r=0,o){n=Math.max(n|0,3);const a=-1*Math.PI/2+r,h=Math.PI*2/n,c=[];for(let l=0;l<n;l++){const u=a-l*h;c.push(t+s*Math.cos(u),e+s*Math.sin(u))}return this.poly(c,!0,o),this}roundPoly(t,e,s,n,r,o=0,a){if(n=Math.max(n|0,3),r<=0)return this.regularPoly(t,e,s,n,o);const h=s*Math.sin(Math.PI/n)-.001;r=Math.min(r,h);const c=-1*Math.PI/2+o,l=Math.PI*2/n,u=(n-2)*Math.PI/n/2;for(let f=0;f<n;f++){const d=f*l+c,x=t+s*Math.cos(d),_=e+s*Math.sin(d),g=d+Math.PI+u,b=d-Math.PI-u,w=x+r*Math.cos(g),S=_+r*Math.sin(g),M=x+r*Math.cos(b),I=_+r*Math.sin(b);f===0?this.moveTo(w,S):this.lineTo(w,S),this.quadraticCurveTo(x,_,M,I,a)}return this.closePath()}roundShape(t,e,s=!1,n){return t.length<3?this:(s?Kh(this,t,e,n):jh(this,t,e),this.closePath())}filletRect(t,e,s,n,r){if(r===0)return this.rect(t,e,s,n);const o=Math.min(s,n)/2,a=Math.min(o,Math.max(-o,r)),h=t+s,c=e+n,l=a<0?-a:0,u=Math.abs(a);return this.moveTo(t,e+u).arcTo(t+l,e+l,t+u,e,u).lineTo(h-u,e).arcTo(h-l,e+l,h,e+u,u).lineTo(h,c-u).arcTo(h-l,c-l,t+s-u,c,u).lineTo(t+u,c).arcTo(t+l,c-l,t,c-u,u).closePath()}chamferRect(t,e,s,n,r,o){if(r<=0)return this.rect(t,e,s,n);const a=Math.min(r,Math.min(s,n)/2),h=t+s,c=e+n,l=[t+a,e,h-a,e,h,e+a,h,c-a,h-a,c,t+a,c,t,c-a,t,e+a];for(let u=l.length-1;u>=2;u-=2)l[u]===l[u-2]&&l[u-1]===l[u-3]&&l.splice(u-1,2);return this.poly(l,!0,o)}ellipse(t,e,s,n,r){return this.drawShape(new Js(t,e,s,n),r),this}roundRect(t,e,s,n,r,o){return this.drawShape(new ti(t,e,s,n,r),o),this}drawShape(t,e){return this.endPoly(),this.shapePrimitives.push({shape:t,transform:e}),this}startPoly(t,e){let s=this._currentPoly;return s&&this.endPoly(),s=new Me,s.points.push(t,e),this._currentPoly=s,this}endPoly(t=!1){const e=this._currentPoly;return e&&e.points.length>2&&(e.closePath=t,this.shapePrimitives.push({shape:e})),this._currentPoly=null,this}_ensurePoly(t=!0){if(!this._currentPoly&&(this._currentPoly=new Me,t)){const e=this.shapePrimitives[this.shapePrimitives.length-1];if(e){let s=e.shape.x,n=e.shape.y;if(e.transform&&!e.transform.isIdentity()){const r=e.transform,o=s;s=r.a*s+r.c*n+r.tx,n=r.b*o+r.d*n+r.ty}this._currentPoly.points.push(s,n)}else this._currentPoly.points.push(0,0)}}buildPath(){const t=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let e=0;e<t.instructions.length;e++){const s=t.instructions[e];this[s.action](...s.data)}this.finish()}get bounds(){const t=this._bounds;t.clear();const e=this.shapePrimitives;for(let s=0;s<e.length;s++){const n=e[s],r=n.shape.getBounds(Zh);n.transform?t.addRect(r,n.transform):t.addRect(r)}return t}}class pe{constructor(t,e=!1){this.instructions=[],this.uid=_t("graphicsPath"),this._dirty=!0,this.checkForHoles=e,typeof t=="string"?wa(t,this):this.instructions=(t==null?void 0:t.slice())??[]}get shapePath(){return this._shapePath||(this._shapePath=new Qh(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(t,e){return t=t.clone(),this.instructions.push({action:"addPath",data:[t,e]}),this._dirty=!0,this}arc(...t){return this.instructions.push({action:"arc",data:t}),this._dirty=!0,this}arcTo(...t){return this.instructions.push({action:"arcTo",data:t}),this._dirty=!0,this}arcToSvg(...t){return this.instructions.push({action:"arcToSvg",data:t}),this._dirty=!0,this}bezierCurveTo(...t){return this.instructions.push({action:"bezierCurveTo",data:t}),this._dirty=!0,this}bezierCurveToShort(t,e,s,n,r){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(ct.shared);let h=0,c=0;if(!o||o.action!=="bezierCurveTo")h=a.x,c=a.y;else{h=o.data[2],c=o.data[3];const l=a.x,u=a.y;h=l+(l-h),c=u+(u-c)}return this.instructions.push({action:"bezierCurveTo",data:[h,c,t,e,s,n,r]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...t){return this.instructions.push({action:"ellipse",data:t}),this._dirty=!0,this}lineTo(...t){return this.instructions.push({action:"lineTo",data:t}),this._dirty=!0,this}moveTo(...t){return this.instructions.push({action:"moveTo",data:t}),this}quadraticCurveTo(...t){return this.instructions.push({action:"quadraticCurveTo",data:t}),this._dirty=!0,this}quadraticCurveToShort(t,e,s){const n=this.instructions[this.instructions.length-1],r=this.getLastPoint(ct.shared);let o=0,a=0;if(!n||n.action!=="quadraticCurveTo")o=r.x,a=r.y;else{o=n.data[0],a=n.data[1];const h=r.x,c=r.y;o=h+(h-o),a=c+(c-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,t,e,s]}),this._dirty=!0,this}rect(t,e,s,n,r){return this.instructions.push({action:"rect",data:[t,e,s,n,r]}),this._dirty=!0,this}circle(t,e,s,n){return this.instructions.push({action:"circle",data:[t,e,s,n]}),this._dirty=!0,this}roundRect(...t){return this.instructions.push({action:"roundRect",data:t}),this._dirty=!0,this}poly(...t){return this.instructions.push({action:"poly",data:t}),this._dirty=!0,this}regularPoly(...t){return this.instructions.push({action:"regularPoly",data:t}),this._dirty=!0,this}roundPoly(...t){return this.instructions.push({action:"roundPoly",data:t}),this._dirty=!0,this}roundShape(...t){return this.instructions.push({action:"roundShape",data:t}),this._dirty=!0,this}filletRect(...t){return this.instructions.push({action:"filletRect",data:t}),this._dirty=!0,this}chamferRect(...t){return this.instructions.push({action:"chamferRect",data:t}),this._dirty=!0,this}star(t,e,s,n,r,o,a){r||(r=n/2);const h=-1*Math.PI/2+o,c=s*2,l=Math.PI*2/c,u=[];for(let f=0;f<c;f++){const d=f%2?r:n,x=f*l+h;u.push(t+d*Math.cos(x),e+d*Math.sin(x))}return this.poly(u,!0,a),this}clone(t=!1){const e=new pe;if(e.checkForHoles=this.checkForHoles,!t)e.instructions=this.instructions.slice();else for(let s=0;s<this.instructions.length;s++){const n=this.instructions[s];e.instructions.push({action:n.action,data:n.data.slice()})}return e}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(t){if(t.isIdentity())return this;const e=t.a,s=t.b,n=t.c,r=t.d,o=t.tx,a=t.ty;let h=0,c=0,l=0,u=0,f=0,d=0,x=0,_=0;for(let g=0;g<this.instructions.length;g++){const b=this.instructions[g],w=b.data;switch(b.action){case"moveTo":case"lineTo":h=w[0],c=w[1],w[0]=e*h+n*c+o,w[1]=s*h+r*c+a;break;case"bezierCurveTo":l=w[0],u=w[1],f=w[2],d=w[3],h=w[4],c=w[5],w[0]=e*l+n*u+o,w[1]=s*l+r*u+a,w[2]=e*f+n*d+o,w[3]=s*f+r*d+a,w[4]=e*h+n*c+o,w[5]=s*h+r*c+a;break;case"quadraticCurveTo":l=w[0],u=w[1],h=w[2],c=w[3],w[0]=e*l+n*u+o,w[1]=s*l+r*u+a,w[2]=e*h+n*c+o,w[3]=s*h+r*c+a;break;case"arcToSvg":h=w[5],c=w[6],x=w[0],_=w[1],w[0]=e*x+n*_,w[1]=s*x+r*_,w[5]=e*h+n*c+o,w[6]=s*h+r*c+a;break;case"circle":w[4]=ye(w[3],t);break;case"rect":w[4]=ye(w[4],t);break;case"ellipse":w[8]=ye(w[8],t);break;case"roundRect":w[5]=ye(w[5],t);break;case"addPath":w[0].transform(t);break;case"poly":w[2]=ye(w[2],t);break;default:kt("unknown transform action",b.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(t){let e=this.instructions.length-1,s=this.instructions[e];if(!s)return t.x=0,t.y=0,t;for(;s.action==="closePath";){if(e--,e<0)return t.x=0,t.y=0,t;s=this.instructions[e]}switch(s.action){case"moveTo":case"lineTo":t.x=s.data[0],t.y=s.data[1];break;case"quadraticCurveTo":t.x=s.data[2],t.y=s.data[3];break;case"bezierCurveTo":t.x=s.data[4],t.y=s.data[5];break;case"arc":case"arcToSvg":t.x=s.data[5],t.y=s.data[6];break;case"addPath":s.data[0].getLastPoint(t);break}return t}}function ye(i,t){return i?i.prepend(t):t.clone()}function gt(i,t,e){const s=i.getAttribute(t);return s?Number(s):e}function Jh(i,t){const e=i.querySelectorAll("defs");for(let s=0;s<e.length;s++){const n=e[s];for(let r=0;r<n.children.length;r++){const o=n.children[r];switch(o.nodeName.toLowerCase()){case"lineargradient":t.defs[o.id]=tl(o);break;case"radialgradient":t.defs[o.id]=el();break}}}}function tl(i){const t=gt(i,"x1",0),e=gt(i,"y1",0),s=gt(i,"x2",1),n=gt(i,"y2",0),r=i.getAttribute("gradientUnits")||"objectBoundingBox",o=new ie(t,e,s,n,r==="objectBoundingBox"?"local":"global");for(let a=0;a<i.children.length;a++){const h=i.children[a],c=gt(h,"offset",0),l=Mt.shared.setValue(h.getAttribute("stop-color")).toNumber();o.addColorStop(c,l)}return o}function el(i){return kt("[SVG Parser] Radial gradients are not yet supported"),new ie(0,0,1,0)}function sn(i){const t=i.match(/url\s*\(\s*['"]?\s*#([^'"\s)]+)\s*['"]?\s*\)/i);return t?t[1]:""}const nn={fill:{type:"paint",default:0},"fill-opacity":{type:"number",default:1},stroke:{type:"paint",default:0},"stroke-width":{type:"number",default:1},"stroke-opacity":{type:"number",default:1},"stroke-linecap":{type:"string",default:"butt"},"stroke-linejoin":{type:"string",default:"miter"},"stroke-miterlimit":{type:"number",default:10},"stroke-dasharray":{type:"string",default:"none"},"stroke-dashoffset":{type:"number",default:0},opacity:{type:"number",default:1}};function Mr(i,t){const e=i.getAttribute("style"),s={},n={},r={strokeStyle:s,fillStyle:n,useFill:!1,useStroke:!1};for(const o in nn){const a=i.getAttribute(o);a&&rn(t,r,o,a.trim())}if(e){const o=e.split(";");for(let a=0;a<o.length;a++){const h=o[a].trim(),[c,l]=h.split(":");nn[c]&&rn(t,r,c,l.trim())}}return{strokeStyle:r.useStroke?s:null,fillStyle:r.useFill?n:null,useFill:r.useFill,useStroke:r.useStroke}}function rn(i,t,e,s){switch(e){case"stroke":if(s!=="none"){if(s.startsWith("url(")){const n=sn(s);t.strokeStyle.fill=i.defs[n]}else t.strokeStyle.color=Mt.shared.setValue(s).toNumber();t.useStroke=!0}break;case"stroke-width":t.strokeStyle.width=Number(s);break;case"fill":if(s!=="none"){if(s.startsWith("url(")){const n=sn(s);t.fillStyle.fill=i.defs[n]}else t.fillStyle.color=Mt.shared.setValue(s).toNumber();t.useFill=!0}break;case"fill-opacity":t.fillStyle.alpha=Number(s);break;case"stroke-opacity":t.strokeStyle.alpha=Number(s);break;case"opacity":t.fillStyle.alpha=Number(s),t.strokeStyle.alpha=Number(s);break}}function sl(i,t){if(typeof i=="string"){const o=document.createElement("div");o.innerHTML=i.trim(),i=o.querySelector("svg")}const e={context:t,defs:{},path:new pe};Jh(i,e);const s=i.children,{fillStyle:n,strokeStyle:r}=Mr(i,e);for(let o=0;o<s.length;o++){const a=s[o];a.nodeName.toLowerCase()!=="defs"&&vr(a,e,n,r)}return t}function vr(i,t,e,s){const n=i.children,{fillStyle:r,strokeStyle:o}=Mr(i,t);r&&e?e={...e,...r}:r&&(e=r),o&&s?s={...s,...o}:o&&(s=o);const a=!e&&!s;a&&(e={color:0});let h,c,l,u,f,d,x,_,g,b,w,S,M,I,E,P,O;switch(i.nodeName.toLowerCase()){case"path":I=i.getAttribute("d"),i.getAttribute("fill-rule")==="evenodd"&&kt("SVG Evenodd fill rule not supported, your svg may render incorrectly"),E=new pe(I,!0),t.context.path(E),e&&t.context.fill(e),s&&t.context.stroke(s);break;case"circle":x=gt(i,"cx",0),_=gt(i,"cy",0),g=gt(i,"r",0),t.context.ellipse(x,_,g,g),e&&t.context.fill(e),s&&t.context.stroke(s);break;case"rect":h=gt(i,"x",0),c=gt(i,"y",0),P=gt(i,"width",0),O=gt(i,"height",0),b=gt(i,"rx",0),w=gt(i,"ry",0),b||w?t.context.roundRect(h,c,P,O,b||w):t.context.rect(h,c,P,O),e&&t.context.fill(e),s&&t.context.stroke(s);break;case"ellipse":x=gt(i,"cx",0),_=gt(i,"cy",0),b=gt(i,"rx",0),w=gt(i,"ry",0),t.context.beginPath(),t.context.ellipse(x,_,b,w),e&&t.context.fill(e),s&&t.context.stroke(s);break;case"line":l=gt(i,"x1",0),u=gt(i,"y1",0),f=gt(i,"x2",0),d=gt(i,"y2",0),t.context.beginPath(),t.context.moveTo(l,u),t.context.lineTo(f,d),s&&t.context.stroke(s);break;case"polygon":M=i.getAttribute("points"),S=M.match(/\d+/g).map(W=>parseInt(W,10)),t.context.poly(S,!0),e&&t.context.fill(e),s&&t.context.stroke(s);break;case"polyline":M=i.getAttribute("points"),S=M.match(/\d+/g).map(W=>parseInt(W,10)),t.context.poly(S,!1),s&&t.context.stroke(s);break;case"g":case"svg":break;default:{kt(`[SVG parser] <${i.nodeName}> elements unsupported`);break}}a&&(e=null);for(let W=0;W<n.length;W++)vr(n[W],t,e,s)}function il(i){return Mt.isColorLike(i)}function on(i){return i instanceof Zs}function an(i){return i instanceof ie}function nl(i){return i instanceof ht}function rl(i,t,e){const s=Mt.shared.setValue(t??0);return i.color=s.toNumber(),i.alpha=s.alpha===1?e.alpha:s.alpha,i.texture=ht.WHITE,{...e,...i}}function ol(i,t,e){return i.texture=t,{...e,...i}}function hn(i,t,e){return i.fill=t,i.color=16777215,i.texture=t.texture,i.matrix=t.transform,{...e,...i}}function ln(i,t,e){return t.buildGradient(),i.fill=t,i.color=16777215,i.texture=t.texture,i.matrix=t.transform,i.textureSpace=t.textureSpace,{...e,...i}}function al(i,t){const e={...t,...i},s=Mt.shared.setValue(e.color);return e.alpha*=s.alpha,e.color=s.toNumber(),e}function se(i,t){if(i==null)return null;const e={},s=i;return il(i)?rl(e,i,t):nl(i)?ol(e,i,t):on(i)?hn(e,i,t):an(i)?ln(e,i,t):s.fill&&on(s.fill)?hn(s,s.fill,t):s.fill&&an(s.fill)?ln(s,s.fill,t):al(s,t)}function Xe(i,t){const{width:e,alignment:s,miterLimit:n,cap:r,join:o,pixelLine:a,...h}=t,c=se(i,h);return c?{width:e,alignment:s,miterLimit:n,cap:r,join:o,pixelLine:a,...c}:null}const hl=new ct,cn=new it,si=class zt extends Nt{constructor(){super(...arguments),this.uid=_t("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new pe,this._transform=new it,this._fillStyle={...zt.defaultFillStyle},this._strokeStyle={...zt.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new $t,this._boundsDirty=!0}clone(){const t=new zt;return t.batchMode=this.batchMode,t.instructions=this.instructions.slice(),t._activePath=this._activePath.clone(),t._transform=this._transform.clone(),t._fillStyle={...this._fillStyle},t._strokeStyle={...this._strokeStyle},t._stateStack=this._stateStack.slice(),t._bounds=this._bounds.clone(),t._boundsDirty=!0,t}get fillStyle(){return this._fillStyle}set fillStyle(t){this._fillStyle=se(t,zt.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(t){this._strokeStyle=Xe(t,zt.defaultStrokeStyle)}setFillStyle(t){return this._fillStyle=se(t,zt.defaultFillStyle),this}setStrokeStyle(t){return this._strokeStyle=se(t,zt.defaultStrokeStyle),this}texture(t,e,s,n,r,o){return this.instructions.push({action:"texture",data:{image:t,dx:s||0,dy:n||0,dw:r||t.frame.width,dh:o||t.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:e?Mt.shared.setValue(e).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new pe,this}fill(t,e){let s;const n=this.instructions[this.instructions.length-1];return this._tick===0&&n&&n.action==="stroke"?s=n.data.path:s=this._activePath.clone(),s?(t!=null&&(e!==void 0&&typeof t=="number"&&(at(mt,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),t={color:t,alpha:e}),this._fillStyle=se(t,zt.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:s}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:t,y:e}=this._activePath.getLastPoint(ct.shared);this._activePath.clear(),this._activePath.moveTo(t,e)}stroke(t){let e;const s=this.instructions[this.instructions.length-1];return this._tick===0&&s&&s.action==="fill"?e=s.data.path:e=this._activePath.clone(),e?(t!=null&&(this._strokeStyle=Xe(t,zt.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:e}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let t=0;t<2;t++){const e=this.instructions[this.instructions.length-1-t],s=this._activePath.clone();if(e&&(e.action==="stroke"||e.action==="fill"))if(e.data.hole)e.data.hole.addPath(s);else{e.data.hole=s;break}}return this._initNextPathLocation(),this}arc(t,e,s,n,r,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*t+a.c*e+a.tx,a.b*t+a.d*e+a.ty,s,n,r,o),this}arcTo(t,e,s,n,r){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*s+o.c*n+o.tx,o.b*s+o.d*n+o.ty,r),this}arcToSvg(t,e,s,n,r,o,a){this._tick++;const h=this._transform;return this._activePath.arcToSvg(t,e,s,n,r,h.a*o+h.c*a+h.tx,h.b*o+h.d*a+h.ty),this}bezierCurveTo(t,e,s,n,r,o,a){this._tick++;const h=this._transform;return this._activePath.bezierCurveTo(h.a*t+h.c*e+h.tx,h.b*t+h.d*e+h.ty,h.a*s+h.c*n+h.tx,h.b*s+h.d*n+h.ty,h.a*r+h.c*o+h.tx,h.b*r+h.d*o+h.ty,a),this}closePath(){var t;return this._tick++,(t=this._activePath)==null||t.closePath(),this}ellipse(t,e,s,n){return this._tick++,this._activePath.ellipse(t,e,s,n,this._transform.clone()),this}circle(t,e,s){return this._tick++,this._activePath.circle(t,e,s,this._transform.clone()),this}path(t){return this._tick++,this._activePath.addPath(t,this._transform.clone()),this}lineTo(t,e){this._tick++;const s=this._transform;return this._activePath.lineTo(s.a*t+s.c*e+s.tx,s.b*t+s.d*e+s.ty),this}moveTo(t,e){this._tick++;const s=this._transform,n=this._activePath.instructions,r=s.a*t+s.c*e+s.tx,o=s.b*t+s.d*e+s.ty;return n.length===1&&n[0].action==="moveTo"?(n[0].data[0]=r,n[0].data[1]=o,this):(this._activePath.moveTo(r,o),this)}quadraticCurveTo(t,e,s,n,r){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*s+o.c*n+o.tx,o.b*s+o.d*n+o.ty,r),this}rect(t,e,s,n){return this._tick++,this._activePath.rect(t,e,s,n,this._transform.clone()),this}roundRect(t,e,s,n,r){return this._tick++,this._activePath.roundRect(t,e,s,n,r,this._transform.clone()),this}poly(t,e){return this._tick++,this._activePath.poly(t,e,this._transform.clone()),this}regularPoly(t,e,s,n,r=0,o){return this._tick++,this._activePath.regularPoly(t,e,s,n,r,o),this}roundPoly(t,e,s,n,r,o){return this._tick++,this._activePath.roundPoly(t,e,s,n,r,o),this}roundShape(t,e,s,n){return this._tick++,this._activePath.roundShape(t,e,s,n),this}filletRect(t,e,s,n,r){return this._tick++,this._activePath.filletRect(t,e,s,n,r),this}chamferRect(t,e,s,n,r,o){return this._tick++,this._activePath.chamferRect(t,e,s,n,r,o),this}star(t,e,s,n,r=0,o=0){return this._tick++,this._activePath.star(t,e,s,n,r,o,this._transform.clone()),this}svg(t){return this._tick++,sl(t,this),this}restore(){const t=this._stateStack.pop();return t&&(this._transform=t.transform,this._fillStyle=t.fillStyle,this._strokeStyle=t.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(t){return this._transform.rotate(t),this}scale(t,e=t){return this._transform.scale(t,e),this}setTransform(t,e,s,n,r,o){return t instanceof it?(this._transform.set(t.a,t.b,t.c,t.d,t.tx,t.ty),this):(this._transform.set(t,e,s,n,r,o),this)}transform(t,e,s,n,r,o){return t instanceof it?(this._transform.append(t),this):(cn.set(t,e,s,n,r,o),this._transform.append(cn),this)}translate(t,e=t){return this._transform.translate(t,e),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;const t=this._bounds;t.clear();for(let e=0;e<this.instructions.length;e++){const s=this.instructions[e],n=s.action;if(n==="fill"){const r=s.data;t.addBounds(r.path.bounds)}else if(n==="texture"){const r=s.data;t.addFrame(r.dx,r.dy,r.dx+r.dw,r.dy+r.dh,r.transform)}if(n==="stroke"){const r=s.data,o=r.style.alignment,a=r.style.width*(1-o),h=r.path.bounds;t.addFrame(h.minX-a,h.minY-a,h.maxX+a,h.maxY+a)}}return t}containsPoint(t){var n;if(!this.bounds.containsPoint(t.x,t.y))return!1;const e=this.instructions;let s=!1;for(let r=0;r<e.length;r++){const o=e[r],a=o.data,h=a.path;if(!o.action||!h)continue;const c=a.style,l=h.shapePath.shapePrimitives;for(let u=0;u<l.length;u++){const f=l[u].shape;if(!c||!f)continue;const d=l[u].transform,x=d?d.applyInverse(t,hl):t;if(o.action==="fill")s=f.contains(x.x,x.y);else{const g=c;s=f.strokeContains(x.x,x.y,g.width,g.alignment)}const _=a.hole;if(_){const g=(n=_.shapePath)==null?void 0:n.shapePrimitives;if(g)for(let b=0;b<g.length;b++)g[b].shape.contains(x.x,x.y)&&(s=!1)}if(s)return!0}}return s}destroy(t=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof t=="boolean"?t:t==null?void 0:t.texture){const s=typeof t=="boolean"?t:t==null?void 0:t.textureSource;this._fillStyle.texture&&this._fillStyle.texture.destroy(s),this._strokeStyle.texture&&this._strokeStyle.texture.destroy(s)}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};si.defaultFillStyle={color:16777215,alpha:1,texture:ht.WHITE,matrix:null,fill:null,textureSpace:"local"};si.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:ht.WHITE,matrix:null,fill:null,textureSpace:"local",pixelLine:!1};let Dt=si;const un=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function ll(i){const t=[];let e=0;for(let s=0;s<un.length;s++){const n=`_${un[s]}`;t[e++]=i[n]}return e=Tr(i._fill,t,e),e=cl(i._stroke,t,e),e=ul(i.dropShadow,t,e),t.join("-")}function Tr(i,t,e){var s;return i&&(t[e++]=i.color,t[e++]=i.alpha,t[e++]=(s=i.fill)==null?void 0:s.styleKey),e}function cl(i,t,e){return i&&(e=Tr(i,t,e),t[e++]=i.width,t[e++]=i.alignment,t[e++]=i.cap,t[e++]=i.join,t[e++]=i.miterLimit),e}function ul(i,t,e){return i&&(t[e++]=i.alpha,t[e++]=i.angle,t[e++]=i.blur,t[e++]=i.distance,t[e++]=Mt.shared.setValue(i.color).toNumber()),e}const ii=class de extends Nt{constructor(t={}){super(),dl(t);const e={...de.defaultTextStyle,...t};for(const s in e){const n=s;this[n]=e[s]}this.update()}get align(){return this._align}set align(t){this._align=t,this.update()}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords=t,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(t){t!==null&&typeof t=="object"?this._dropShadow=this._createProxy({...de.defaultDropShadow,...t}):this._dropShadow=t?this._createProxy({...de.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(t){this._fontFamily=t,this.update()}get fontSize(){return this._fontSize}set fontSize(t){typeof t=="string"?this._fontSize=parseInt(t,10):this._fontSize=t,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle=t.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant=t,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight=t,this.update()}get leading(){return this._leading}set leading(t){this._leading=t,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing=t,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight=t,this.update()}get padding(){return this._padding}set padding(t){this._padding=t,this.update()}get trim(){return this._trim}set trim(t){this._trim=t,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline=t,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace=t,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap=t,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth=t,this.update()}get fill(){return this._originalFill}set fill(t){t!==this._originalFill&&(this._originalFill=t,this._isFillStyle(t)&&(this._originalFill=this._createProxy({...Dt.defaultFillStyle,...t},()=>{this._fill=se({...this._originalFill},Dt.defaultFillStyle)})),this._fill=se(t===0?"black":t,Dt.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(t){t!==this._originalStroke&&(this._originalStroke=t,this._isFillStyle(t)&&(this._originalStroke=this._createProxy({...Dt.defaultStrokeStyle,...t},()=>{this._stroke=Xe({...this._originalStroke},Dt.defaultStrokeStyle)})),this._stroke=Xe(t,Dt.defaultStrokeStyle),this.update())}_generateKey(){return this._styleKey=ll(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this)}reset(){const t=de.defaultTextStyle;for(const e in t)this[e]=t[e]}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new de({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth})}destroy(t=!1){var s,n,r,o;if(this.removeAllListeners(),typeof t=="boolean"?t:t==null?void 0:t.texture){const a=typeof t=="boolean"?t:t==null?void 0:t.textureSource;(s=this._fill)!=null&&s.texture&&this._fill.texture.destroy(a),(n=this._originalFill)!=null&&n.texture&&this._originalFill.texture.destroy(a),(r=this._stroke)!=null&&r.texture&&this._stroke.texture.destroy(a),(o=this._originalStroke)!=null&&o.texture&&this._originalStroke.texture.destroy(a)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(t,e){return new Proxy(t,{set:(s,n,r)=>(s[n]=r,e==null||e(n,r),this.update(),!0)})}_isFillStyle(t){return(t??null)!==null&&!(Mt.isColorLike(t)||t instanceof ie||t instanceof Zs)}};ii.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};ii.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let je=ii;function dl(i){const t=i;if(typeof t.dropShadow=="boolean"&&t.dropShadow){const e=je.defaultDropShadow;i.dropShadow={alpha:t.dropShadowAlpha??e.alpha,angle:t.dropShadowAngle??e.angle,blur:t.dropShadowBlur??e.blur,color:t.dropShadowColor??e.color,distance:t.dropShadowDistance??e.distance}}if(t.strokeThickness!==void 0){at(mt,"strokeThickness is now a part of stroke");const e=t.stroke;let s={};if(Mt.isColorLike(e))s.color=e;else if(e instanceof ie||e instanceof Zs)s.fill=e;else if(Object.hasOwnProperty.call(e,"color")||Object.hasOwnProperty.call(e,"fill"))s=e;else throw new Error("Invalid stroke value.");i.stroke={...s,width:t.strokeThickness}}if(Array.isArray(t.fillGradientStops)){at(mt,"gradient fill is now a fill pattern: `new FillGradient(...)`");let e;i.fontSize==null?i.fontSize=je.defaultTextStyle.fontSize:typeof i.fontSize=="string"?e=parseInt(i.fontSize,10):e=i.fontSize;const s=new ie({start:{x:0,y:0},end:{x:0,y:(e||0)*1.7}}),n=t.fillGradientStops.map(r=>Mt.shared.setValue(r).toNumber());n.forEach((r,o)=>{const a=o/(n.length-1);s.addColorStop(a,r)}),i.fill={fill:s}}}const fl=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function pl(i){const t=typeof i.fontSize=="number"?`${i.fontSize}px`:i.fontSize;let e=i.fontFamily;Array.isArray(i.fontFamily)||(e=i.fontFamily.split(","));for(let s=e.length-1;s>=0;s--){let n=e[s].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&!fl.includes(n)&&(n=`"${n}"`),e[s]=n}return`${i.fontStyle} ${i.fontVariant} ${i.fontWeight} ${t} ${e.join(",")}`}const Ms={willReadFrequently:!0},Ut=class N{static get experimentalLetterSpacingSupported(){let t=N._experimentalLetterSpacingSupported;if(t!==void 0){const e=Ht.get().getCanvasRenderingContext2D().prototype;t=N._experimentalLetterSpacingSupported="letterSpacing"in e||"textLetterSpacing"in e}return t}constructor(t,e,s,n,r,o,a,h,c){this.text=t,this.style=e,this.width=s,this.height=n,this.lines=r,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=h,this.fontProperties=c}static measureText(t=" ",e,s=N._canvas,n=e.wordWrap){var w;const r=`${t}:${e.styleKey}`;if(N._measurementCache[r])return N._measurementCache[r];const o=pl(e),a=N.measureFont(o);a.fontSize===0&&(a.fontSize=e.fontSize,a.ascent=e.fontSize);const h=N.__context;h.font=o;const l=(n?N._wordWrap(t,e,s):t).split(/(?:\r\n|\r|\n)/),u=new Array(l.length);let f=0;for(let S=0;S<l.length;S++){const M=N._measureText(l[S],e.letterSpacing,h);u[S]=M,f=Math.max(f,M)}const d=((w=e._stroke)==null?void 0:w.width)||0;let x=f+d;e.dropShadow&&(x+=e.dropShadow.distance);const _=e.lineHeight||a.fontSize;let g=Math.max(_,a.fontSize+d)+(l.length-1)*(_+e.leading);return e.dropShadow&&(g+=e.dropShadow.distance),new N(t,e,x,g,l,u,_+e.leading,f,a)}static _measureText(t,e,s){let n=!1;N.experimentalLetterSpacingSupported&&(N.experimentalLetterSpacing?(s.letterSpacing=`${e}px`,s.textLetterSpacing=`${e}px`,n=!0):(s.letterSpacing="0px",s.textLetterSpacing="0px"));const r=s.measureText(t);let o=r.width;const a=-r.actualBoundingBoxLeft;let c=r.actualBoundingBoxRight-a;if(o>0)if(n)o-=e,c-=e;else{const l=(N.graphemeSegmenter(t).length-1)*e;o+=l,c+=l}return Math.max(o,c)}static _wordWrap(t,e,s=N._canvas){const n=s.getContext("2d",Ms);let r=0,o="",a="";const h=Object.create(null),{letterSpacing:c,whiteSpace:l}=e,u=N._collapseSpaces(l),f=N._collapseNewlines(l);let d=!u;const x=e.wordWrapWidth+c,_=N._tokenize(t);for(let g=0;g<_.length;g++){let b=_[g];if(N._isNewline(b)){if(!f){a+=N._addLine(o),d=!u,o="",r=0;continue}b=" "}if(u){const S=N.isBreakingSpace(b),M=N.isBreakingSpace(o[o.length-1]);if(S&&M)continue}const w=N._getFromCache(b,c,h,n);if(w>x)if(o!==""&&(a+=N._addLine(o),o="",r=0),N.canBreakWords(b,e.breakWords)){const S=N.wordWrapSplit(b);for(let M=0;M<S.length;M++){let I=S[M],E=I,P=1;for(;S[M+P];){const W=S[M+P];if(!N.canBreakChars(E,W,b,M,e.breakWords))I+=W;else break;E=W,P++}M+=P-1;const O=N._getFromCache(I,c,h,n);O+r>x&&(a+=N._addLine(o),d=!1,o="",r=0),o+=I,r+=O}}else{o.length>0&&(a+=N._addLine(o),o="",r=0);const S=g===_.length-1;a+=N._addLine(b,!S),d=!1,o="",r=0}else w+r>x&&(d=!1,a+=N._addLine(o),o="",r=0),(o.length>0||!N.isBreakingSpace(b)||d)&&(o+=b,r+=w)}return a+=N._addLine(o,!1),a}static _addLine(t,e=!0){return t=N._trimRight(t),t=e?`${t}
`:t,t}static _getFromCache(t,e,s,n){let r=s[t];return typeof r!="number"&&(r=N._measureText(t,e,n)+e,s[t]=r),r}static _collapseSpaces(t){return t==="normal"||t==="pre-line"}static _collapseNewlines(t){return t==="normal"}static _trimRight(t){if(typeof t!="string")return"";for(let e=t.length-1;e>=0;e--){const s=t[e];if(!N.isBreakingSpace(s))break;t=t.slice(0,-1)}return t}static _isNewline(t){return typeof t!="string"?!1:N._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:N._breakingSpaces.includes(t.charCodeAt(0))}static _tokenize(t){const e=[];let s="";if(typeof t!="string")return e;for(let n=0;n<t.length;n++){const r=t[n],o=t[n+1];if(N.isBreakingSpace(r,o)||N._isNewline(r)){s!==""&&(e.push(s),s=""),e.push(r);continue}s+=r}return s!==""&&e.push(s),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,s,n,r){return!0}static wordWrapSplit(t){return N.graphemeSegmenter(t)}static measureFont(t){if(N._fonts[t])return N._fonts[t];const e=N._context;e.font=t;const s=e.measureText(N.METRICS_STRING+N.BASELINE_SYMBOL),n={ascent:s.actualBoundingBoxAscent,descent:s.actualBoundingBoxDescent,fontSize:s.actualBoundingBoxAscent+s.actualBoundingBoxDescent};return N._fonts[t]=n,n}static clearMetrics(t=""){t?delete N._fonts[t]:N._fonts={}}static get _canvas(){if(!N.__canvas){let t;try{const e=new OffscreenCanvas(0,0),s=e.getContext("2d",Ms);if(s!=null&&s.measureText)return N.__canvas=e,e;t=Ht.get().createCanvas()}catch{t=Ht.get().createCanvas()}t.width=t.height=10,N.__canvas=t}return N.__canvas}static get _context(){return N.__context||(N.__context=N._canvas.getContext("2d",Ms)),N.__context}};Ut.METRICS_STRING="|ÉqÅ";Ut.BASELINE_SYMBOL="M";Ut.BASELINE_MULTIPLIER=1.4;Ut.HEIGHT_MULTIPLIER=2;Ut.graphemeSegmenter=(()=>{if(typeof(Intl==null?void 0:Intl.Segmenter)=="function"){const i=new Intl.Segmenter;return t=>[...i.segment(t)].map(e=>e.segment)}return i=>[...i]})();Ut.experimentalLetterSpacing=!1;Ut._fonts={};Ut._newlines=[10,13];Ut._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];Ut._measurementCache={};let gl=Ut;class yt extends Ys{constructor(t){t instanceof Dt&&(t={context:t});const{context:e,roundPixels:s,...n}=t||{};super({label:"Graphics",...n}),this.renderPipeId="graphics",e?this._context=e:this._context=this._ownedContext=new Dt,this._context.on("update",this.onViewUpdate,this),this.allowChildren=!1,this.roundPixels=s??!1}set context(t){t!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=t,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(t){return this._context.containsPoint(t)}destroy(t){this._ownedContext&&!t?this._ownedContext.destroy(t):(t===!0||(t==null?void 0:t.context)===!0)&&this._context.destroy(t),this._ownedContext=null,this._context=null,super.destroy(t)}_callContextMethod(t,e){return this.context[t](...e),this}setFillStyle(...t){return this._callContextMethod("setFillStyle",t)}setStrokeStyle(...t){return this._callContextMethod("setStrokeStyle",t)}fill(...t){return this._callContextMethod("fill",t)}stroke(...t){return this._callContextMethod("stroke",t)}texture(...t){return this._callContextMethod("texture",t)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...t){return this._callContextMethod("arc",t)}arcTo(...t){return this._callContextMethod("arcTo",t)}arcToSvg(...t){return this._callContextMethod("arcToSvg",t)}bezierCurveTo(...t){return this._callContextMethod("bezierCurveTo",t)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...t){return this._callContextMethod("ellipse",t)}circle(...t){return this._callContextMethod("circle",t)}path(...t){return this._callContextMethod("path",t)}lineTo(...t){return this._callContextMethod("lineTo",t)}moveTo(...t){return this._callContextMethod("moveTo",t)}quadraticCurveTo(...t){return this._callContextMethod("quadraticCurveTo",t)}rect(...t){return this._callContextMethod("rect",t)}roundRect(...t){return this._callContextMethod("roundRect",t)}poly(...t){return this._callContextMethod("poly",t)}regularPoly(...t){return this._callContextMethod("regularPoly",t)}roundPoly(...t){return this._callContextMethod("roundPoly",t)}roundShape(...t){return this._callContextMethod("roundShape",t)}filletRect(...t){return this._callContextMethod("filletRect",t)}chamferRect(...t){return this._callContextMethod("chamferRect",t)}star(...t){return this._callContextMethod("star",t)}svg(...t){return this._callContextMethod("svg",t)}restore(...t){return this._callContextMethod("restore",t)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...t){return this._callContextMethod("rotate",t)}scaleTransform(...t){return this._callContextMethod("scale",t)}setTransform(...t){return this._callContextMethod("setTransform",t)}transform(...t){return this._callContextMethod("transform",t)}translateTransform(...t){return this._callContextMethod("translate",t)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(t){this._context.fillStyle=t}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(t){this._context.strokeStyle=t}clone(t=!1){return t?new yt(this._context.clone()):(this._ownedContext=null,new yt(this._context))}lineStyle(t,e,s){at(mt,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const n={};return t&&(n.width=t),e&&(n.color=e),s&&(n.alpha=s),this.context.strokeStyle=n,this}beginFill(t,e){at(mt,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const s={};return t!==void 0&&(s.color=t),e!==void 0&&(s.alpha=e),this.context.fillStyle=s,this}endFill(){at(mt,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const t=this.context.strokeStyle;return(t.width!==Dt.defaultStrokeStyle.width||t.color!==Dt.defaultStrokeStyle.color||t.alpha!==Dt.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...t){return at(mt,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",t)}drawEllipse(...t){return at(mt,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",t)}drawPolygon(...t){return at(mt,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",t)}drawRect(...t){return at(mt,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",t)}drawRoundedRect(...t){return at(mt,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",t)}drawStar(...t){return at(mt,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",t)}}class ml extends Ys{constructor(t,e){const{text:s,resolution:n,style:r,anchor:o,width:a,height:h,roundPixels:c,...l}=t;super({...l}),this.batched=!0,this._resolution=null,this._autoResolution=!0,this._didTextUpdate=!0,this._styleClass=e,this.text=s??"",this.style=r,this.resolution=n??null,this.allowChildren=!1,this._anchor=new Tt({_onUpdate:()=>{this.onViewUpdate()}}),o&&(this.anchor=o),this.roundPixels=c??!1,a!==void 0&&(this.width=a),h!==void 0&&(this.height=h)}get anchor(){return this._anchor}set anchor(t){typeof t=="number"?this._anchor.set(t):this._anchor.copyFrom(t)}set text(t){t=t.toString(),this._text!==t&&(this._text=t,this.onViewUpdate())}get text(){return this._text}set resolution(t){this._autoResolution=t===null,this._resolution=t,this.onViewUpdate()}get resolution(){return this._resolution}get style(){return this._style}set style(t){var e;t||(t={}),(e=this._style)==null||e.off("update",this.onViewUpdate,this),t instanceof this._styleClass?this._style=t:this._style=new this._styleClass(t),this._style.on("update",this.onViewUpdate,this),this.onViewUpdate()}get width(){return Math.abs(this.scale.x)*this.bounds.width}set width(t){this._setWidth(t,this.bounds.width)}get height(){return Math.abs(this.scale.y)*this.bounds.height}set height(t){this._setHeight(t,this.bounds.height)}getSize(t){return t||(t={}),t.width=Math.abs(this.scale.x)*this.bounds.width,t.height=Math.abs(this.scale.y)*this.bounds.height,t}setSize(t,e){typeof t=="object"?(e=t.height??t.width,t=t.width):e??(e=t),t!==void 0&&this._setWidth(t,this.bounds.width),e!==void 0&&this._setHeight(e,this.bounds.height)}containsPoint(t){const e=this.bounds.width,s=this.bounds.height,n=-e*this.anchor.x;let r=0;return t.x>=n&&t.x<=n+e&&(r=-s*this.anchor.y,t.y>=r&&t.y<=r+s)}onViewUpdate(){this.didViewUpdate||(this._didTextUpdate=!0),super.onViewUpdate()}_getKey(){return`${this.text}:${this._style.styleKey}:${this._resolution}`}destroy(t=!1){super.destroy(t),this.owner=null,this._bounds=null,this._anchor=null,(typeof t=="boolean"?t:t!=null&&t.style)&&this._style.destroy(t),this._style=null,this._text=null}}function xl(i,t){let e=i[0]??{};return(typeof e=="string"||i[1])&&(at(mt,`use new ${t}({ text: "hi!", style }) instead`),e={text:e,style:i[1]}),e}class wt extends ml{constructor(...t){const e=xl(t,"Text");super(e,je),this.renderPipeId="text"}updateBounds(){const t=this._bounds,e=this._anchor,s=gl.measureText(this._text,this._style),{width:n,height:r}=s;t.minX=-e._x*n,t.maxX=t.minX+n,t.minY=-e._y*r,t.maxY=t.minY+r}}Ft.add(Gr,Dr);var R=(i=>(i.EMPTY="empty",i.FIRE="fire",i.WATER="water",i.NATURE="nature",i))(R||{});const Ee={empty:13488856,fire:16728374,water:29913,nature:3066944},yl=[{q:1,r:0,s:-1},{q:1,r:-1,s:0},{q:0,r:-1,s:1},{q:-1,r:0,s:1},{q:-1,r:1,s:0},{q:0,r:1,s:-1}];class _l{constructor(t,e,s="pointy",n){Y(this,"app");Y(this,"container");Y(this,"_hexSize");Y(this,"orientation");Y(this,"gridCenter");Y(this,"iconTextures");Y(this,"gridRadius",0);Y(this,"highlightContainer");Y(this,"hexGraphics",new Map);Y(this,"hoverHighlight",null);Y(this,"clickHighlight",null);this.app=t,this._hexSize=e,this.orientation=s,this.iconTextures=n,this.container=new pt,this.highlightContainer=new pt,this.highlightContainer.alpha=.4,this.app.stage.addChild(this.container),this.app.stage.addChild(this.highlightContainer),this.gridCenter=new ct(this.app.screen.width/2,this.app.screen.height/2),this.container.position.copyFrom(this.gridCenter),this.highlightContainer.position.copyFrom(this.gridCenter),this.container.eventMode="static",this.container.on("pointermove",this.onPointerMove,this),this.container.on("pointerdown",this.onPointerDown,this)}get gridContainer(){return this.container}get hexSize(){return this._hexSize}hexToPixel(t){const e=this.orientation==="pointy"?{f0:Math.sqrt(3),f1:Math.sqrt(3)/2,f2:0,f3:1.5}:{f0:1.5,f1:0,f2:Math.sqrt(3)/2,f3:Math.sqrt(3)},s=(e.f0*t.q+e.f1*t.r)*this._hexSize,n=(e.f2*t.q+e.f3*t.r)*this._hexSize;return new ct(s,n)}drawHex(t,e,s=1,n=!0){const r=this.hexToPixel(t),o=`${t.q},${t.r}`,a=this.hexGraphics.get(o);if(a&&(a.destroy(),this.hexGraphics.delete(o)),e===R.EMPTY){const c=new yt;c.lineStyle(1,10066329,.3),c.beginFill(16777215,.3),c.drawPolygon(this.getHexCorners(r)),c.endFill(),this.hexGraphics.set(o,c),this.gridContainer.addChild(c);return}const h=new yt;if(h.lineStyle(1,3355443,.8),h.beginFill(Ee[e],1),h.drawPolygon(this.getHexCorners(r)),h.endFill(),this.hexGraphics.set(o,h),this.gridContainer.addChild(h),n&&this.iconTextures[e]){const c=new It(this.iconTextures[e]);c.anchor.set(.5),c.position.copyFrom(r),c.name=`icon_${t.q}_${t.r}`,this.gridContainer.addChild(c)}}getHexCorners(t){const e=[],s=(this.orientation==="pointy"?30:0)*Math.PI/180;for(let n=0;n<6;n++){const r=s+60*n*Math.PI/180;e.push(new ct(t.x+this._hexSize*Math.cos(r),t.y+this._hexSize*Math.sin(r)))}return e}drawHexagonalGrid(t){this.gridRadius=t,this.container.removeChildren();const e=[];for(let n=-t;n<=t;n++)for(let r=Math.max(-t,-n-t);r<=Math.min(t,-n+t);r++){const o=-n-r;this.isValidHex({q:n,r,s:o})&&e.push({q:n,r,s:o})}e.sort((n,r)=>{const o=(Math.abs(n.q)+Math.abs(n.r)+Math.abs(n.s))/2,a=(Math.abs(r.q)+Math.abs(r.r)+Math.abs(r.s))/2;return o-a}),(()=>{const r=[];e.forEach(o=>{const a=(Math.abs(o.q)+Math.abs(o.r)+Math.abs(o.s))/2;r[a]||(r[a]=[]),r[a].push(o)}),r.forEach((o,a)=>{setTimeout(()=>{o.sort(()=>Math.random()-.5),o.forEach((h,c)=>{const l=Math.random()*80;setTimeout(()=>{this.drawHex(h,R.EMPTY,0,!1);const u=this.getHexGraphics(h);if(!u)return;u.alpha=0,u.scale.set(.5);const f=this.hexToPixel(h),d=u.x,x=u.y,_=Math.sqrt(f.x*f.x+f.y*f.y),g=Math.atan2(f.y,f.x),b=10*(1-Math.min(1,_/(this.gridRadius*this.hexSize))),w={x:Math.cos(g)*-b,y:Math.sin(g)*-b};u.x+=w.x,u.y+=w.y;const S=performance.now(),M=600+Math.random()*200,I=()=>{if(!u||!u.parent)return;const E=performance.now()-S,P=Math.min(E/M,1),O=P<.5?4*P*P*P:1-Math.pow(-2*P+2,3)/2;u.alpha=Math.pow(O,.7);const W=1+Math.sin(P*Math.PI)*.15;u.scale.set(.5+.5*O*W),u.x=d+w.x*(1-O)*(1-O),u.y=x+w.y*(1-O)*(1-O),P<1&&requestAnimationFrame(I)};requestAnimationFrame(I)},l)})},a*50*(1+a*.1))})})()}isValidHex(t){const{q:e,r:s,s:n}=t;return Math.abs(e)<=this.gridRadius&&Math.abs(s)<=this.gridRadius&&Math.abs(n)<=this.gridRadius}getNeighbors(t){const e=[];return yl.forEach(s=>{const n={q:t.q+s.q,r:t.r+s.r,s:t.s+s.s};this.isValidHex(n)&&e.push(n)}),e}pixelToFractionalHex(t){const e=this.container.toLocal(t),s=this.orientation==="pointy"?{b0:Math.sqrt(3)/3,b1:-1/3,b2:0,b3:2/3}:{b0:2/3,b1:0,b2:-1/3,b3:Math.sqrt(3)/3},n=(s.b0*e.x+s.b1*e.y)/this._hexSize,r=(s.b2*e.x+s.b3*e.y)/this._hexSize;return{q:n,r,s:-n-r}}hexRound(t){let e=Math.round(t.q),s=Math.round(t.r),n=Math.round(t.s);const r=Math.abs(e-t.q),o=Math.abs(s-t.r),a=Math.abs(n-t.s);return r>o&&r>a?e=-s-n:o>a?s=-e-n:n=-e-s,{q:e,r:s,s:n}}pixelToHex(t){const e=this.pixelToFractionalHex(t);return this.hexRound(e)}getHexGraphics(t){const e=`hex_${t.q}_${t.r}`;return this.container.getChildByName(e)}recenter(t,e){this.gridCenter.set(t,e),this.container.position.copyFrom(this.gridCenter),this.highlightContainer.position.copyFrom(this.gridCenter),console.log(`Grid recentered to ${t}, ${e}`)}clearHighlights(){this.highlightContainer.removeChildren(),this.hoverHighlight=null,this.clickHighlight=null}updateHighlights(t,e){this.clearHighlights();const s=(n,r,o)=>{const a=this.hexToPixel(n),h=new yt;h.poly(this.getHexCorners(a));const c=o?.7:.6;h.fill({color:r,alpha:c});const l=o?3:2;h.stroke({width:l,color:r,alpha:.9}),this.highlightContainer.addChild(h);const u=performance.now(),f=()=>{if(!h.parent)return;const d=performance.now()-u,b=(o?.7:.6)+(o?.3:.2)*Math.sin(d/(o?120:200));if(h.alpha=b,o){const w=1+.05*Math.sin(d/100);h.scale.set(w)}requestAnimationFrame(f)};requestAnimationFrame(f)};t.forEach(n=>{s(n,6750054,!0)}),e.forEach(n=>{s(n,16737894,!1)})}onPointerMove(t){const e=this.container.toLocal(t.global),s=this.pixelToHex(e);this.isValidHex(s)?this.highlightHexOnHover(s):this.clearHoverHighlight()}highlightHexOnHover(t){this.clearHoverHighlight(),this.hoverHighlight=new yt;const e=this.hexToPixel(t),s=this.getHexCorners(e);this.hoverHighlight.beginFill(8947848,.3),this.hoverHighlight.drawPolygon(s),this.hoverHighlight.endFill(),this.highlightContainer.addChild(this.hoverHighlight)}clearHoverHighlight(){this.hoverHighlight&&(this.highlightContainer.removeChild(this.hoverHighlight),this.hoverHighlight.destroy(),this.hoverHighlight=null)}onPointerDown(t){const e=this.container.toLocal(t.global),s=this.pixelToHex(e);this.isValidHex(s)&&this.highlightHexOnClick(s)}highlightHexOnClick(t){this.clearClickHighlight(),this.clickHighlight=new yt;const e=this.hexToPixel(t),s=this.getHexCorners(e);this.clickHighlight.beginFill(6710886,.4),this.clickHighlight.drawPolygon(s),this.clickHighlight.endFill(),this.highlightContainer.addChild(this.clickHighlight)}clearClickHighlight(){this.clickHighlight&&(this.highlightContainer.removeChild(this.clickHighlight),this.clickHighlight.destroy(),this.clickHighlight=null)}}const Xt=30;function bl(i,t,e,s,n,r){const o=Ee[s]??16777215,a=[],h=30*Math.PI/180;for(let c=0;c<6;c++){const l=h+60*c*Math.PI/180;a.push(new ct(e.x+Xt*Math.cos(l),e.y+Xt*Math.sin(l)))}if(i.poly(a),r&&r>1){const c=wl(o,2236962);i.fill({color:c,alpha:1})}else i.fill({color:o,alpha:1});if(i.stroke({width:1,color:3355443,alpha:.8}),r&&r>1){let c=function(){const S=(performance.now()-f)/1e3%d/d,M=.4+.4*Math.sin(S*Math.PI*2),I=1+.15*Math.sin(S*Math.PI*2);u.alpha=M,u.scale.set(I),requestAnimationFrame(c)},l=function(){const S=(performance.now()-_)/1e3%g/g,M=1+.2*Math.sin(S*Math.PI*2);x.scale.set(M),requestAnimationFrame(l)};const u=new yt;u.lineStyle(3,16777215,.6),u.drawCircle(e.x,e.y,Xt*.8),u.lineStyle(2,o,.8),u.drawCircle(e.x,e.y,Xt*.7),t.addChild(u);const f=performance.now(),d=2;requestAnimationFrame(c);const x=new wt(r.toString());x.style={fontFamily:"Arial",fontSize:20,fontWeight:"bold",fill:16777215,stroke:{color:0,width:4},align:"center"},x.scale.set(1.2),x.anchor.set(.5),x.position.set(e.x,e.y+10),t.addChild(x);const _=performance.now(),g=1.5;requestAnimationFrame(l)}if(s!==R.EMPTY&&n){const c=new It(n);if(c.anchor.set(.5),c.position.copyFrom(e),r&&r>1){const l=Xt*.6/Math.max(c.width,c.height);c.scale.set(l),c.y-=10}else{const l=Xt*.8/Math.max(c.width,c.height);c.scale.set(l)}t.addChild(c)}}function wl(i,t){const e=i>>16&255,s=i>>8&255,n=i&255,r=Math.min(e+(t>>16&255),255),o=Math.min(s+(t>>8&255),255),a=Math.min(n+(t&255),255);return r<<16|o<<8|a}function dn(i){const t={f0:Math.sqrt(3),f1:Math.sqrt(3)/2,f2:0,f3:1.5},e=(t.f0*i.q+t.f1*i.r)*Xt,s=(t.f2*i.q+t.f3*i.r)*Xt;return new ct(e,s)}class Sl{constructor(t,e,s,n,r,o){Y(this,"app");Y(this,"container");Y(this,"pattern");Y(this,"graphics");Y(this,"iconSpritesContainer");Y(this,"grid");Y(this,"initialPosition",new ct);Y(this,"isDragging",!1);Y(this,"dragOffset",new ct);Y(this,"iconTextures");Y(this,"checkSingleHexPlacement",()=>!0);Y(this,"lastValidPlacementHex",null);Y(this,"lastValidTargetHex",null);Y(this,"onPlace",()=>{});this.app=t,this.grid=e,this.pattern=s,this.iconTextures=n,this.checkSingleHexPlacement=r,this.container=new pt,this.graphics=new yt,this.iconSpritesContainer=new pt,this.container.addChild(this.graphics),this.container.addChild(this.iconSpritesContainer),this.drawShape(),this.container.eventMode="static",this.container.cursor="pointer",this.container.on("pointerdown",this.onDragStart,this),this.container.on("pointerup",this.onDragEnd,this),this.container.on("pointerupoutside",this.onDragEnd,this),this.container.on("pointertap",this.onRotate,this)}onDragStart(t){this.isDragging||(this.isDragging=!0,this.grid.clearHighlights(),this.app.stage.addChild(this.container),this.dragOffset.x=0,this.dragOffset.y=0,this.container.alpha=.7,this.app.stage.eventMode="static",this.app.stage.on("pointermove",this.onDragMove,this))}onDragMove(t){var e;if(this.isDragging){const s=this.container.parent.toLocal(t.global),n=this.grid.pixelToHex(s);this.container.x=s.x,this.container.y=s.y,this.getGridHexes(n);let r=-1,o=null;this.pattern.hexes.forEach(x=>{const _=n.q+x.q,g=n.r+x.r,b=-_-g,w={q:_,r:g,s:b};if(this.grid.isValidHex(w)){const S={q:w.q-x.q,r:w.r-x.r,s:-w.q+x.q-w.r+x.r},M=this.getGridHexes(S);let I=0;M.forEach(E=>{this.checkSingleHexPlacement(E)&&I++}),I>r&&(r=I,o=S)}});const a=((e=window.gameConfig)==null?void 0:e.snapping)||{radiusMultiplier:1.5,strength:.9,showDebugLogs:!0};if(o&&r>0){const x=this.grid.hexToPixel(o),_=this.grid.gridContainer.toGlobal(x),g=this.container.parent.toLocal(_),b=Math.sqrt(Math.pow(s.x-g.x,2)+Math.pow(s.y-g.y,2)),w=this.grid.hexSize*a.radiusMultiplier;if(b<w){const S=a.strength*(1-Math.min(.8,b/w));a.showDebugLogs&&console.log(`[DEBUG] Snapping with strength ${S.toFixed(2)}, distance ${b.toFixed(2)}, threshold ${w.toFixed(2)}`),this.container.x=this.container.x+(g.x-this.container.x)*S,this.container.y=this.container.y+(g.y-this.container.y)*S,this.lastValidPlacementHex=o}}const h=o||this.grid.pixelToHex(new ct(this.container.x,this.container.y));this.lastValidTargetHex=h;const c=this.getGridHexes(h),l=[],u=[],f=[];let d=!0;if(c.forEach(x=>{this.checkSingleHexPlacement(x)?(l.push(x),this.grid.getNeighbors(x).forEach(b=>{`${b.q}${b.r}`;const w=this.pattern.hexes.find(S=>x.q-h.q===S.q&&x.r-h.r===S.r);if(w&&!this.checkSingleHexPlacement(b)&&this.grid.getHexGraphics(b)){const M=w.type;f.some(E=>E.hex.q===b.q&&E.hex.r===b.r)||f.push({hex:b,type:M})}})):(u.push(x),d=!1)}),d&&l.length>0)this.lastValidPlacementHex=h;else if(l.length===0){const x=this.findNearestValidPosition(h);if(x){this.lastValidPlacementHex=x;const _=this.getGridHexes(x);l.length=0,u.length=0,_.forEach(g=>{this.checkSingleHexPlacement(g)?l.push(g):u.push(g)})}}this.grid.updateHighlights(l,u),f.length>0&&this.animateMatchingHexes(f)}}animateMatchingHexes(t){t.forEach(e=>{const s=this.grid.getHexGraphics(e.hex);if(s){const n=Ee[e.type];performance.now();const r=`icon_${e.hex.q}_${e.hex.r}`,o=this.app.stage.getChildByName(r,!0);if(!o){this.animateMatchingPulse(s,n);return}const a=new It(this.iconTextures[e.type]);a.anchor.set(.5);const h=o.parent.toGlobal(o.position);a.position.copyFrom(h);const c=this.grid.hexSize*.4/Math.max(a.width,a.height);a.scale.set(c),this.app.stage.addChild(a);const l=this.app.stage.getChildByName("uiContainer");if(!l){a.destroy(),this.animateMatchingPulse(s,n);return}const u=l.x+l.width/2,f=l.y+40;let d;e.type===R.FIRE?(d={x:u-120,y:f},console.log(`Fixed FIRE target: ${d.x}, ${d.y}`)):e.type===R.NATURE?(d={x:u+120,y:f},console.log(`Fixed NATURE target: ${d.x}, ${d.y}`)):(d={x:u,y:f},console.log(`Fixed WATER target: ${d.x}, ${d.y}`));const x=.6,_=performance.now(),g=100;setTimeout(()=>{const b=()=>{const w=performance.now(),S=(w-_-g)/1e3;if(S<0){a.y=h.y+Math.sin(w/100)*3,requestAnimationFrame(b);return}if(S<x){let M=S/x;M=M<.5?2*M*M:1-Math.pow(-2*M+2,2)/2;const I=h.x+(d.x-h.x)*.5+40,E=h.y+(d.y-h.y)*.3-80,P=(1-M)*(1-M)*h.x+2*(1-M)*M*I+M*M*d.x,O=(1-M)*(1-M)*h.y+2*(1-M)*M*E+M*M*d.y,W=5*(1-M),$=Math.sin(M*Math.PI*6)*W,U=Math.cos(M*Math.PI*6)*W;a.position.set(P+$,O+U),a.scale.set(c*(1-.5*M)),a.rotation=M*Math.PI*2,s.tint=n,requestAnimationFrame(b)}else a.alpha=.8,setTimeout(()=>{a.alpha=.5,setTimeout(()=>{a.destroy(),s.tint=16777215,s.scale.set(1)},50)},50)};requestAnimationFrame(b)},g),this.animateMatchingPulse(s,n)}})}animateMatchingPulse(t,e){const s=performance.now(),n=()=>{if(!t.parent)return;const r=performance.now()-s,o=1+.3*Math.sin(r/120),a=(e>>16&255)*o,h=(e>>8&255)*o,c=(e&255)*o,l=Math.min(255,Math.round(a))<<16|Math.min(255,Math.round(h))<<8|Math.min(255,Math.round(c));t.tint=l;const u=1+.05*Math.sin(r/150);t.scale.set(u),this.isDragging?requestAnimationFrame(n):(t.tint=16777215,t.scale.set(1))};requestAnimationFrame(n)}onDragEnd(){if(!this.isDragging)return;if(this.isDragging=!1,this.container.alpha=1,this.app.stage.eventMode="auto",this.app.stage.off("pointermove",this.onDragMove,this),this.lastValidPlacementHex){console.log("[DEBUG] Using stored valid placement at:",this.lastValidPlacementHex);const o=this.getGridHexes(this.lastValidPlacementHex);this.grid.updateHighlights(o,[]),setTimeout(()=>this.grid.clearHighlights(),300),this.onPlace(this,this.lastValidPlacementHex),this.lastValidPlacementHex=null,this.lastValidTargetHex=null;return}const t=new ct(this.container.x,this.container.y),e=this.grid.pixelToHex(t);let s=null,n=-1;if(this.pattern.hexes.forEach(o=>{const a=e.q+o.q,h=e.r+o.r,c={q:a-o.q,r:h-o.r,s:-a+o.q-h+o.r},l=this.getGridHexes(c);let u=0;l.forEach(f=>{this.checkSingleHexPlacement(f)&&u++}),u>n&&(n=u,s=c)}),console.log(`[DEBUG] Found placement with validity score ${n}/${this.pattern.hexes.length}`),s&&n===this.pattern.hexes.length){console.log("[DEBUG] Full valid placement found at:",s);const o=this.getGridHexes(s);this.grid.updateHighlights(o,[]),setTimeout(()=>this.grid.clearHighlights(),300),this.onPlace(this,s)}else if(s&&n>this.pattern.hexes.length/2){console.log("[DEBUG] Partial valid placement found at:",s);const o=this.findNearestValidPosition(s);if(o){console.log("[DEBUG] Found nearby fully valid placement at:",o);const a=this.getGridHexes(o);this.grid.updateHighlights(a,[]),setTimeout(()=>this.grid.clearHighlights(),300),this.onPlace(this,o)}else console.log("[DEBUG] No fully valid placement nearby, using partial:",s),this.onPlace(this,s)}else console.log("[DEBUG] No valid placement found, returning to initial position"),this.container.position.copyFrom(this.initialPosition);const r=this.grid.hexGraphics;r&&Array.from(r.values()).forEach(o=>{o&&(o.tint=16777215,o.scale.set(1))})}findNearestValidPosition(t){if(this.checkPlacement(t))return t;const e=new Set;e.add(`${t.q},${t.r}`);const s=[t],n=2;for(;s.length>0;){const r=s.shift();if((Math.abs(r.q-t.q)+Math.abs(r.r-t.r)+Math.abs(r.s-t.s))/2>n)continue;if(this.checkPlacement(r))return r;const a=this.grid.getNeighbors(r);for(const h of a){const c=`${h.q},${h.r}`;e.has(c)||(e.add(c),s.push(h))}}return null}checkPlacement(t){return this.getGridHexes(t).every(s=>this.checkSingleHexPlacement(s))}onRotate(){if(this.isDragging)return;const t=this.isTriangularShape(),e=this.isTwoHexShape();if(t){let s=function(){const l=performance.now()-n;if(l<r){let u=l/r;u=.5-.5*Math.cos(Math.PI*u),h.container.rotation=o+(a-o)*u,requestAnimationFrame(s)}else h.container.rotation=a,h.pattern.hexes=h.pattern.hexes.map(u=>({q:-u.r,r:-u.s,s:-u.q,type:u.type,weight:u.weight})),console.log("Triangle shape rotation completed")};const n=performance.now(),r=300,o=this.container.rotation,a=o+Math.PI/3,h=this;requestAnimationFrame(s)}else if(e){let s=function(){const l=performance.now()-n;if(l<r){let u=l/r;u=u<.5?2*u*u:1-Math.pow(-2*u+2,2)/2,h.container.rotation=o+(a-o)*u,requestAnimationFrame(s)}else h.container.rotation=a,h.pattern.hexes=h.pattern.hexes.map(u=>({q:-u.q,r:-u.r,s:-u.s,type:u.type,weight:u.weight})),console.log("Two-hex shape flip completed")};const n=performance.now(),r=350,o=this.container.rotation,a=o+Math.PI,h=this;requestAnimationFrame(s)}else{let s=function(){const l=performance.now()-n;if(l<r){let u=l/r;u=u<.5?2*u*u:1-Math.pow(-2*u+2,2)/2,h.container.rotation=o+(a-o)*u,requestAnimationFrame(s)}else h.container.rotation=a,h.pattern.hexes=h.pattern.hexes.map(u=>({q:-u.q,r:-u.r,s:-u.s,type:u.type,weight:u.weight})),console.log("Non-triangle shape flip completed")};const n=performance.now(),r=400,o=this.container.rotation,a=o+Math.PI,h=this;requestAnimationFrame(s)}}isTriangularShape(){if(this.pattern.hexes.length!==3)return!1;const t=this.pattern.hexes,e=Array(3).fill(0).map(()=>Array(3).fill(!1));for(let s=0;s<3;s++)for(let n=0;n<3;n++){if(s===n)continue;const r=Math.abs(t[s].q-t[n].q)+Math.abs(t[s].r-t[n].r)+Math.abs(t[s].s-t[n].s);e[s][n]=r===2}for(let s=0;s<3;s++){let n=0;for(let r=0;r<3;r++)e[s][r]&&n++;if(n!==2)return!1}return!0}isTwoHexShape(){if(this.pattern.hexes.length!==2)return!1;const t=this.pattern.hexes[0],e=this.pattern.hexes[1];return Math.abs(t.q-e.q)+Math.abs(t.r-e.r)+Math.abs(t.s-e.s)===2}drawShape(){this.graphics.clear(),this.iconSpritesContainer.removeChildren(),this.pattern.hexes.forEach(e=>{const s=dn(e),n=this.iconTextures[e.type];bl(this.graphics,this.iconSpritesContainer,s,e.type,n,e.weight)});const t=this.container.getLocalBounds();this.container.pivot.set(t.x+t.width/2,t.y+t.height/2),this.container.position.set(this.container.position.x,this.container.position.y)}setPosition(t,e){this.container.position.set(t,e)}setInitialPosition(t,e){this.initialPosition.set(t,e),this.setPosition(t,e)}getGridHexes(t){return this.pattern.hexes.map(e=>({q:t.q+e.q,r:t.r+e.r,s:t.s+e.s,type:e.type,weight:e.weight}))}getHexCenter(t){for(let e=0;e<this.pattern.hexes.length;e++){const s=this.pattern.hexes[e];if(s.q===t.q&&s.r===t.r&&s.s===t.s)return dn(s)}return null}}function vs(i,t,e){let s;switch(t){case R.FIRE:s="🔥";break;case R.WATER:s="💧";break;case R.NATURE:s="🍃";break;default:s="❓"}const n=new je({fontSize:e*1.6,fontFamily:"Arial",fill:16777215,align:"center"}),r=new wt(s,n);r.anchor.set(.5),r.position.set(e,e);const o=new pt;o.addChild(r);const a=i.renderer.generateTexture(o);return r.destroy(),o.destroy(),a}const Ar={showScorePanel:!1,showGoalsPanel:!0,showLevelIndicator:!1,flyingIconGrowEffect:1.5,showTotalScore:!0,showBestScore:!0,showTutorialButton:!0,showDebugButtons:!1,goalsPanelConfig:{x:0,y:10,scale:1,width:300,height:80}};class Cl{constructor(t,e,s=Ar){Y(this,"app");Y(this,"uiContainer");Y(this,"scoreLabels",{});Y(this,"goalLabels",{});Y(this,"progressBars",{});Y(this,"levelLabel",null);Y(this,"config");Y(this,"totalScoreLabel",null);Y(this,"bestScoreLabel",null);Y(this,"totalScore",0);Y(this,"bestScore",0);Y(this,"goalsPanelWidth");Y(this,"goalsPanelHeight");Y(this,"scoreIconSize",16);Y(this,"tutorialButton",null);Y(this,"onTutorialButtonClick",null);Y(this,"loseScreenContainer",null);var n,r;this.app=t,this.config=s,this.goalsPanelWidth=((n=s.goalsPanelConfig)==null?void 0:n.width)||350,this.goalsPanelHeight=((r=s.goalsPanelConfig)==null?void 0:r.height)||80,this.uiContainer=new pt,this.uiContainer.name="uiContainer",this.loadBestScore(),this.initializeUI(e)}initializeUI(t){this.removeExistingUI(),this.config.showGoalsPanel&&this.createGoalsPanel(t),this.config.showScorePanel&&this.createScorePanel(t),(this.config.showTotalScore||this.config.showBestScore)&&this.createScoreDisplay(),this.config.showTutorialButton&&this.createTutorialButton();const e=this.config.goalsPanelConfig||{x:0,y:10,scale:1};this.uiContainer.scale.set(e.scale);const n=(this.app.screen.width-this.goalsPanelWidth*e.scale)/2+e.x;this.uiContainer.position.set(n,e.y),this.app.stage.addChild(this.uiContainer)}createScoreDisplay(){const t=new pt;t.name="scoreContainer",this.config.showTotalScore&&(this.totalScoreLabel=new wt("Score: 0",{fill:16777215,fontSize:24,fontFamily:"Arial",fontWeight:"bold",dropShadow:{alpha:.8,angle:Math.PI/6,blur:2,color:0,distance:2}}),this.totalScoreLabel.anchor.set(.5,0),this.totalScoreLabel.position.set(0,0),t.addChild(this.totalScoreLabel)),this.config.showBestScore&&(this.bestScoreLabel=new wt(`Best: ${this.bestScore}`,{fill:16766720,fontSize:22,fontFamily:"Arial",fontWeight:"bold",align:"center",dropShadow:{alpha:.8,angle:Math.PI/6,blur:3,color:0,distance:3},stroke:{color:10052885,width:3,alignment:0}}),this.bestScoreLabel.anchor.set(.5,0),this.bestScoreLabel.position.set(0,30),t.addChild(this.bestScoreLabel)),t.position.set(this.app.screen.width/2,100),this.app.stage.addChild(t)}loadBestScore(){const t=localStorage.getItem("hexMergeBestScore");t&&(this.bestScore=parseInt(t,10))}saveBestScore(){localStorage.setItem("hexMergeBestScore",this.bestScore.toString())}updateTotalScore(t){this.totalScore=Object.values(t).reduce((e,s)=>e+s,0),this.totalScoreLabel&&(this.totalScoreLabel.text=`Score: ${this.totalScore}`),this.totalScore>this.bestScore&&(this.bestScore=this.totalScore,this.bestScoreLabel&&(this.bestScoreLabel.text=`Best: ${this.bestScore}`,this.animateBestScoreUpdate()),this.saveBestScore())}animateBestScoreUpdate(){if(!this.bestScoreLabel)return;const t=this.bestScoreLabel.scale.x;this.bestScoreLabel.y,requestAnimationFrame(()=>{this.bestScoreLabel.scale.set(t*1.3);const s=()=>{const n=performance.now()/1e3,r=Math.sin(n*8)*.2,o=16766720,a=(o>>16&255)/255,h=(o>>8&255)/255,c=(o&255)/255,l=.8+r,u=Math.min(a*l,1)*255,f=Math.min(h*l,1)*255,d=Math.min(c*l,1)*255,x=Math.round(u)<<16|Math.round(f)<<8|Math.round(d);this.bestScoreLabel.style.fill=x,this.bestScoreLabel.scale.x>t?requestAnimationFrame(s):this.bestScoreLabel.style.fill=16766720};s(),setTimeout(()=>{let n=0;const r=.5,o=()=>{if(n+=1/60/r,n>=1){this.bestScoreLabel.scale.set(t);return}const a=1-Math.pow(1-n,3),h=t*1.3-.3*t*a;this.bestScoreLabel.scale.set(h),requestAnimationFrame(o)};requestAnimationFrame(o)},2e3)})}removeExistingUI(){const t=this.app.stage.getChildByName("uiContainer");t&&this.app.stage.removeChild(t);const e=this.app.stage.getChildByName("scoreContainer");e&&this.app.stage.removeChild(e)}createGoalsPanel(t){const e=new yt;e.roundRect(0,0,this.goalsPanelWidth,this.goalsPanelHeight,10),e.fill({color:3355443,alpha:.8}),e.stroke({width:2,color:5592405}),this.uiContainer.addChild(e),this.config.showLevelIndicator&&(this.levelLabel=new wt("Уровень 1",{fill:16766720,fontSize:14,fontFamily:"Arial",fontWeight:"bold"}),this.levelLabel.x=10,this.levelLabel.y=10,this.uiContainer.addChild(this.levelLabel)),this.createGoalIndicators(e,t)}createGoalIndicators(t,e){let r=(this.goalsPanelWidth-310)/2;const o=10;[R.FIRE,R.WATER,R.NATURE].forEach(a=>{const h=new pt;h.position.set(r,o),this.uiContainer.addChild(h);const c=new It(e[a]);c.width=30,c.height=30,c.anchor.set(.5,0),c.position.set(90/2,0),h.addChild(c);const l=new wt("0/0",{fill:16777215,fontSize:18,fontFamily:"Arial",fontWeight:"bold",align:"center",dropShadow:{alpha:.8,angle:Math.PI/6,blur:2,color:0,distance:2}});l.anchor.set(.5,.5),l.position.set(90/2,45),h.addChild(l),this.goalLabels[a]=l,r+=110})}createScorePanel(t){const e=this.goalsPanelHeight-25;let s=this.goalsPanelWidth-150;const n=6;[R.FIRE,R.WATER,R.NATURE].forEach(r=>{const o=new It(t[r]);o.width=this.scoreIconSize,o.height=this.scoreIconSize,o.x=s,o.y=e,this.uiContainer.addChild(o);const a=new wt("0",{fill:16777215,fontSize:14,fontFamily:"Arial",fontWeight:"bold"});a.anchor.set(0,.5),a.x=o.x+o.width+n/2,a.y=o.y+o.height/2,this.uiContainer.addChild(a),this.scoreLabels[r]=a,s-=50})}updateLevel(t){this.config.showLevelIndicator&&this.levelLabel&&(this.levelLabel.text=`Уровень ${t+1}`)}updateGoalDisplay(t,e){this.config.showGoalsPanel&&Object.keys(t).forEach(s=>{const n=s;if(n===R.EMPTY||!this.goalLabels[n])return;const r=t[n],o=e[n];this.goalLabels[n].text=`${r}/${o}`,r>=o&&this.animateCompletedGoal(this.goalLabels[n])})}animateCompletedGoal(t){const e=()=>{const n=performance.now()/1e3*.1%1,r=this.hslToRgb(n,1,.6);t.style.fill=r,t.parent&&requestAnimationFrame(e)};requestAnimationFrame(e)}hslToRgb(t,e,s){let n,r,o;if(e===0)n=r=o=s;else{const a=(l,u,f)=>(f<0&&(f+=1),f>1&&(f-=1),f<.16666666666666666?l+(u-l)*6*f:f<.5?u:f<.6666666666666666?l+(u-l)*(.6666666666666666-f)*6:l),h=s<.5?s*(1+e):s+e-s*e,c=2*s-h;n=a(c,h,t+1/3),r=a(c,h,t),o=a(c,h,t-1/3)}return(Math.round(n*255)<<16)+(Math.round(r*255)<<8)+Math.round(o*255)}animateScoreUpdate(t,e,s){if(e===s)return;const n=this.scoreLabels[t];if(!n)return;let r=e;const o=.8,a=performance.now(),h=()=>{const l=(performance.now()-a)/1e3;if(l<o){const u=l/o,f=u<.5?4*u*u*u:1-Math.pow(-2*u+2,3)/2;r=Math.floor(e+(s-e)*f),n.text=`${r}`;const d=1+.2*Math.sin(u*Math.PI);n.scale.set(d),requestAnimationFrame(h)}else n.text=`${s}`,n.scale.set(1)};if(requestAnimationFrame(h),this.goalLabels[t]){const c=this.goalLabels[t].text,l=parseInt(c.split("/")[1]);this.goalLabels[t].text=`${s}/${l}`,s>=l&&this.animateCompletedGoal(this.goalLabels[t])}}updateScoreDisplay(t){if(!this.config.showScorePanel){Object.keys(t).forEach(e=>{const s=e;if(s===R.EMPTY||!this.goalLabels[s])return;const n=this.goalLabels[s].text,r=n.includes("/")?parseInt(n.split("/")[1]):0,o=t[s];this.goalLabels[s].text=`${o}/${r}`,o>=r&&this.animateCompletedGoal(this.goalLabels[s])}),this.updateTotalScore(t);return}Object.keys(t).forEach(e=>{const s=e;if(s===R.EMPTY||!this.scoreLabels[s])return;const n=parseInt(this.scoreLabels[s].text)||0,r=t[s];n!==r&&this.animateScoreUpdate(s,n,r)}),this.updateTotalScore(t)}setUIVisibility(t){this.config={...this.config,...t},this.initializeUI({})}animateIconToScore(t,e,s,n=this.scoreIconSize,r){const o=new It(t);o.width=o.height=n,o.anchor.set(.5),o.position.copyFrom(e),this.app.stage.addChild(o);let a=0;const h=1,c=.5,l=this.config.flyingIconGrowEffect||1,u=e.x,f=e.y,d=.1;let x=0,_=!0;const g=()=>{if(_)if(x+=.016,x<d){o.y=f+Math.sin(x*20)*3,requestAnimationFrame(g);return}else _=!1;if(a<1){a+=.018;let b=Math.min(a,1);b=b<.5?2*b*b:1-Math.pow(-2*b+2,2)/2;const S=u+(s.x-u)*.5+40,M=f+(s.y-f)*.3-80,I=(1-b)*(1-b)*u+2*(1-b)*b*S+b*b*s.x,E=(1-b)*(1-b)*f+2*(1-b)*b*M+b*b*s.y,P=5*(1-b),O=5,W=Math.sin(b*Math.PI*O)*P,$=Math.cos(b*Math.PI*O)*P;o.position.set(I+W,E+$);let U;if(b<.5)U=1+(l-1)*(b*2);else{const nt=(b-.5)*2;U=l-(l-c/h)*nt}o.scale.set(h*U),o.rotation=a*Math.PI*2,requestAnimationFrame(g)}else o.scale.set(.4),setTimeout(()=>{o.scale.set(.6),setTimeout(()=>{o.scale.set(.4),setTimeout(()=>{o.destroy(),r&&r()},50)},50)},50)};requestAnimationFrame(g)}handleResize(){const t=this.config.goalsPanelConfig||{x:0,y:10,scale:1},s=(this.app.screen.width-this.goalsPanelWidth*t.scale)/2+t.x;this.uiContainer.position.set(s,t.y),this.tutorialButton&&this.tutorialButton.position.set(this.app.screen.width-70,this.app.screen.height-70);const n=this.app.stage.getChildByName("scoreContainer");n&&n.position.set(this.app.screen.width/2,100),this.shouldShowDebugButtons()&&console.log("[DEBUG] UI resize - debug buttons should be repositioned")}animateLevelUp(t){const e=new wt(`Уровень ${t+1}!`,{fill:16766720,fontSize:48,fontWeight:"bold",dropShadow:{alpha:.8,angle:Math.PI/6,blur:4,color:0,distance:6}});e.anchor.set(.5),e.position.set(this.app.screen.width/2,this.app.screen.height/2),e.alpha=0,e.scale.set(.5),this.app.stage.addChild(e);let s=0;const n=()=>{s+=.02,s<1?(e.alpha=Math.min(1,s*2),e.scale.set(.5+s*.5),requestAnimationFrame(n)):s<3?requestAnimationFrame(n):s<4?(e.alpha=1-(s-3),requestAnimationFrame(n)):e.destroy()};requestAnimationFrame(n)}createTutorialButton(){this.tutorialButton=new pt,this.tutorialButton.name="tutorialButton";const t=new yt;t.roundRect(0,0,40,40,8),t.fill({color:4886754,alpha:.8}),t.stroke({width:2,color:3834066}),this.tutorialButton.addChild(t);const e=new wt("❓",{fill:16777215,fontSize:22,fontWeight:"bold"});e.anchor.set(.5),e.position.set(20,20),this.tutorialButton.addChild(e),this.tutorialButton.position.set(this.app.screen.width-70,this.app.screen.height-70),this.tutorialButton.eventMode="static",this.tutorialButton.cursor="pointer",this.tutorialButton.on("pointertap",()=>{this.onTutorialButtonClick&&this.onTutorialButtonClick()}),this.tutorialButton.on("pointerover",()=>{t.scale.set(1.05),t.alpha=.9}),this.tutorialButton.on("pointerout",()=>{t.scale.set(1),t.alpha=.8}),this.app.stage.addChild(this.tutorialButton)}shouldShowDebugButtons(){return(window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1")&&this.config.showDebugButtons}createTutorialDebugButton(t){if(!this.shouldShowDebugButtons()){console.log("[DEBUG] Tutorial debug button not created: debug buttons are disabled");return}const e=new pt,s=new yt;s.roundRect(0,0,50,50,8),s.fill({color:4886754,alpha:.7}),s.stroke({width:2,color:3834066}),e.addChild(s);const n=new wt("🎓",{fill:16777215,fontSize:24,fontWeight:"bold"});n.anchor.set(.5),n.position.set(25,25),e.addChild(n),e.position.set(10,10),e.eventMode="static",e.cursor="pointer",e.on("pointertap",()=>{console.log("[DEBUG] Tutorial debug button clicked"),t.isActive()?t.stop():this.onTutorialButtonClick&&this.onTutorialButtonClick()}),e.on("pointerover",()=>{s.scale.set(1.05),s.alpha=.9}),e.on("pointerout",()=>{s.scale.set(1),s.alpha=.7}),this.app.stage.addChild(e),window.addEventListener("resize",()=>{e.position.set(10,10)})}createDeleteDebugButton(t){if(!this.shouldShowDebugButtons())return console.log("[DEBUG] Delete debug button not created: debug buttons are disabled"),null;const e=new pt,s=new yt;s.roundRect(0,0,50,50,8),s.fill({color:16724736,alpha:.7}),s.stroke({width:2,color:16746496}),e.addChild(s);const n=new wt("🗑️",{fill:16777215,fontSize:24,fontWeight:"bold"});return n.anchor.set(.5),n.position.set(25,25),e.addChild(n),e.position.set(this.app.screen.width-110,10),e.eventMode="static",e.cursor="pointer",e.on("pointertap",()=>{console.log("[DEBUG] Delete debug button clicked"),t()}),e.on("pointerover",()=>{s.scale.set(1.05),s.alpha=.9}),e.on("pointerout",()=>{s.scale.set(1),s.alpha=.7}),e}createBonusDebugButton(t){if(!this.shouldShowDebugButtons())return console.log("[DEBUG] Bonus debug button not created: debug buttons are disabled"),null;const e=new pt,s=new yt;s.roundRect(0,0,50,50,8),s.fill({color:16763904,alpha:.7}),s.stroke({width:2,color:16755200}),e.addChild(s);const n=new wt("⭐",{fill:16777215,fontSize:24,fontWeight:"bold"});return n.anchor.set(.5),n.position.set(25,25),e.addChild(n),e.position.set(this.app.screen.width-170,10),e.eventMode="static",e.cursor="pointer",e.on("pointertap",()=>{console.log("[DEBUG] Bonus debug button clicked"),t()}),e.on("pointerover",()=>{s.scale.set(1.05),s.alpha=.9}),e.on("pointerout",()=>{s.scale.set(1),s.alpha=.7}),e}showLoseScreen(t,e){console.log("[DEBUG] Showing lose screen with final score:",t),this.loseScreenContainer&&(this.app.stage.removeChild(this.loseScreenContainer),this.loseScreenContainer.destroy()),this.loseScreenContainer=new pt,this.loseScreenContainer.name="loseScreenContainer";const s=new yt;s.rect(0,0,this.app.screen.width,this.app.screen.height),s.fill({color:0,alpha:.7}),this.loseScreenContainer.addChild(s);const n=new wt("Game Over!",{fill:16777215,fontSize:48,fontFamily:"Arial",fontWeight:"bold",align:"center",dropShadow:{alpha:.8,angle:Math.PI/6,blur:4,color:0,distance:4}});n.anchor.set(.5),n.position.set(this.app.screen.width/2,this.app.screen.height/2-100),this.loseScreenContainer.addChild(n);const r=new wt(`Final Score: ${t}`,{fill:16766720,fontSize:36,fontFamily:"Arial",fontWeight:"bold",align:"center",dropShadow:{alpha:.8,angle:Math.PI/6,blur:4,color:0,distance:4}});r.anchor.set(.5),r.position.set(this.app.screen.width/2,this.app.screen.height/2),this.loseScreenContainer.addChild(r);const o=new pt,a=new yt;a.roundRect(0,0,200,60,10),a.fill({color:5025616,alpha:.9}),a.stroke({width:3,color:16777215}),o.addChild(a);const h=new wt("Play Again",{fill:16777215,fontSize:24,fontFamily:"Arial",fontWeight:"bold"});h.anchor.set(.5),h.position.set(100,30),o.addChild(h),o.position.set(this.app.screen.width/2-100,this.app.screen.height/2+80),o.eventMode="static",o.cursor="pointer",o.on("pointerover",()=>{a.scale.set(1.05),a.tint=6732650}),o.on("pointerout",()=>{a.scale.set(1),a.tint=16777215}),o.on("pointertap",()=>{a.scale.set(.95),setTimeout(()=>{this.loseScreenContainer&&(this.loseScreenContainer.alpha=0,e())},100)}),this.loseScreenContainer.addChild(o),this.app.stage.addChild(this.loseScreenContainer),this.loseScreenContainer.alpha=0;const c=performance.now(),l=.5,u=()=>{const f=(performance.now()-c)/1e3,d=Math.min(f/l,1);this.loseScreenContainer&&(this.loseScreenContainer.alpha=d,n.scale.set(.5+.5*d),r.scale.set(.5+.5*d),d<1&&requestAnimationFrame(u))};requestAnimationFrame(u)}hideLoseScreen(){if(this.loseScreenContainer){const t=performance.now(),e=.3,s=()=>{const n=(performance.now()-t)/1e3,r=Math.min(n/e,1);this.loseScreenContainer&&(this.loseScreenContainer.alpha=1-r,r<1?requestAnimationFrame(s):(this.app.stage.removeChild(this.loseScreenContainer),this.loseScreenContainer.destroy(),this.loseScreenContainer=null))};requestAnimationFrame(s)}}createLoseTestButton(t){if(!this.shouldShowDebugButtons())return console.log("[DEBUG] Lose test button not created: debug buttons are disabled"),null;const e=new pt,s=new yt;s.roundRect(0,0,50,50,8),s.fill({color:16753920,alpha:.7}),s.stroke({width:2,color:16711680}),e.addChild(s);const n=new wt("💀",{fill:16777215,fontSize:24,fontWeight:"bold"});return n.anchor.set(.5),n.position.set(25,25),e.addChild(n),e.position.set(this.app.screen.width-230,10),e.eventMode="static",e.cursor="pointer",e.on("pointertap",()=>{console.log("[DEBUG] Lose test button clicked"),t()}),e.on("pointerover",()=>{s.scale.set(1.05),s.alpha=.9}),e.on("pointerout",()=>{s.scale.set(1),s.alpha=.7}),e}}class Ml{constructor(t){Y(this,"app");Y(this,"container");Y(this,"active",!1);Y(this,"handSprite");Y(this,"targetShape",null);Y(this,"handTexture");Y(this,"animationTimer",null);this.app=t,this.container=new pt,this.container.name="tutorialContainer";const e=new wt("👆",{fontSize:50,fill:16777215});this.handTexture=this.app.renderer.generateTexture(e),this.handSprite=new It(this.handTexture),this.handSprite.anchor.set(.5,.1),this.handSprite.scale.set(1.2);const s=new yt;s.beginFill(16777215,.2),s.drawCircle(0,0,35),s.endFill(),this.container.addChild(s),this.container.addChild(this.handSprite),this.handSprite.visible=!1,s.visible=!1,this.app.stage.addChild(this.container),this.app.stage.sortableChildren=!0,this.container.zIndex=1e3}start(t){this.active||(this.active=!0,this.targetShape=t,this.container.children.forEach(e=>{e.visible=!0}),this.bringToFront(),this.animateDrag(),this.addInteractionListeners())}bringToFront(){this.app.stage.sortableChildren||(this.app.stage.sortableChildren=!0);const t=Math.max(...Array.from(this.app.stage.children).filter(e=>e!==this.container&&"zIndex"in e).map(e=>e.zIndex||0));this.container.zIndex=Math.max(t+100,1e3),this.app.stage.sortChildren(),this.app.stage.removeChild(this.container),this.app.stage.addChild(this.container)}animateDrag(){if(!this.targetShape)return;const t=this.targetShape.initialPosition;this.container.position.set(t.x,t.y+60);const e=this.app.screen.width/2,s=this.app.screen.height/2;let n=0;const r=performance.now();let o=0;const a=()=>{if(!this.active)return;o++,o>=30&&(this.bringToFront(),o=0);const c=(performance.now()-r)/1e3;if(n===0){const l=Math.min(c/1,1),u=Math.sin(c*5)*3,f=Math.cos(c*5)*3;this.container.position.set(t.x+u,t.y+60-l*50+f),this.handSprite.rotation=Math.sin(c*3)*.1-l*.2,l>=1&&(n=1,this.bringToFront())}else if(n===1){const l=Math.min((c-1)/2,1);l<.1?(this.handSprite.scale.set(1.1-l*.5),this.handSprite.rotation=-.2-l*.3):l>=.95?(this.handSprite.scale.set(1.1+(l-.95)*2),this.handSprite.rotation=-.5+(l-.95)*.5):(this.handSprite.scale.set(1.1),this.handSprite.rotation=-.5);const u=t.x+(e-t.x)*.5,f=t.y-80,d=l,x=(1-d)*(1-d)*t.x+2*(1-d)*d*u+d*d*e,_=(1-d)*(1-d)*t.y+2*(1-d)*d*f+d*d*s;if(this.container.position.set(x,_),l>=1){n=0,this.animationTimer=setTimeout(()=>{this.animateDrag()},1e3);return}}this.animationTimer=requestAnimationFrame(a)};this.animationTimer=requestAnimationFrame(a)}addInteractionListeners(){if(!this.targetShape)return;this.targetShape.container.on("pointerdown",this.stop.bind(this),{once:!0})}stop(){this.active=!1;const t=()=>{this.container.alpha>0?(this.container.alpha-=.1,requestAnimationFrame(t)):(this.container.children.forEach(e=>{e.visible=!1}),this.container.alpha=1)};t(),this.animationTimer!==null&&(cancelAnimationFrame(this.animationTimer),clearTimeout(this.animationTimer),this.animationTimer=null),this.targetShape&&(this.targetShape.container.off("pointerdown",this.stop.bind(this)),this.targetShape=null)}isActive(){return this.active}}let le=null;const ot={availableShapesCount:3,bonusHexChance:.4,hexSize:30,shapeSpacingMultiplier:6,placementCheckDelay:500,cameraShake:{duration:.4,intensity:2.5,minIntensity:1.5,maxIntensity:4,decay:3,frequency:5},mobile:{scaleFactor:.65,maxWidth:768,aspectRatioThreshold:.8,shapeSpacingMultiplier:5,cameraShake:{intensity:1.8,minIntensity:1,maxIntensity:2.5,duration:.3}}},Oe=()=>{const i=window.innerWidth<=ot.mobile.maxWidth,t=window.innerWidth/window.innerHeight,e=t<=ot.mobile.aspectRatioThreshold,s="ontouchstart"in window||navigator.maxTouchPoints>0,n=navigator.userAgent.toLowerCase(),o=["android","iphone","ipod","ipad","mobile","tablet"].some(h=>n.includes(h)),a=i&&s||e&&s||i&&o;return console.log(`[DEBUG] Device detection: isMobile=${a}, width=${window.innerWidth}, aspectRatio=${t.toFixed(2)}, touch=${s}`),a},Ts=[{hexes:[{q:0,r:0,s:0,type:R.FIRE},{q:1,r:-1,s:0,type:R.NATURE},{q:0,r:-1,s:1,type:R.WATER}]},{hexes:[{q:0,r:0,s:0,type:R.WATER},{q:-1,r:1,s:0,type:R.FIRE},{q:0,r:1,s:-1,type:R.NATURE},{q:1,r:0,s:-1,type:R.WATER}]},{hexes:[{q:0,r:0,s:0,type:R.NATURE},{q:1,r:0,s:-1,type:R.FIRE},{q:-1,r:0,s:1,type:R.WATER}]},{hexes:[{q:0,r:0,s:0,type:R.FIRE},{q:1,r:-1,s:0,type:R.WATER}]},{hexes:[{q:0,r:0,s:0,type:R.WATER},{q:0,r:-1,s:1,type:R.NATURE}]},{hexes:[{q:0,r:0,s:0,type:R.NATURE},{q:-1,r:0,s:1,type:R.FIRE}]}],_e=[R.FIRE,R.WATER,R.NATURE],K=new ar,vl={hexes:[{q:0,r:0,s:0,type:R.EMPTY}]},fn=[{[R.FIRE]:20,[R.WATER]:20,[R.NATURE]:20},{[R.FIRE]:50,[R.WATER]:50,[R.NATURE]:50},{[R.FIRE]:100,[R.WATER]:100,[R.NATURE]:100},{[R.FIRE]:200,[R.WATER]:200,[R.NATURE]:200},{[R.FIRE]:400,[R.WATER]:400,[R.NATURE]:400},{[R.FIRE]:800,[R.WATER]:800,[R.NATURE]:800}];let pn=0,qe=!1;const Tl=(i,t)=>{var f,d;const e=Oe();let n=e&&ot.mobile.cameraShake?ot.mobile.cameraShake.intensity:ot.cameraShake.intensity;if(t){const x=e&&((f=ot.mobile.cameraShake)!=null&&f.minIntensity)?ot.mobile.cameraShake.minIntensity:ot.cameraShake.minIntensity,_=e&&((d=ot.mobile.cameraShake)!=null&&d.maxIntensity)?ot.mobile.cameraShake.maxIntensity:ot.cameraShake.maxIntensity,g=Math.min(Math.max(t,1),6)/6;n=x+g*(_-x),console.log(`[DEBUG] Dynamic shake intensity: ${n.toFixed(2)} for shape size ${t} (range: ${x.toFixed(2)}-${_.toFixed(2)})`)}const r=e&&ot.mobile.cameraShake?ot.mobile.cameraShake.duration:ot.cameraShake.duration,o=ot.cameraShake.decay,a=ot.cameraShake.frequency,h=i.stage.position.x,c=i.stage.position.y;console.log(`[DEBUG] Camera shake: intensity=${n.toFixed(2)}, duration=${r}s, decay=${o}, frequency=${a}`);const l=performance.now();function u(){const _=(performance.now()-l)/1e3;if(_<r){const g=Math.max(0,1-Math.pow(_/r,o)),b=n*g,w=b*Math.sin(_*a*2*Math.PI),S=b*Math.sin((_+.25)*a*2*Math.PI);i.stage.position.x=h+w,i.stage.position.y=c+S,requestAnimationFrame(u)}else i.stage.position.x=h,i.stage.position.y=c}requestAnimationFrame(u)},Pr=(i,t,e,s,n=!0)=>{const r=`${s.q},${s.r}`,o=e.get(r);if(!o)return console.log(`[DEBUG] Failed to find hex info for key ${r} during removal.`),!1;console.log(`[DEBUG] Removing hex at ${r}, type: ${o.type}, weight: ${o.weight}`),e.delete(r)||console.log(`[DEBUG] Failed to delete hex ${r} from placedHexes map.`);const h=`icon_${s.q}_${s.r}`,c=i.stage.getChildByName(h,!0),l=`weight_${s.q}_${s.r}`,u=i.stage.getChildByName(l,!0);if(n){if(c){let d=function(){const g=(performance.now()-x)/1e3,b=Math.min(g/_,1);c.alpha=1-b,c.scale.set(1-b*.5),b<1?requestAnimationFrame(d):c.destroy()};const x=performance.now(),_=.3;requestAnimationFrame(d)}if(u){let d=function(){const g=(performance.now()-x)/1e3,b=Math.min(g/_,1);u.alpha=1-b,b<1?requestAnimationFrame(d):u.destroy()};const x=performance.now(),_=.3;requestAnimationFrame(d)}const f=t.getHexGraphics(s);if(f){let d=function(){const g=(performance.now()-x)/1e3,b=Math.min(g/_,1);f.alpha=1-b;const w=1-b*.3;f.scale.set(w),b<1?requestAnimationFrame(d):(t.drawHex(s,R.EMPTY),console.log(`[DEBUG] Hex at ${r} removed and redrawn as EMPTY after animation`))};const x=performance.now(),_=.5;requestAnimationFrame(d)}else t.drawHex(s,R.EMPTY),console.log(`[DEBUG] Hex at ${r} immediately redrawn as EMPTY (no graphics found for animation)`)}else c&&c.destroy(),u&&u.destroy(),t.drawHex(s,R.EMPTY),console.log(`[DEBUG] Hex at ${r} immediately removed without animation`);return!0},Al=(i,t,e,s=!0)=>{if(e.size===0)return console.log("[DEBUG] No hexes to remove"),{removed:!1};const n=Array.from(e.keys()),r=Math.floor(Math.random()*n.length),o=n[r],[a,h]=o.split(",").map(Number),c={q:a,r:h,s:-a-h},l=Pr(i,t,e,c,s);return{removed:l,hex:l?c:void 0}};async function Pl(){var xt;await K.init({width:window.innerWidth,height:window.innerHeight,backgroundColor:15527921,antialias:!0,resolution:window.devicePixelRatio||1,autoDensity:!0}),(xt=document.getElementById("app"))==null||xt.appendChild(K.canvas);const i=ot.hexSize,t={[R.FIRE]:vs(K,R.FIRE,i*.6),[R.WATER]:vs(K,R.WATER,i*.6),[R.NATURE]:vs(K,R.NATURE,i*.6)},e=new _l(K,i,"pointy",t),s=3;e.drawHexagonalGrid(s);const n=new Map,r={[R.FIRE]:0,[R.WATER]:0,[R.NATURE]:0},o={...Ar,flyingIconGrowEffect:2},a=new Cl(K,t,o);le=new Ml(K),a.onTutorialButtonClick=()=>{console.log("Tutorial button clicked"),_.length>0&&!le.isActive()?(le.start(_[0]),console.log("Starting tutorial on first shape")):console.log("Tutorial not started - no shapes available or tutorial already active")};const h=window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1";h&&a.createTutorialDebugButton(le);let c=localStorage.getItem("hexMergeTutorialShown")==="true";const l=a.createDeleteDebugButton(()=>{console.log("[DEBUG] Delete button clicked");const z=Al(K,e,n,!0);z.removed&&z.hex?console.log(`[DEBUG] Successfully initiated removal of hex at ${z.hex.q},${z.hex.r}`):console.log("[DEBUG] Failed to remove random hex.")});l&&K.stage.addChild(l);const u=a.createBonusDebugButton(()=>{if(_.length===0){console.log("[DEBUG] No shapes available to replace with bonus");return}const z=Math.floor(Math.random()*_.length),p=_[z],m={x:p.initialPosition.x,y:p.initialPosition.y},y=performance.now(),C=300;function v(){const A=performance.now()-y;if(A<C){let k=A/C;const F=k<.3?1+k*.5:(1-k)*1.2;p.container.scale.set(F),p.container.alpha=1-k,requestAnimationFrame(v)}else{p.container.destroy();const k=_.findIndex(F=>F===p);k>-1&&_.splice(k,1),setTimeout(()=>{const F=M(m,!0);console.log("[DEBUG] Replaced shape with bonus hex"),F.container.alpha=0,F.container.scale.set(.1);const q=performance.now(),Z=400;function X(){const lt=performance.now()-q;if(lt<Z){const tt=lt/Z,bt=(B=>1+2.70158*Math.pow(B-1,3)+1.70158*Math.pow(B-1,2))(tt);F.container.scale.set(bt),F.container.alpha=tt,requestAnimationFrame(X)}else F.container.scale.set(1),F.container.alpha=1}requestAnimationFrame(X)},150)}}requestAnimationFrame(v)});u&&K.stage.addChild(u),window.addEventListener("resize",()=>{console.log("[DEBUG] Resize: Updating debug buttons positions if needed"),l&&l.position.set(K.screen.width-110,10),u&&u.position.set(K.screen.width-170,10),st&&st.position.set(K.screen.width-230,10)}),a.shouldShowDebugButtons()&&console.log("[DEBUG] Debug buttons added");const f=()=>{a.updateScoreDisplay(r)},d=z=>{const p=`${z.q},${z.r}`;return e.isValidHex(z)&&!n.has(p)},x=(z,p)=>z.getGridHexes(p).every(y=>d(y)),_=[],g=()=>{const p=Oe()?ot.mobile.shapeSpacingMultiplier:ot.shapeSpacingMultiplier;return i*p},b=g(),w=()=>{const p=Oe()?4:5;return K.screen.height-i*p},S=w();(K.screen.width-b*(Ts.length-1))/2;const M=(z,p=!1)=>{console.log(`Creating shape at ${z.x}, ${z.y}, forceBonusHex=${p}`);let m;if(p){console.log("Creating single bonus hex as a figure"),m={hexes:[...vl.hexes]};const k=_e[Math.floor(Math.random()*_e.length)],F=Math.floor(Math.random()*7)+3;m.hexes[0]={...m.hexes[0],type:k,weight:F},qe=!0,console.log(`Created SINGLE bonus hex figure with type ${k} and weight ${F}`)}else{const k=Math.floor(Math.random()*Ts.length);m={hexes:Ts[k].hexes.map(X=>{const j=_e[Math.floor(Math.random()*_e.length)];return{...X,type:j,weight:1}})};const q=m.hexes.length===3&&I(m.hexes);let Z=0;if(q?(Z=Math.floor(Math.random()*6),console.log(`Applied ${Z} random rotations to triangular shape`)):(Z=Math.floor(Math.random()*2),Z===1&&(m.hexes=m.hexes.map(X=>({...X,q:-X.q,r:-X.r,s:-X.s})),console.log("Applied random flip to non-triangular shape"))),q)for(let X=0;X<Z;X++)m.hexes=m.hexes.map(j=>({...j,q:-j.r,r:-j.s,s:-j.q}))}const y=new Sl(K,e,m,t,d,le);y.setInitialPosition(z.x,z.y),y.onPlace=(k,F)=>{x(k,F)?E(k,F):(console.log("Placement invalid, returning shape."),k.setPosition(k.initialPosition.x,k.initialPosition.y))},K.stage.addChild(y.container),_.push(y),y.container.scale.set(.1),y.container.alpha=0;let C=1,v=0,T=.05;function A(){if(v<1){v+=T;const k=Math.min(v,1);let F;F=C*(1+Math.sin(k*Math.PI)*.2*(1-k)),y.container.scale.set(F),y.container.alpha=k,requestAnimationFrame(A)}}return requestAnimationFrame(A),y};function I(z){if(z.length!==3)return!1;const p=Array(3).fill(0).map(()=>Array(3).fill(!1));for(let m=0;m<3;m++)for(let y=0;y<3;y++){if(m===y)continue;const C=Math.abs(z[m].q-z[y].q)+Math.abs(z[m].r-z[y].r)+Math.abs(z[m].s-z[y].s);p[m][y]=C===2}for(let m=0;m<3;m++){let y=0;for(let C=0;C<3;C++)p[m][C]&&y++;if(y!==2)return!1}return!0}const E=(z,p)=>{var bt;console.log(`Handling placement for shape at ${p.q},${p.r}`);const m=(B,L,G)=>{e.drawHex(B,L,1.05,!0);const H=e.getHexGraphics(B);if(H){let re=function(){if(!Lt||!Lt.parent)return;const ri=(performance.now()-oe)/1e3%Kt/Kt,Er=.4+.3*Math.sin(ri*Math.PI*2),kr=1+.1*Math.sin(ri*Math.PI*2);Lt.alpha=Er,Lt.scale.set(kr),H.parent&&requestAnimationFrame(re)};H.tint=16777215;const Lt=new yt;Lt.lineStyle(3,Ee[L],.7),Lt.drawCircle(0,0,i*.8),Lt.name=`glow_${B.q}_${B.r}`,H.addChild(Lt);const oe=performance.now(),Kt=2;requestAnimationFrame(re)}const J=K.stage.getChildByName(`weight_${B.q}_${B.r}`,!0);J&&J.destroy();const et=e.hexToPixel(B),V=e.gridContainer.toGlobal(et),D=new wt(G.toString(),{fill:16777215,fontSize:20,fontWeight:"bold",dropShadow:{alpha:1,angle:Math.PI/6,blur:0,color:0,distance:1}});D.name=`weight_${B.q}_${B.r}`,D.anchor.set(.5),D.position.copyFrom(V),D.y+=10,K.stage.addChild(D),"zIndex"in D&&(D.zIndex=100);const Q=`icon_${B.q}_${B.r}`,ut=K.stage.getChildByName(Q,!0);ut&&"zIndex"in ut&&(ut.zIndex=10,ut.y-=10),"sortableChildren"in K.stage&&(K.stage.sortableChildren=!0,K.stage.sortChildren());const dt=performance.now(),Bt=1.5;function ne(){if(!D||!D.parent)return;const oe=(performance.now()-dt)/1e3%Bt/Bt,Kt=1+.2*Math.sin(oe*Math.PI*2);D.scale.set(Kt),requestAnimationFrame(ne)}return requestAnimationFrame(ne),D},y=(B,L)=>{console.log(`[DEBUG] Finding connected hexes starting from (${B.q},${B.r}) of type ${L}`);const G=[B],H=new Set,J=[],et=`${B.q},${B.r}`;for(H.add(et),n.has(et)?J.push(B):console.log(`[DEBUG] Start hex ${et} not found in placedHexes, starting search without it.`);G.length>0;){const V=G.shift(),D=e.getNeighbors(V);for(const Q of D){const ut=`${Q.q},${Q.r}`;if(H.has(ut))continue;const dt=n.get(ut);dt&&dt.type===L&&(H.add(ut),G.push(Q),J.push(Q))}}return console.log(`[DEBUG] Found ${J.length} connected hexes of type ${L}`),J};console.log("[DEBUG] Pattern info:",JSON.stringify({hexCount:z.pattern.hexes.length,firstHexType:z.pattern.hexes[0].type,firstHexWeight:z.pattern.hexes[0].weight||1,hasBonus:z.pattern.hexes.some(B=>(B.weight||1)>1)}));const C=z.getGridHexes(p);let v={[R.FIRE]:0,[R.WATER]:0,[R.NATURE]:0};const T=[],A=[],k=[],F=new Set;C.forEach(B=>{const L=`${B.q},${B.r}`;if(B.type!==R.EMPTY){const G=B.weight||1;console.log(`[DEBUG] Placing hex at ${L} with type ${B.type}, weight ${G}`),n.set(L,{type:B.type,weight:G}),T.push(B),G<=1?(e.drawHex(B,B.type,1,!0),console.log(`Placed and drew NON-bonus hex ${L} with type ${B.type}`)):(console.log(`[DEBUG] Identified bonus hex at ${L} (weight ${G}), deferring drawing.`),A.push(B))}}),console.log(`[DEBUG] === STARTING BONUS HEX PROCESSING STAGE === Found ${A.length} bonus hex(es)`),A.forEach(B=>{const L=`${B.q},${B.r}`,G=n.get(L);if(!G){console.warn(`[WARN] Bonus hex info for ${L} not found in placedHexes during bonus processing stage.`);return}const H=G.type;let J=G.weight;console.log(`[DEBUG] Processing bonus hex at ${L}, type: ${H}, initial weight: ${J}`);const et=y(B,H);console.log(`[DEBUG] Found ${et.length} connected hexes (including self) for bonus hex at ${L}`);const V=et.filter(D=>!(D.q===B.q&&D.r===B.r));if(console.log(`[DEBUG] Filtered list: ${V.length} hexes identified for removal.`),V.length>0){console.log(`[DEBUG] === STARTING ABSORPTION PROCESS === for ${V.length} hexes`);let D=0;V.forEach((Q,ut)=>{const dt=`${Q.q},${Q.r}`;console.log(`[DEBUG] ABSORBING: Hex #${ut+1} at ${dt}`);const Bt=n.get(dt);if(Bt){console.log(`[DEBUG] Neighbor info: type=${Bt.type}, weight=${Bt.weight}`),D+=Bt.weight;const ne=Pr(K,e,n,Q,!0);console.log(ne?`[DEBUG] Successfully initiated removal for neighbor ${dt}`:`[DEBUG] Failed to initiate removal for neighbor ${dt}`)}else console.log(`[DEBUG] ERROR: No hex info found for neighbor ${dt} in placedHexes map during absorption!`)}),J+=D,n.get(L).weight=J,console.log(`[DEBUG] ABSORPTION COMPLETE: Added total weight ${D}. Bonus hex ${L} new weight: ${J}`)}else console.log(`[DEBUG] NO NEIGHBORS found to absorb for bonus hex at ${L}`);console.log(`[DEBUG] Drawing bonus hex ${L} with final weight ${J}`),m(B,H,J),F.add(L)}),console.log("[DEBUG] === STARTING SCORING STAGE ===");const q=new Map;for(const B of _e)q.set(B,[]);for(const[B,L]of n.entries()){if(L.type===R.EMPTY||F.has(B))continue;const[G,H]=B.split(",").map(Number),J={q:G,r:H,s:-G-H},et=y(J,L.type);et.length>1&&et.some(D=>C.some(Q=>Q.q===D.q&&Q.r===D.r&&Q.type===L.type))&&((bt=q.get(L.type))==null||bt.push(et),et.forEach(D=>{const Q=`${D.q},${D.r}`;F.add(Q)}))}q.forEach((B,L)=>{if(B.length!==0)if(B.length===1){const H=B[0].filter(V=>{const D=`${V.q},${V.r}`;return n.has(D)});if(H.length<=0)return;const J=H.filter(V=>C.some(D=>D.q===V.q&&D.r===V.r&&D.type===L)),et=H.filter(V=>!C.some(D=>D.q===V.q&&D.r===V.r));if(J.length>0&&et.length>0){let V=0;H.forEach(Q=>{const ut=`${Q.q},${Q.r}`,dt=n.get(ut);dt&&(V+=dt.weight)});const D=L;v[D]!==void 0?(v[D]+=V,console.log(`Scored +${V} for ${D} for connecting with existing hexes`)):console.warn(`[WARN] Attempted to increment score for invalid type: ${L}`),Z(H,L)}else console.log(`No score for ${L} - group consists only of newly placed hexes`)}else{const G=new Map;B.forEach(V=>{V.forEach(D=>{const Q=`${D.q},${D.r}`;n.has(Q)&&G.set(Q,D)})});const H=Array.from(G.values());if(H.length<=0)return;const J=H.filter(V=>C.some(D=>D.q===V.q&&D.r===V.r&&D.type===L)),et=H.filter(V=>!C.some(D=>D.q===V.q&&D.r===V.r));if(J.length>0&&et.length>0){let V=0;H.forEach(Q=>{const ut=`${Q.q},${Q.r}`,dt=n.get(ut);dt&&(V+=dt.weight)});const D=L;v[D]!==void 0?(v[D]+=V,console.log(`Scored +${V} for ${D} for connecting with existing hexes (multiple groups)`)):console.warn(`[WARN] Attempted to increment score for invalid type: ${L} (multiple groups)`),Z(H,L)}else console.log(`No score for ${L} - combined groups consist only of newly placed hexes`)}});function Z(B,L){const G=B.filter(J=>C.some(et=>et.q===J.q&&et.r===J.r&&et.type===L)),H=B.filter(J=>!C.some(et=>et.q===J.q&&et.r===J.r));if(G.length>0&&H.length>0){let J=0;G.forEach(V=>{const D=`${V.q},${V.r}`,Q=n.get(D);Q&&(J+=Q.weight)}),H.forEach(V=>{const D=`${V.q},${V.r}`,Q=n.get(D);Q&&(J+=Q.weight)});const et=L;v[et]!==void 0?(v[et]+=J,console.log(`Scored +${J} for ${et} for connecting with existing hexes`)):console.warn(`[WARN] Attempted to increment score for invalid type: ${L} (prepareGroupForAnimation)`),G.forEach(V=>{k.push({hex:V,type:L,neighbors:[...H]})})}else console.log(`No score for ${L} - no connection with existing hexes`)}r[R.FIRE]+=v[R.FIRE],r[R.WATER]+=v[R.WATER],r[R.NATURE]+=v[R.NATURE],f(),console.log("[DEBUG] Applying camera shake effect on shape placement");const X=z.pattern.hexes.length;Tl(K,X);const j=()=>{T.forEach(B=>{const L=e.getHexGraphics(B);if(!L)return;const G=n.get(`${B.q},${B.r}`);if(G&&G.type!==R.EMPTY){let H=function(){if(!(L!=null&&L.parent))return;const Q=(performance.now()-et)/1e3;if(Q<=V){const dt=3355443*(1-Q/V),Bt=Math.min(J+dt,16777215);L.tint=Bt,requestAnimationFrame(H)}else L.tint=16777215};const J=Ee[G.type],et=performance.now(),V=.4;requestAnimationFrame(H)}})},lt=()=>{k.length!==0&&setTimeout(()=>{k.forEach((B,L)=>{setTimeout(()=>{const G=[B.hex,...B.neighbors];G.forEach(D=>{const Q=`icon_${D.q}_${D.r}`,ut=K.stage.getChildByName(Q,!0);ut&&(ut.visible=!1)});const H=e.hexToPixel(B.hex),J=e.gridContainer.toGlobal(H),et=a.uiContainer.children.find(D=>D instanceof It&&D.texture===t[B.type]&&D.width===a.scoreIconSize);let V;if(et)V=K.stage.toLocal(et.getGlobalPosition());else{const D=a.uiContainer.x+a.uiContainer.width/2,Q=a.uiContainer.y+40;let ut=0;B.type===R.FIRE?(ut=-120,console.log("Flying FIRE icon to LEFT position")):B.type===R.NATURE?(ut=120,console.log("Flying NATURE icon to RIGHT position")):console.log("Flying WATER icon to CENTER position"),V={x:D+ut,y:Q}}a.animateIconToScore(t[B.type],J,V,i*.6,()=>{setTimeout(()=>{G.forEach((D,Q)=>{setTimeout(()=>{const ut=`icon_${D.q}_${D.r}`,dt=K.stage.getChildByName(ut,!0);if(dt){let Bt=function(){const oe=(performance.now()-ne)/1e3;if(oe<re){const Kt=oe/re,ni=Math.min(1,Kt*1.2*(1-.2*Math.sin(Kt*Math.PI)));dt.scale.set(ni),requestAnimationFrame(Bt)}else dt.scale.set(1)};dt.visible=!0,dt.scale.set(0);const ne=performance.now(),re=.3;requestAnimationFrame(Bt)}},Q*30)})},100)})},L*100)})},500)};j(),k.length>0&&lt(),f();const tt={x:z.initialPosition.x,y:z.initialPosition.y};z.container.destroy();const Pt=_.findIndex(B=>B===z);Pt>-1&&_.splice(Pt,1),setTimeout(()=>{const B=Math.random()<ot.bonusHexChance&&!qe;console.log(`[DEBUG] Creating new shape. hasBonusHexInCurrentSet=${qe}, shouldCreateBonus=${B}, random=${Math.random().toFixed(2)}`),M(tt,B),_.length===0&&P(),setTimeout(()=>{U()},ot.placementCheckDelay)},300)},P=()=>{qe=!1,_.forEach(y=>{y.container.parent&&y.container.destroy()}),_.length=0;const p=Math.random()<ot.bonusHexChance?Math.floor(Math.random()*ot.availableShapesCount):-1,m=g();console.log(`[DEBUG] Generating shapes with spacing: ${m}`);for(let y=0;y<ot.availableShapesCount;y++)setTimeout(()=>{const C=m*(ot.availableShapesCount-1),T=(K.screen.width-C)/2+y*m,A=y===p;M({x:T,y:S},A),A&&console.log(`Creating bonus hex at position ${y}`)},y*150);setTimeout(()=>{!c&&_.length>0&&(le.start(_[0]),localStorage.setItem("hexMergeTutorialShown","true"),c=!0)},1e3)};P(),a.updateGoalDisplay(r,fn[pn]);const O=()=>{const z=Oe();K.renderer.resize(window.innerWidth,window.innerHeight);const p=z?ot.mobile.scaleFactor:1;if(K.stage.scale.set(p),z){const m=K.screen.width*(1/p),y=K.screen.height*(1/p),C=(m-K.screen.width)/2,v=(y-K.screen.height)/2;K.stage.position.set(C,v),console.log(`[DEBUG] Resize: Applied mobile scaling: scale=${p}, offsetX=${C}, offsetY=${v}`)}else K.stage.position.set(0,0),console.log("[DEBUG] Resize: Applied desktop scaling (no scale)");if(e.recenter(K.screen.width/2,K.screen.height/2),a.handleResize(),_.length>0){const m=g(),y=w(),C=m*(ot.availableShapesCount-1),v=(K.screen.width-C)/2;_.forEach((T,A)=>{const k=v+A*m;T.setInitialPosition(k,y);const F={x:T.container.x,y:T.container.y};Math.sqrt(Math.pow(F.x-T.initialPosition.x,2)+Math.pow(F.y-T.initialPosition.y,2))<i*3&&(T.setPosition(k,y),console.log(`[DEBUG] Repositioned shape ${A} to ${k},${y}`))}),console.log(`[DEBUG] Resize: Repositioned ${_.length} shapes with spacing=${m}`)}h&&console.log("[DEBUG] Resize: Updating debug buttons positions if needed")};window.addEventListener("resize",O),window.addEventListener("orientationchange",()=>{console.log(`[DEBUG] Orientation changed, screen size: ${window.innerWidth}x${window.innerHeight}`),setTimeout(O,100)});let W=!1;const $=()=>{if(console.log("[DEBUG] Checking if any shape can be placed on grid"),_.length===0)return console.log("[DEBUG] No shapes to check for placement"),!1;for(const z of _){console.log(`[DEBUG] Checking shape with ${z.pattern.hexes.length} hexes`);const p=[];for(let m=-3;m<=s;m++)for(let y=Math.max(-3,-m-s);y<=Math.min(s,-m+s);y++){const C=-m-y;p.push({q:m,r:y,s:C})}for(const m of p)if(x(z,m))return console.log(`[DEBUG] Found valid placement for shape at ${m.q},${m.r}`),!0}return console.log("[DEBUG] No valid placements found for any shape"),!1},U=()=>{if(!W)if(console.log("[DEBUG] Checking lose condition"),$())console.log("[DEBUG] Game can continue - valid placements available");else{console.log("[DEBUG] Game over - no valid placements left"),W=!0;const z=Object.values(r).reduce((p,m)=>p+m,0);a.showLoseScreen(z,()=>{console.log("[DEBUG] Restarting game after loss"),nt()})}},nt=()=>{console.log("[DEBUG] Restarting game"),W=!1,a.hideLoseScreen(),Object.keys(r).forEach(z=>{r[z]=0}),e.drawHexagonalGrid(s),n.clear(),P(),a.updateGoalDisplay(r,fn[pn]),f()};setTimeout(()=>{U()},1e3);const st=a.createLoseTestButton(()=>{const z=Object.values(r).reduce((p,m)=>p+m,0);a.showLoseScreen(z,nt)});st&&(K.stage.addChild(st),window.addEventListener("resize",()=>{st.position.set(K.screen.width-230,10)}))}Pl();export{nr as $,ir as A,At as B,pt as C,Ht as D,rt as E,$t as F,Ze as G,To as H,wi as I,It as J,Qo as K,aa as L,it as M,_t as N,Ct as O,ct as P,ua as Q,Gs as R,Ia as S,Fe as T,Bs as U,Ci as V,ls as W,vi as X,Eo as Y,Mt as Z,Pn as _,Nt as a,at as a0,mt as a1,ma as a2,Ha as a3,sh as a4,nh as a5,lh as a6,uh as a7,dh as a8,Zs as a9,ie as aa,pl as ab,gl as ac,Ye as ad,je as ae,ue as af,ll as ag,yr as ah,qi as ai,Ui as aj,Yr as ak,yt as al,jr as am,wr as an,Ae as b,Ne as c,Xs as d,Ft as e,Li as f,Pa as g,tr as h,$n as i,Wt as j,Jn as k,eh as l,ih as m,li as n,ah as o,ch as p,hr as q,ho as r,js as s,ht as t,jn as u,Xr as v,kt as w,ph as x,Vt as y,vn as z};
