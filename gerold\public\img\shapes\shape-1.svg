<svg width="822" height="808" viewBox="0 0 822 808" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_263_439)">
<ellipse cx="411" cy="404" rx="161" ry="154" fill="url(#paint0_linear_263_439)"/>
</g>
<defs>
<filter id="filter0_f_263_439" x="0" y="0" width="822" height="808" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="125" result="effect1_foregroundBlur_263_439"/>
</filter>
<linearGradient id="paint0_linear_263_439" x1="411" y1="250" x2="411" y2="558" gradientUnits="userSpaceOnUse">
<stop stop-color="#7343D2"/>
<stop offset="1" stop-color="#7343D2" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
