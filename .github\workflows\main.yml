name: Deploy to Server

on:
  push:
    branches:
      - master  

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout репозитория
        uses: actions/checkout@v3

      - name: Установка SSH-клиента
        run: sudo apt-get install -y sshpass

      - name: Деплой на сервер
        run: |
          sshpass -p "${{ secrets.SSH_PASSWORD }}" ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << EOF
          cd D2DStudioVRWebsite2
          git pull origin master
          git lfs pull
          cd gerold
          npm i
          npm run build
          pm2 restart D2DStudioVRWebsite2
          EOF
