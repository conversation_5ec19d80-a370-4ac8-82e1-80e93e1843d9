'use client'

import { useState, useEffect } from 'react'
import deviceDetection from '@/libs/deviceDetection'

export function DeviceDebug() {
  const [deviceInfo, setDeviceInfo] = useState<any>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Get device info on mount
    setDeviceInfo(deviceDetection.getDeviceInfo())
  }, [])

  if (!deviceInfo) return null

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 left-4 z-50 bg-purple-600 text-white px-3 py-2 rounded-lg text-sm font-mono hover:bg-purple-700 transition-colors"
      >
        Device Info
      </button>

      {/* Debug panel */}
      {isVisible && (
        <div className="fixed bottom-16 left-4 z-50 bg-black/90 text-white p-4 rounded-lg max-w-sm text-xs font-mono">
          <h3 className="text-purple-400 font-bold mb-2">Device Detection</h3>
          <div className="space-y-1">
            <div className={`${deviceInfo.shouldUseStatic ? 'text-red-400' : 'text-green-400'}`}>
              <strong>Use Static:</strong> {deviceInfo.shouldUseStatic ? 'YES' : 'NO'}
            </div>
            <div className={`${deviceInfo.isAndroid ? 'text-yellow-400' : 'text-gray-400'}`}>
              <strong>Android:</strong> {deviceInfo.isAndroid ? 'YES' : 'NO'}
            </div>
            <div className={`${deviceInfo.isLowEnd ? 'text-orange-400' : 'text-gray-400'}`}>
              <strong>Low-end:</strong> {deviceInfo.isLowEnd ? 'YES' : 'NO'}
            </div>
            <div>
              <strong>Mobile:</strong> {deviceInfo.isMobile ? 'YES' : 'NO'}
            </div>
            <div>
              <strong>Memory:</strong> {deviceInfo.memory || 'N/A'} GB
            </div>
            <div>
              <strong>CPU Cores:</strong> {deviceInfo.cores || 'N/A'}
            </div>
            <div>
              <strong>Connection:</strong> {deviceInfo.connection?.effectiveType || 'N/A'}
            </div>
            <div>
              <strong>Screen:</strong> {deviceInfo.screen?.width}x{deviceInfo.screen?.height}
            </div>
          </div>
          
          <details className="mt-3">
            <summary className="cursor-pointer text-purple-400 hover:text-purple-300">
              User Agent
            </summary>
            <p className="mt-1 text-gray-300 text-xs break-all">
              {deviceInfo.userAgent}
            </p>
          </details>
        </div>
      )}
    </>
  )
} 