"use client";
import ButtonPrimary from "@/components/shared/buttons/ButtonPrimary";
import ButtonPrimary2 from "@/components/shared/buttons/ButtonPrimary2";
import { useHeaderContext } from "@/context_api/HeaderContext";
import getNavItems from "@/libs/getNavItems";
import indexingAndActiveLink from "@/libs/indexingAndActiveLink";
import Link from "next/link";
import { useEffect } from "react";

const Navbar = ({ isActiveMobileMenu, setIsActiveMobileMenu, isSticky }) => {
	const { isIndexPage, isInnerPage, isResumeBtn, headerType } =
		useHeaderContext();
	const navItems = getNavItems();
	useEffect(() => {
		// mobileMenuController();
		indexingAndActiveLink();
	}, []);
	return (
		<nav className="-mt-8 lg:-mt-6">
			<ul className="nav flex items-center gap-x-5 xl:gap-x-30px 2xl:gap-x-45px">
				{navItems?.length
					? navItems?.map(({ name, path, path2 }, idx) => (
							<li key={idx} className="nav_item group relative hidden lg:block">
								<Link
									href={isIndexPage ? path : path2}
									className={`text-size-15 font-medium ${
										isInnerPage && !isSticky
											? "text-white-color"
											: "text-seondary-color dark:text-white-color"
									}  capitalize py-2 md:py-3 lg:py-4 2xl:py-5 relative z-0 after:w-0 after:h-0.5 after:bg-gradient-primary after:absolute after:right-0 hover:after:left-0 after:bottom-[10px] after:transition-all after:duration-500 group-hover:after:w-full`}
								>
									{name}
								</Link>
							</li>
					  ))
					: ""}

				{/* <!-- action button --> */}
				{headerType === 3 ? (
					""
				) : (
					<li>
						{isResumeBtn ? (
							<ButtonPrimary2 url={"#"}>Resume</ButtonPrimary2>
						) : (
							<ButtonPrimary url={isIndexPage ? "#contact" : "/#contact"}>
								Hire Us!
							</ButtonPrimary>
						)}
					</li>
				)}
				{/* <!-- open mobile menu button --> */}
				<li className="menu-bar lg:hidden z-[9999] relative flex items-center">
					<div className="menu-bar">
						<button
							className={isActiveMobileMenu ? `active z-[9999] relative` : "z-[9999] relative"}
							onClick={() => setIsActiveMobileMenu(!isActiveMobileMenu)}
							aria-label="Toggle Mobile Menu"
							style={{ pointerEvents: 'auto' }}
						>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
						</button>
					</div>
				</li>
			</ul>
		</nav>
	);
};

export default Navbar;
