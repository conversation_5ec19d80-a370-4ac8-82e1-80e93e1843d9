const Resume3 = () => {
	return (
		<section id="resume">
			<div className="py-20 md:py-100px lg:py-30 dark:bg-black-color-2 bg-cream-light-color ">
				<div className="container">
					{/* <!-- section heading --> */}
					<div className="mb-10 md:mb-75px xl:mb-90px text-center">
						<h2
							className="text-3xl md:text-size-35 lg:text-size-40 xl:text-size-45 inline-block bg-gradient-text-light dark:bg-gradient-text bg-clip-text leading-1.2 text-transparent wow fadeInUp"
							data-wow-delay=".3s"
						>
							My Resume
						</h2>
					</div>
					{/* <!-- resumes --> */}
					<div className="grid md:grid-cols-2 gap-y-50px gap-0 md:gap-25px lg:gap-30px">
						{/* <!--  resume 1 --> */}
						<div>
							<div
								className="mb-10 md:mb-50px wow fadeInUp"
								data-wow-delay=".3s"
							>
								<h2 className="text-3xl  md:text-4xl lg:text-size-40 text-primary-color dark:text-white-color leading-1.2 md:leading-1.2  mb-30px md:mb-45px font-medium">
									Education
								</h2>
							</div>
							<div
								className="pb-35px pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-[30px] after:left-1.5 after:z-10 wow fadeInUp"
								data-wow-delay=".3s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-mortarboard  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2022 - Present
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Lead Developer
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											Blockdots, London
										</p>
									</div>
								</div>
							</div>
							<div
								className="pb-35px pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-[30px] after:left-1.5 after:z-10 wow fadeInUp"
								data-wow-delay=".4s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-mortarboard  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2021 - 2022
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Full Stack Web Developer
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											Parsons, The New School
										</p>
									</div>
								</div>
							</div>
							<div
								className="pb-35px pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-[30px] after:left-1.5 after:z-10 wow fadeInUp"
								data-wow-delay=".5s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-mortarboard  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2020 - 2021
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											UI Designer
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											House of Life, Leeds
										</p>
									</div>
								</div>
							</div>
							<div
								className="pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 wow fadeInUp"
								data-wow-delay=".6s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-mortarboard  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2018 - 2020
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Junior Graphics Designer
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											Theme Junction, Bursa
										</p>
									</div>
								</div>
							</div>
						</div>
						{/* <!--  resume 2 --> */}
						<div>
							<div
								className="mb-10 md:mb-50px wow fadeInUp"
								data-wow-delay=".3s"
							>
								<h2 className="text-3xl  md:text-4xl lg:text-size-40 text-primary-color dark:text-white-color leading-1.2 md:leading-1.2  mb-30px md:mb-45px font-medium">
									Work Experience
								</h2>
							</div>
							<div
								className="pb-35px pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-[30px] after:left-1.5 after:z-10 wow fadeInUp"
								data-wow-delay=".3s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-hand-bag  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2020 - 2023
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Programming course
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											Harverd University
										</p>
									</div>
								</div>
							</div>
							<div
								className="pb-35px pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-[30px] after:left-1.5 after:z-10 wow fadeInUp"
								data-wow-delay=".4s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-hand-bag  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2016 - 2020
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Graphic design course
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											University of Denmark
										</p>
									</div>
								</div>
							</div>
							<div
								className="pb-35px pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-[30px] after:left-1.5 after:z-10 wow fadeInUp"
								data-wow-delay=".5s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-hand-bag  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2012 - 2015
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Web design course
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											University of California
										</p>
									</div>
								</div>
							</div>
							<div
								className="pl-25px md:pl-10 relative before:w-13px before:h-13px before:bg-primary-color dark:before:bg-bg-color-3 before:absolute before:left-0 before:top-[25px] before:rounded-full before:z-20 wow fadeInUp"
								data-wow-delay=".6s"
							>
								<div className="flex gap-15px md:gap-5 items-start">
									<div className="flex-shrink-0 w-57px h-57px bg-primary-color dark:bg-seondary-color inline-flex justify-center items-center rounded-100% text-white-color">
										<i className="flaticon flaticon-hand-bag  text-3xl leading-[8px]"></i>
									</div>
									<div>
										<p className="text-primary-color group-hover:text-white transition-all duration-300 text-xl font-extrabold mb-10px">
											2010 - 2011
										</p>
										<h4 className="text-xl text-primary-color-light dark:text-white-color mb-10px uppercase font-extrabold">
											Design & Technology
										</h4>
										<p className=" text-body-color-2 dark:text-body-color group-hover:text-white-color transition-all duration-300">
											Parsons, The New School
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default Resume3;
