import React from "react";

const Resume2 = () => {
  return (
    <section id="resume">
      <div className="pt-60px md:pt-100px lg:pt-30">
        <div className="container">
          {/* <!-- section heading --> */}
          <div className="mb-10 md:mb-50px">
            <h2
              className="text-3xl md:text-size-35 lg:text-size-40 xl:text-size-45 inline-block bg-gradient-text-light dark:bg-gradient-text bg-clip-text leading-1.2 text-transparent wow fadeInUp"
              data-wow-delay=".3s"
            >
              Education & Work Experience
            </h2>
          </div>
          {/* <!-- resumes --> */}
          <div className="flex justify-between flex-wrap gap-50px md:gap-15px px-5 py-30px lg:px-35px lg:pt-10 lg:pb-70px bg-cream-light-color dark:bg-primary-color-light border border-body-color dark:border-seondary-color rounded-15px">
            {/* <!--  resume 1 --> */}
            <div>
              <h3 className="text-primary-color text-lg md:text-xl font-semibold mb-30px md:mb-45px uppercase">
                EDUCATION
              </h3>
              <div
                className="pb-35px pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-1.5 after:left-1.5 after:z-10 wow fadeInUp"
                data-wow-delay=".3s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  BA IN DESIGN
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">January 2024 - Present</p>
              </div>
              <div
                className="pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 wow fadeInUp"
                data-wow-delay=".4s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  UX/UI DESIGN SCHOOL
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">April 2024 - Present</p>
              </div>
            </div>
            {/* <!--  resume 2 --> */}
            <div>
              <h3 className="text-primary-color text-lg md:text-xl font-semibold mb-30px md:mb-45px uppercase">
                EXPERIENCE
              </h3>
              <div
                className="pb-35px pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-1.5 after:left-1.5 after:z-10 wow fadeInUp"
                data-wow-delay=".3s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  LION PARCEL
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">January 2024 - Present</p>
              </div>
              <div
                className="pb-35px pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-1.5 after:left-1.5 after:z-10 wow fadeInUp"
                data-wow-delay=".4s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  ENVER STUDIO
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">May 2024 - Present</p>
              </div>
              <div
                className="pb-35px pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-1.5 after:left-1.5 after:z-10 wow fadeInUp"
                data-wow-delay=".5s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  TOKO DISTRIBUTOR
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">March 2024 - Present</p>
              </div>
              <div
                className="pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 wow fadeInUp"
                data-wow-delay=".6s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  SAKOO
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">June 2024 - Present</p>
              </div>
            </div>
            {/* <!--  resume 2 --> */}
            <div>
              <h3 className="text-primary-color text-lg md:text-xl font-semibold mb-30px md:mb-45px uppercase">
                PAST ROLES
              </h3>
              <div
                className="pb-35px pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-1.5 after:left-1.5 after:z-10 wow fadeInUp"
                data-wow-delay=".3s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  LION PARCEL
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">January 2024 - Present</p>
              </div>
              <div
                className="pb-35px pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 after:w-1px after:h-full after:bg-border-color dark:after:bg-bg-color-2 after:absolute after:top-1.5 after:left-1.5 after:z-10 wow fadeInUp"
                data-wow-delay=".4s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  ENVER STUDIO
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">May 2024 - Present</p>
              </div>
              <div
                className="pl-35px lg:pl-50px relative before:w-13px before:h-13px before:bg-primary-color before:absolute before:left-0 before:top-0 before:rounded-full before:z-20 wow fadeInUp"
                data-wow-delay=".5s"
              >
                <h4 className="text-primary-color-light dark:text-white-color text-lg md:text-xl leading-1.2 md:leading-1.2 font-medium mb-3 uppercase">
                  TOKO DISTRIBUTOR
                </h4>

                <p className="text-primary-color-light dark:text-white-color font-medium mb-2">
                  Product Designer
                </p>
                <p className="text-gray-color">March 2024 - Present</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume2;
