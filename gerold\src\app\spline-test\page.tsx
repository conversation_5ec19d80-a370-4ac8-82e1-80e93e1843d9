'use client'

import { SplineTest } from "@/components/ui/spline-test";
import { SplineScene } from "@/components/ui/spline";
import { DeviceDebug } from "@/components/ui/device-debug";

export default function SplineTestPage() {
  return (
    <div className="min-h-screen bg-dark-color pt-20">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Spline Fallback Test
          </h1>
          <p className="text-gray-400 text-lg">
            This page shows the static fallback image that Android and low-end devices will see.
          </p>
        </div>
        
        <div className="grid gap-8">
          {/* Regular Spline (3D version) */}
          <div className="bg-white/5 p-6 rounded-lg">
            <h2 className="text-2xl font-semibold text-white mb-4">
              Regular Version (3D Spline)
            </h2>
            <p className="text-gray-400 mb-4">
              This is what desktop/iPhone users see
            </p>
            <div className="w-full h-[500px] bg-black/[0.96] relative overflow-hidden rounded-lg">
              <div className="absolute top-4 left-4 z-10 bg-green-500 text-white px-3 py-1 rounded-lg text-sm font-mono">
                3D VERSION
              </div>
              <SplineScene 
                scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
                className="w-full h-full"
                fallbackImage="/img/hero/d2d-card.jpeg"
                fallbackAlt="D2D Studio - VR/AR Development Agency"
              />
            </div>
          </div>

          {/* Static Fallback Version */}
          <div className="bg-white/5 p-6 rounded-lg">
            <h2 className="text-2xl font-semibold text-white mb-4">
              Static Fallback Version
            </h2>
            <p className="text-gray-400 mb-4">
              This is what Android/low-end devices see
            </p>
            <SplineTest />
          </div>
        </div>
      </div>
      
      <DeviceDebug />
    </div>
  )
} 