import { Suspense } from "react";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "./css/animate.min.css";
import "./css/backToTop.css";
import "./css/flaticon_gerold.css";
import "./css/font-awesome-pro.min.css";
import "./css/glightbox.min.css";
import "./css/nice-select2.css";
import "./css/odometer-theme-default.css";
import "./globals.css";

import Footer from "@/components/layout/footer/Footer";
import Header from "@/components/layout/header/Header";
import BackToTop from "@/components/shared/others/BackToTop";
import Preloader from "@/components/shared/others/Preloader";
import GoogleAuthProvider from "@/components/providers/GoogleAuthProvider";
import CommonContext from "@/context_api/CommonContext";
import HeaderContextProvider from "@/context_api/HeaderContext";
import FooterContextProvider from "@/context_api/FooterContext";
import { DeviceDebug } from "@/components/ui/device-debug";

export const metadata = {
    metadataBase: new URL('https://d2d.media'),
    title: "D2D Studio",
    description: "High quality interactive VR/AR simulators and breathtaking 3D models for your business.",
    openGraph: {
        title: "D2D Studio — build immersive products with us",
        description: "VR/AR, 3D, Mobile, Web & Game development by a full‑cycle tech agency",
        images: [
            {
                url: '/D2D.png',
                width: 1200,
                height: 630,
                alt: 'D2D Studio',
            },
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: "D2D Studio — build immersive products with us",
        description: "VR/AR, 3D, Mobile, Web & Game development by a full‑cycle tech agency",
        images: ['/D2D.png'],
    },
};

export default function RootLayout({ children }) {
    return (
        <html lang="en" className="dark ">
            <body
                className={`font-sora  dark:bg-dark-color overflow-x-hidden  relative`}
            >
                <GoogleAuthProvider>
                    <CommonContext>
                        {/* <!-- start preloader --> */}
                        <Preloader />
                        {/* <!-- end preloader --> */}

                        {/* <!-- header --> */}
                        <HeaderContextProvider
                            value={{ 
                                isIndexPage: true, 
                                isInnerPage: false, 
                                headerType: 1, 
                                isResumeBtn: false 
                            }}
                        >
                            <Header />
                        </HeaderContextProvider>
                        {/* <!-- header --> */}

                        {/* <!-- main --> */}
                        <main id="main">
                            {children}
                        </main>
                        {/* <!-- main --> */}

                        {/* <!-- footer --> */}
                        <FooterContextProvider value={{ footerType: 1 }}>
                            <Footer />
                        </FooterContextProvider>
                        {/* <!-- footer --> */}

                        {/* <!-- back to top --> */}
                        <BackToTop />
                        {/* <!-- back to top --> */}

                        {/* <!-- Device Debug (remove in production) --> */}
                        <DeviceDebug />
                        {/* <!-- Device Debug --> */}
                    </CommonContext>
                </GoogleAuthProvider>
            </body>
        </html>
    );
}
