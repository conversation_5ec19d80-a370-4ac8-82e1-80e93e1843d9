"use client";
import { useHeaderContext } from "@/context_api/HeaderContext";
import Image from "next/image";
import Link from "next/link";

const Logo = ({ isSticky }) => {
  const { isInnerPage } = useHeaderContext();
  return (
    <Link href="/" className={`logo -mt-3 lg:-mt-6 ${isSticky ? 'lg:logo-sticky' : ''}`}>
      <Image
        className={`h-40 w-auto ${
          isInnerPage && !isSticky
            ? "inline-block dark:inline-block"
            : "hidden dark:inline-block"
        }`}
        src="/img/logo/d2d-logo.png"
        alt="D2D Studio Logo"
        width={150}
        height={48}
        style={{ objectFit: "contain" }}
      />
      <Image
        className={`h-40 w-auto ${
          isInnerPage && !isSticky
            ? "hidden"
            : "inline-block dark:hidden"
        }`}
        src="/img/logo/d2d-logo.avif"
        alt="D2D Studio Logo"
        width={150}
        height={48}
        style={{ objectFit: "contain" }}
      />
    </Link>
  );
};

export default Logo;
