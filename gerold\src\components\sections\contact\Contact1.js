import FormSelect from "@/components/shared/Inputs/FormSelect";
import { useState } from "react";

const Contact1 = () => {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        subject: "",
        message: ""
    });
    
    const [submitStatus, setSubmitStatus] = useState(null);
    
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };
    
    const handleSelectChange = (value) => {
        setFormData(prev => ({
            ...prev,
            subject: value
        }));
    };

    // Функция валидации формы
    const validateForm = () => {
        // Required fields
        const required = ['firstName', 'lastName', 'email', 'message'];
        const errors = {};
        
        required.forEach(field => {
            if (!formData[field]) {
                errors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
            }
        });
        
        // Email validation
        if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) {
            errors.email = 'Please enter a valid email address';
        }
        
        return errors;
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        
        // Валидация формы
        const errors = validateForm();
        if (Object.keys(errors).length > 0) {
            setSubmitStatus({
                success: false,
                message: "Please fill out all required fields correctly.",
                errors
            });
            return;
        }
        
        try {
            // Формируем параметры для mailto ссылки
            const subject = encodeURIComponent(`Website Contact from ${formData.firstName} ${formData.lastName}`);
            
            // Формируем тело письма
            const body = encodeURIComponent(
                `From: ${formData.firstName} ${formData.lastName}\n` +
                `Email: ${formData.email}\n` +
                (formData.phone ? `Phone: ${formData.phone}\n` : '') +
                `Subject: ${formData.subject || 'New Inquiry'}\n\n` +
                formData.message
            );
            
            // Создаем mailto ссылку
            const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
            
            // Сразу открываем почтовый клиент
            window.location.href = mailtoLink;
            
        } catch (error) {
            console.error("Error opening email client:", error);
            setSubmitStatus({ 
                success: false, 
                message: "We couldn't open your email client automatically. Please try again or email us directly."
            });
        }
    };

    return (
        <section id="contact">
            <div className="bg-cream-light-color dark:bg-black-color py-60px md:py-20 lg:py-100px xl:py-30">
                <div className="container">
                    <div className="flex flex-col-reverse md:grid md:grid-cols-12 md:items-center gap-x-6 gap-y-10 overflow-hidden">
                        {/* <!-- section heading --> */}
                        <div className="md:col-start-1 md:col-span-7 lg:col-span-6">
                            <div className=" wow fadeInLeft" data-wow-delay=".3s">
                                <form 
                                    className="contact px-15px py-30px md:px-5 lg:px-30px lg:py-10 xl:px-10 bg-white-color dark:bg-primary-color-light rounded-15px"
                                    onSubmit={handleSubmit}
                                >
                                    <div className="mb-25px text-center">
                                        <h2 className="text-3xl md:text-size-35 lg:text-size-40 xl:text-size-45 bg-gradient-text-light dark:bg-gradient-text bg-clip-text xl:leading-1.2 text-transparent mb-15px">
                                            Let's work together!
                                        </h2>
                                        <p
                                            className="text-primary-color-light dark:text-body-color wow fadeInLeft"
                                            data-wow-delay=".4s"
                                        >
                                            Fill out the form below and we'll get back to you as soon as possible.
                                        </p>
                                    </div>

                                    {/* Form status message */}
                                    {submitStatus && !submitStatus.errors && !submitStatus.success && (
                                        <div className="mb-5 p-3 rounded-lg text-center bg-red-100 text-red-700 border border-red-300">
                                            <p>{submitStatus.message}</p>
                                        </div>
                                    )}

                                    {/* <!-- inputs --> */}
                                    <div
                                        className="grid grid-cols-1 sm:grid-cols-2 gap-15px wow fadeInUp"
                                        data-wow-delay=".5s"
                                    >
                                        {/* <!-- first name --> */}
                                        <div>
                                            <input
                                                type="text"
                                                name="firstName"
                                                value={formData.firstName}
                                                onChange={handleChange}
                                                placeholder="First name"
                                                className={`text-white-color w-full px-5 py-14px border ${submitStatus?.errors?.firstName ? 'border-red-500' : 'border-gray-color-3'} bg-cream-light-color dark:bg-black-color focus:border-primary-color rounded-lg outline-none focus:outline-none transition-all duration-300 placeholder:text-gray-color leading-1`}
                                                required
                                            />
                                            {submitStatus?.errors?.firstName && (
                                                <p className="text-red-500 text-xs mt-1">{submitStatus.errors.firstName}</p>
                                            )}
                                        </div>
                                        {/* <!-- Last name --> */}
                                        <div>
                                            <input
                                                type="text"
                                                name="lastName"
                                                value={formData.lastName}
                                                onChange={handleChange}
                                                placeholder="Last name"
                                                className={`text-white-color w-full px-5 py-14px border ${submitStatus?.errors?.lastName ? 'border-red-500' : 'border-gray-color-3'} bg-cream-light-color dark:bg-black-color focus:border-primary-color rounded-lg outline-none focus:outline-none transition-all duration-300 placeholder:text-gray-color leading-1`}
                                                required
                                            />
                                            {submitStatus?.errors?.lastName && (
                                                <p className="text-red-500 text-xs mt-1">{submitStatus.errors.lastName}</p>
                                            )}
                                        </div>
                                        {/* <!-- Email address --> */}
                                        <div>
                                            <input
                                                type="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleChange}
                                                placeholder="Email address"
                                                className={`text-white-color w-full px-5 py-14px border ${submitStatus?.errors?.email ? 'border-red-500' : 'border-gray-color-3'} bg-cream-light-color dark:bg-black-color focus:border-primary-color rounded-lg outline-none focus:outline-none transition-all duration-300 placeholder:text-gray-color leading-1`}
                                                required
                                            />
                                            {submitStatus?.errors?.email && (
                                                <p className="text-red-500 text-xs mt-1">{submitStatus.errors.email}</p>
                                            )}
                                        </div>
                                        {/* <!-- Phone number --> */}
                                        <div>
                                            <input
                                                type="text"
                                                name="phone"
                                                value={formData.phone}
                                                onChange={handleChange}
                                                placeholder="Phone number (optional)"
                                                className="text-white-color w-full px-5 py-14px border border-gray-color-3 bg-cream-light-color dark:bg-black-color focus:border-primary-color rounded-lg outline-none focus:outline-none transition-all duration-300 placeholder:text-gray-color leading-1"
                                            />
                                        </div>
                                        <div className="form_group sm:col-start-1 sm:col-span-2">
                                            <FormSelect onChange={handleSelectChange} />
                                        </div>
                                        <div className="sm:col-start-1 sm:col-span-2">
                                            <textarea
                                                cols="1"
                                                rows="10"
                                                name="message"
                                                value={formData.message}
                                                onChange={handleChange}
                                                placeholder="Message"
                                                className={`text-white-color w-full px-5 py-14px border ${submitStatus?.errors?.message ? 'border-red-500' : 'border-gray-color-3'} bg-cream-light-color dark:bg-black-color focus:border-primary-color rounded-lg outline-none focus:outline-none transition-all duration-300 placeholder:text-gray-color leading-1`}
                                                required
                                            />
                                            {submitStatus?.errors?.message && (
                                                <p className="text-red-500 text-xs mt-1">{submitStatus.errors.message}</p>
                                            )}
                                        </div>
                                        <div className="sm:col-start-1 sm:col-span-2">
                                            <button
                                                type="submit"
                                                className="text-size-15 font-bold text-white-color capitalize py-17px px-35px bg-200 bg-gradient-secondary hover:bg-[-100%] rounded-full leading-1 transition-all duration-300"
                                            >
                                                Send Message
                                            </button>
                                            {submitStatus && submitStatus.errors && (
                                                <p className="mt-2 text-red-500">
                                                    {submitStatus.message}
                                                </p>
                                            )}
                                        </div>
                                        <div className="sm:col-start-1 sm:col-span-2 text-center mt-2">
                                            <p className="text-primary-color-light dark:text-body-color text-sm">
                                                Or email us directly at{' '}
                                                <a 
                                                    href="mailto:<EMAIL>"
                                                    className="text-primary-color underline hover:no-underline"
                                                >
                                                    <EMAIL>
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        {/* <!-- contact info area --> */}
                        <div className="md:col-start-8 md:col-span-5">
                            <div className="contact-info-list">
                                <ul className="flex flex-col gap-y-10">
                                    <li
                                        className="flex items-center gap-25px position-relative wow fadeInRight"
                                        data-wow-delay=".4s"
                                    >
                                        <div className="icon-box text-xl flex-shrink-0 w-50px h-50px text-white-color flex justify-center items-center flex-col bg-gradient-primary-2 rounded-full leading-1">
                                            <i className="flaticon-phone-call leading-1 mt-1"></i>
                                        </div>
                                        <div className="text-box">
                                            <p className="text-primary-color-light dark:text-white-color mb-1">
                                                Phone (WhatsApp)
                                            </p>
                                            <a
                                                href="tel:+31686215945"
                                                className="text-primary-color-light dark:text-white-color text-lg lg:text-xl font-medium hover:text-primary-color"
                                            >
                                                +31686215945
                                            </a>
                                        </div>
                                    </li>
                                    <li
                                        className="flex items-center gap-25px position-relative wow fadeInRight"
                                        data-wow-delay=".5s"
                                    >
                                        <div className="icon-box text-xl flex-shrink-0 w-50px h-50px text-white-color flex justify-center items-center flex-col bg-gradient-primary-2 rounded-full leading-1">
                                            <i className="flaticon-mail-inbox-app leading-1 mt-1"></i>
                                        </div>
                                        <div className="text-box">
                                            <p className="text-primary-color-light dark:text-white-color mb-1">
                                                Email
                                            </p>
                                            <a
                                                href="mailto:<EMAIL>"
                                                className="text-primary-color-light dark:text-white-color text-lg lg:text-xl font-medium hover:text-primary-color"
                                            >
                                                <EMAIL>
                                            </a>
                                        </div>
                                    </li>
                                    <li
                                        className="flex items-center gap-25px position-relative wow fadeInRight"
                                        data-wow-delay=".6s"
                                    >
                                        <div className="icon-box text-xl flex-shrink-0 w-50px h-50px text-white-color flex justify-center items-center flex-col bg-gradient-primary-2 rounded-full leading-1">
                                            <i className="flaticon-location leading-1 mt-1"></i>
                                        </div>
                                        <div className="text-box">
                                            <p className="text-primary-color-light dark:text-white-color mb-1">
                                                Address
                                            </p>
                                            <a
                                                href="#"
                                                className="text-primary-color-light dark:text-white-color text-lg lg:text-xl font-medium hover:text-primary-color"
                                            >
                                                Veldm. Montgomerylaan 339 03, <br />
                                                Netherlands, Eindhoven
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Contact1;
