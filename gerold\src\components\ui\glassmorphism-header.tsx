'use client'

import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface GlassmorphismHeaderProps {
  children: React.ReactNode
  className?: string
  threshold?: number
}

export function GlassmorphismHeader({ 
  children, 
  className, 
  threshold = 100 
}: GlassmorphismHeaderProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      const progress = Math.min(scrollY / threshold, 1)
      setScrollProgress(progress)
      setIsVisible(scrollY > threshold)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [threshold])

  const dynamicStyles = {
    background: `rgba(5, 7, 9, ${0.1 + scrollProgress * 0.3})`,
    backdropFilter: `blur(${4 + scrollProgress * 12}px) saturate(${100 + scrollProgress * 80}%)`,
    WebkitBackdropFilter: `blur(${4 + scrollProgress * 12}px) saturate(${100 + scrollProgress * 80}%)`,
    borderBottom: `1px solid rgba(255, 255, 255, ${0.05 + scrollProgress * 0.07})`,
    boxShadow: `0 ${4 + scrollProgress * 4}px ${16 + scrollProgress * 16}px rgba(0, 0, 0, ${0.05 + scrollProgress * 0.1})`,
  }

  return (
    <div
      className={cn(
        'fixed top-0 left-0 right-0 z-[999] transition-all ease-out',
        isVisible 
          ? 'opacity-100 translate-y-0 scale-100 header-animate-in duration-400 pointer-events-auto' 
          : 'opacity-0 -translate-y-5 scale-95 header-animate-out duration-150 pointer-events-none',
        className
      )}
      style={dynamicStyles}
    >
      <div 
        className={cn(
          'transition-all ease-out content-animate-in',
          isVisible 
            ? 'opacity-100 translate-y-0 duration-400 delay-75' 
            : 'opacity-0 translate-y-2 duration-150 delay-0'
        )}
      >
        {children}
      </div>
    </div>
  )
} 