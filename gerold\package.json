{"name": "gerold", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-oauth/google": "^0.12.1", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.10.22", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "glightbox": "^3.3.0", "gsap": "^3.12.5", "isotope-layout": "^3.0.6", "libphonenumber-js": "^1.12.6", "next": "^15.1.2", "nice-select2": "^2.2.0", "nodemailer": "^6.10.0", "passkit-generator": "^3.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-odometerjs": "^3.1.3", "swiper": "^11.1.15", "tailwind-merge": "^3.3.1", "typescript": "^5.8.3", "wow.js": "^1.2.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "tailwindcss": "^3.4.1"}}