"use client";
import { useEffect } from 'react';

export const GoogleAuthProvider = ({ children }) => {
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "************-j1vcd8tgl99hudfgpvlcl3l6c9qbdm54.apps.googleusercontent.com";
  
  useEffect(() => {
    // Load the Google API script
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client?hl=en';
    script.async = true;
    script.defer = true;
    document.body.appendChild(script);
    
    return () => {
      // Clean up script tag when component unmounts
      const scriptElement = document.querySelector('script[src="https://accounts.google.com/gsi/client?hl=en"]');
      if (scriptElement && document.body.contains(scriptElement)) {
        document.body.removeChild(scriptElement);
      }
    };
  }, []);
  
  return (
    <>{children}</>
  );
};

export default GoogleAuthProvider; 