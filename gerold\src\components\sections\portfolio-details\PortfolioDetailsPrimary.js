"use client";

import ButtonPrimary from "@/components/shared/buttons/ButtonPrimary";
import getAPortfolio from "@/libs/getAPortfolio";
import getPortfolio from "@/libs/getPortfolio";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState, useEffect, useRef } from "react";

// Обновляем функцию получения URL видео
const getVideoSourceUrl = (id) => {
  const localVideos = {
    1: "/videos/dental2.mp4",
    2: "/videos/farming.mp4",
    3: "/videos/atomic-short.mp4",
    4: "/videos/Solar3D.mp4",
    5: "/videos/kids-game.mp4",
    6: "/videos/3DNFT.mp4",
    7: "/videos/office2.mp4",
    8: "/videos/interior.mp4",
    9: "/videos/bb.mp4",
  };
  return localVideos[id] || null;
};

const PortfolioDetailsPrimary = () => {
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const videoRef = useRef(null);
  const videoErrorRef = useRef(false);

  const portfolios = getPortfolio();
  const params = useParams();
  const id = params?.id;
  const currentId = parseInt(id);
  const prevId = currentId > 1 ? currentId - 1 : 1;
  const totalPorfolio = portfolios?.length;
  const nextId = currentId < totalPorfolio ? currentId + 1 : totalPorfolio;
  const portfolio = getAPortfolio(currentId);
  const pervPortfolio = getAPortfolio(prevId);
  const nextPortfolio = getAPortfolio(nextId);
  const { title, title2, desc, desc1, desc2, img, descItems, statusItem, gallery } = portfolio || {};
  const isPrevProject = currentId > 1;
  const isNextProject = currentId < totalPorfolio;

  // Универсальная медиа-галерея (видео + фото)
  const videoSrc = getVideoSourceUrl(currentId);
  const media = [];
  if (videoSrc) {
    media.push({ type: 'video', src: videoSrc, poster: img });
  }
  if (Array.isArray(gallery) && gallery.length > 0) {
    gallery.forEach(src => media.push({ type: 'image', src }));
  } else if (img) {
    media.push({ type: 'image', src: img });
  }
  const [mainIndex, setMainIndex] = useState(0);
  useEffect(() => { setMainIndex(0); }, [currentId]); // сбрасываем при смене проекта
  const mainMedia = media[mainIndex];

  // Обработка стрелок на клавиатуре
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (media.length <= 1) return;
      if (e.key === 'ArrowLeft') {
        setMainIndex((prev) => (prev - 1 + media.length) % media.length);
      } else if (e.key === 'ArrowRight') {
        setMainIndex((prev) => (prev + 1) % media.length);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [media.length]);

  useEffect(() => {
    setIsClient(true);
    
    // Проверяем, является ли устройство мобильным
    const checkMobileDevice = () => {
      const userAgent = window.navigator.userAgent;
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    };
    
    // Проверяем, является ли устройство iOS
    const checkIOSDevice = () => {
      const userAgent = window.navigator.userAgent.toLowerCase();
      return /iphone|ipad|ipod/.test(userAgent);
    };

    setIsMobile(checkMobileDevice());
    setIsIOS(checkIOSDevice());
  }, []);

  // Обработчик ошибок видео
  const handleVideoError = () => {
    videoErrorRef.current = true;
    console.error("Video failed to load");
    
    // Если видео не загрузилось, попробуем загрузить его повторно через 2 секунды
    setTimeout(() => {
      if (videoRef.current) {
        videoRef.current.load();
      }
    }, 2000);
  };

  // Рендер главного медиа
  const renderMainMedia = () => {
    if (!mainMedia) return null;
    return (
      <div
        className="relative w-full"
        style={{
          aspectRatio: '16/9',
          background: '#181818',
          borderRadius: '16px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 0,
        }}
      >
        {mainMedia.type === 'video' ? (
          <video
            ref={videoRef}
            autoPlay={isIOS ? true : !isMobile}
            muted={isIOS ? true : !isMobile}
            playsInline
            controls
            controlsList="nodownload"
            preload="auto"
            style={{ width: '100%', height: '100%', objectFit: 'contain', background: '#181818' }}
            onError={handleVideoError}
            poster={mainMedia.poster || img || `/img/portfolio/portfolio-${currentId}.jpg`}
          >
            <source src={mainMedia.src} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : (
          <img
            src={mainMedia.src}
            alt="main gallery"
            style={{ width: '100%', height: '100%', objectFit: 'contain', background: '#181818' }}
          />
        )}
      </div>
    );
  };

  // Рендер миниатюр
  const renderThumbnails = () => {
    if (media.length <= 1) return null;
    return (
      <div className="flex gap-2 mt-4 justify-center flex-wrap">
        {media.map((item, idx) => (
          <div
            key={idx}
            onClick={() => setMainIndex(idx)}
            className={`cursor-pointer border-2 rounded-lg overflow-hidden ${mainIndex === idx ? 'border-primary-color' : 'border-transparent'}`}
            style={{
              width: 100,
              aspectRatio: '16/9',
              background: '#222',
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {item.type === 'video' ? (
              <>
                <video
                  src={item.src}
                  poster={item.poster || img}
                  muted
                  playsInline
                  style={{ width: '100%', height: '100%', objectFit: 'cover', display: 'block', background: '#222' }}
                />
                <span style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: 'white',
                  fontSize: 28,
                  pointerEvents: 'none',
                  textShadow: '0 0 8px #000',
                }}>
                  ▶
                </span>
              </>
            ) : (
              <img
                src={item.src}
                alt="thumb"
                style={{ width: '100%', height: '100%', objectFit: 'cover', display: 'block', background: '#222' }}
              />
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <section>
      <div className="py-60px md:py-20 lg:py-100px xl:py-30 dark:bg-black-color">
        <div className="container">
          {/* Portfolio */}
          <div className="group relative wow fadeInUp" data-wow-delay=".3s">
            <div className="relative overflow-hidden">
              <div className="overflow-hidden p-5 md:p-10 xl:p-60px pb-0 md:pb-0 xl:pb-0 bg-white dark:bg-black-color">
                {renderMainMedia()}
                {renderThumbnails()}
              </div>
              <div className="pt-30px md:pt-10 lg:pt-60px">
                <div className="transition-all duration-500">
                  <div className="relative z-10">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5 gap-x-50px items-start px-15px md:px-25px lg:px-10 mb-10 md:mb-50px">
                      <div>
                        <h3 className="mb-10px">
                          <span className="text-primary-color-light dark:text-white-color capitalize relative z-0 text-size-35 md:text-size-40 lg:text-size-45 font-bold">
                            {title}
                          </span>
                        </h3>
                        <p className="text-primary-color-light dark:text-white-color mb-5 md:mb-7">
                          {desc}
                        </p>
                        <div>
                          <ButtonPrimary className={"group/nested"} url={"/#contact"}>
                            live preview
                            <i className="fal fa-arrow-right ml-10px -rotate-45 group-hover/nested:rotate-0 transition-all duration-300"></i>
                          </ButtonPrimary>
                        </div>
                      </div>
                      <ul className="grid grid-cols-2 gap-x-15px lg:gap-x-5 gap-y-5 md:gap-y-30px">
                        {statusItem?.length
                          ? statusItem.map(({ title, desc }, idx) => (
                              <li key={idx}>
                                <p className="text-primary-color-light dark:text-white-color mb-1.5">
                                  {title}
                                </p>
                                <p className="text-primary-color-light dark:text-white-color font-medium mb-1.5">
                                  {desc}
                                </p>
                              </li>
                            ))
                          : ""}
                      </ul>
                    </div>
                    <div className="px-15px md:px-25px lg:px-10">
                      <div className="mb-10 md:mb-50px">
                        <div>
                          <h3 className="mb-15px md:mb-5">
                            <span className="text-primary-color-light dark:text-white-color capitalize relative z-0 text-size-35 md:text-size-40 lg:text-size-45 font-bold">
                              {title2}
                            </span>
                          </h3>
                          <p className="text-primary-color-light dark:text-white-color mb-15px md:mb-5">
                            {desc1}
                          </p>
                          <p className="text-primary-color-light dark:text-white-color mb-15px md:mb-5">
                            {desc2}
                          </p>
                        </div>
                      </div>
                      <div className="mb-10 md:mb-50px">
                        <div>
                          {descItems?.length
                            ? descItems.map(({ title, desc }, idx) => (
                                <div
                                  key={idx}
                                  className={`flex gap-15px xl:gap-x-50px flex-wrap ${
                                    descItems.length - 1 === idx ? "" : " mb-10 md:mb-50px"
                                  }`}
                                >
                                  <h4 className="max-w-265px w-full">
                                    <span className="text-primary-color-light dark:text-white-color uppercase relative z-0 text-xl font-bold">
                                      {title}
                                    </span>
                                  </h4>
                                  <p className="text-primary-color-light dark:text-white-color mb-15px md:mb-5 max-w-3xl w-full">
                                    {desc}
                                  </p>
                                </div>
                              ))
                            : ""}
                        </div>
                      </div>
                    </div>
                    <div className="px-15px py-30px md:px-25px lg:px-50px lg:py-35px bg-primary-color flex justify-between flex-wrap gap-30px items-center">
                      <Link
                        href={isPrevProject ? `/portfolio/${prevId}` : "#"}
                        className="flex gap-5 items-end group/nested"
                      >
                        {isPrevProject && (
                          <>
                            <span className="text-xl md:text-3xl text-white rotate-45 group-hover/nested:rotate-0 transition-all duration-500">
                              <i className="fal fa-arrow-left mb-2"></i>
                            </span>
                            <h6>
                              <span className="text-white-color block">Previous Project</span>
                              <span className="text-white-color capitalize relative z-0 text-size-25 md:text-size-35 lg:text-size-45 font-bold">
                                {pervPortfolio?.title}
                              </span>
                            </h6>
                          </>
                        )}
                      </Link>
                      <Link
                        href={isNextProject ? `/portfolio/${nextId}` : "#"}
                        className="flex gap-5 items-end group/nested ml-auto"
                      >
                        {isNextProject && (
                          <>
                            <h6>
                              <span className="text-white-color block">Next Project</span>
                              <span className="text-white-color capitalize relative z-0 text-size-25 md:text-size-35 lg:text-size-45 font-bold">
                                {nextPortfolio?.title}
                              </span>
                            </h6>
                            <span className="text-xl md:text-3xl text-white -rotate-45 group-hover/nested:rotate-0 transition-all duration-500">
                              <i className="fal fa-arrow-right mb-2"></i>
                            </span>
                          </>
                        )}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioDetailsPrimary;