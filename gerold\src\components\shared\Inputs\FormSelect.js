"use client";
import React, { useEffect } from "react";

const FormSelect = () => {
  useEffect(() => {
    import("../../../libs/nice-select2").then(({ default: NiceSelect }) => {
      new NiceSelect(document.getElementById("conService"));
      //   NiceSelect.bind(document.getElementById("conService"));
    });
  }, []);
  return (
    <select name="conService" id="conService" className="tj-nice-select">
      <option defaultValue={"Select an option"} disabled>
        Choose Service
      </option>
      <option defaultValue="vr">VR Development</option>
      <option defaultValue="mr-ar">MR / AR Development</option>
      <option defaultValue="interactive">3D App or Website</option>
      <option defaultValue="animation">Motion / 3D Animation</option>
      <option defaultValue="other">Other</option>
    </select>
  );
};

export default FormSelect;
