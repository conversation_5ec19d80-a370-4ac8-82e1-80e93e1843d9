"use client";

import { useEffect } from 'react';

export default function Interactive3D() {
  // Отключаем прокрутку страницы
  useEffect(() => {
    // Блокировка скролла 
    document.documentElement.style.overflow = 'hidden';
    document.documentElement.style.height = '100%';
    
    document.body.style.overflow = 'hidden';
    document.body.style.height = '100vh';
    document.body.style.width = '100vw';
    document.body.style.position = 'fixed';
    document.body.style.touchAction = 'none';
    
    // Блокировка событий скролла
    const preventDefault = (e) => e.preventDefault();
    document.addEventListener('wheel', preventDefault, { passive: false });
    document.addEventListener('touchmove', preventDefault, { passive: false });
    
    // Мета-тег для запрета масштабирования
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (viewportMeta) {
      viewportMeta.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
    }
    
    return () => {
      // Восстанавливаем нормальный скролл при размонтировании
      document.documentElement.style.overflow = '';
      document.documentElement.style.height = '';
      
      document.body.style.overflow = '';
      document.body.style.height = '';
      document.body.style.width = '';
      document.body.style.position = '';
      document.body.style.touchAction = '';
      
      document.removeEventListener('wheel', preventDefault);
      document.removeEventListener('touchmove', preventDefault);
      
      if (viewportMeta) {
        viewportMeta.content = 'width=device-width, initial-scale=1';
      }
    };
  }, []);

  return (
    <div className="h-screen w-screen">
      <iframe 
        allowFullScreen 
        src="https://xrplace.io/app/index.html" 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          border: 'none',
          overflow: 'hidden'
        }}
        title="XR Interactive Experience"
      />
    </div>
  );
} 