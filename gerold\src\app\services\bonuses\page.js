"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from "next/navigation";
import confetti from 'canvas-confetti';

// Обновляем бонусы с иконками в стиле изображения
const bonuses = [
  { id: 1, name: "<PERSON>ser<PERSON>", color: "#CF9E6F", textColor: "#E6C9A8", iconKey: "Dessert" },
  { id: 2, name: "Coffee", color: "#B67A45", textColor: "#E6C9A8", iconKey: "Coffee" },
  { id: 3, name: "Bowl", color: "#CF9E6F", textColor: "#E6C9A8", iconKey: "Bowl" },
  { id: 4, name: "Shopping", color: "#B67A45", textColor: "#E6C9A8", iconKey: "Shopping" },
  { id: 5, name: "Gift", color: "#CF9E6F", textColor: "#E6C9A8", iconKey: "Gift" },
  { id: 6, name: "Cup", color: "#B67A45", textColor: "#E6C9A8", iconKey: "Cup" },
  { id: 7, name: "Present", color: "#CF9E6F", textColor: "#E6C9A8", iconKey: "Present" },
  { id: 8, name: "Mug", color: "#B67A45", textColor: "#E6C9A8", iconKey: "Mug" },
];

// Функция для получения SVG иконок вынесена за пределы компонента
const getIconSVG = (name, color, size) => {
  const iconMap = {
    "Dessert": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 9H5l7 8zM3 11h18" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 3a2 2 0 0 0-2 2v4h4V5a2 2 0 0 0-2-2z" stroke="${color}" stroke-width="2"/>
                </svg>`,
    "Coffee": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                 <path d="M18 8h1a4 4 0 1 1 0 8h-1M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                 <path d="M6 1v3M10 1v3M14 1v3" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
               </svg>`,
    "Bowl": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
               <path d="M20 8H4M18 16.5V19M6 16.5V19" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
               <path d="M12 4a8 8 0 0 1 8 8 8 8 0 0 1-16 0 8 8 0 0 1 8-8z" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
             </svg>`,
    "Shopping": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4H6z" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                   <path d="M3 6h18M16 10a4 4 0 0 1-8 0" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                 </svg>`,
    "Gift": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 12v10H4V12M2 7h20v5H2V7zM12 22V7M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7zM12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
             </svg>`,
    "Cup": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
             <path d="M21 10h-8m-10 1v5a6 6 0 0 0 6 6h4a6 6 0 0 0 6-6v-1" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
             <path d="M17 10V8a4 4 0 0 0-4-4h-2a4 4 0 0 0-4 4v2" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
           </svg>`,
    "Present": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                 <path d="M20 12v10H4V12M2 7h20v5H2V7zM12 22V7M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7zM12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
               </svg>`,
    "Mug": `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
             <path d="M17 8h1a4 4 0 1 1 0 8h-1M3 8h14v9a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V8z" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
           </svg>`,
  };
  
  return iconMap[name] || '';
};

export default function BonusWheel() {
  const router = useRouter();
  const [spinning, setSpinning] = useState(false);
  const [result, setResult] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [wheelSize, setWheelSize] = useState(400);
  const [pixelRatio, setPixelRatio] = useState(1);
  const canvasRef = useRef(null);
  const iconImagesRef = useRef({}); // Хранилище для предзагруженных иконок
  const wheelRef = useRef({
    anglePerSegment: 360 / bonuses.length,
    currentRotation: 0,
    targetRotation: 0,
    spinTime: 0,
    spinTimeTotal: 0,
    startAngle: 0,
    selectedSegment: null,
    segmentPositions: null,
    pulseActive: false
  });
  
  const [arrowAngle, setArrowAngle] = useState(0);
  const [arrowVelocity, setArrowVelocity] = useState(0);
  const arrowAnimationRef = useRef(null);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 480) {
        setWheelSize(280);
      } else if (window.innerWidth < 768) {
        setWheelSize(320);
      } else {
        setWheelSize(400);
      }
      
      const dpr = typeof window !== 'undefined' ? 
                 window.devicePixelRatio || 1 : 1;
      setPixelRatio(dpr);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Add useEffect to draw the wheel when component mounts and preload icons
  useEffect(() => {
    const preloadIcons = async () => {
      const loadedIcons = {};
      const promises = bonuses.map(bonus => {
        return new Promise((resolve, reject) => {
          const iconSVG = getIconSVG(bonus.iconKey, '#E6C9A8', 24);
          if (!iconSVG) {
            console.warn(`SVG not found for iconKey: ${bonus.iconKey}`);
            resolve(); // Resolve even if SVG is missing to not block others
            return;
          }
          const img = new Image();
          const svgBlob = new Blob([iconSVG], {type: 'image/svg+xml'});
          const url = URL.createObjectURL(svgBlob);
          
          img.onload = () => {
            loadedIcons[bonus.iconKey] = img;
            URL.revokeObjectURL(url);
            resolve();
          };
          img.onerror = (err) => {
            console.error(`Error loading SVG icon for ${bonus.iconKey}:`, err);
            URL.revokeObjectURL(url);
            reject(err); // Or resolve() to proceed without the icon
          };
          img.src = url;
        });
      });
      
      try {
        await Promise.all(promises);
        iconImagesRef.current = loadedIcons;
        console.log("Icons preloaded:", loadedIcons);
        // Draw the wheel initially after icons are loaded
        if (canvasRef.current) {
          drawWheel();
        }
      } catch (error) {
        console.error("Error preloading icons:", error);
        // Optionally draw the wheel even if icons failed to load
        if (canvasRef.current) {
          drawWheel();
        }
      }
    };

    preloadIcons();

    // Make sure the canvas is ready before drawing
    // if (canvasRef.current) {
      // Draw wheel is now called after preloadIcons finishes
      // setTimeout(() => {
      //   drawWheel();
      // }, 100);
    // }
  }, [wheelSize, pixelRatio]); // Rerun if wheel size changes to potentially adjust icon size if needed

  // Add useEffect to draw the wheel when component mounts
  useEffect(() => {
    // Make sure the canvas is ready before drawing
    if (canvasRef.current) {
      // Small delay to ensure canvas is fully initialized
      setTimeout(() => {
        drawWheel();
      }, 100);
    }
  }, [wheelSize, pixelRatio]);

  const drawWheel = () => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d', { willReadFrequently: false, alpha: false });
      if (!ctx) {
        console.error('Canvas context not available');
        return;
      }
      
      const physicalWidth = wheelSize * pixelRatio;
      const physicalHeight = wheelSize * pixelRatio;
      
      canvas.width = physicalWidth;
      canvas.height = physicalHeight;
      
      canvas.style.width = `${wheelSize}px`;
      canvas.style.height = `${wheelSize}px`;
      
      ctx.scale(pixelRatio, pixelRatio);
      
      const radius = wheelSize / 2;
      
      // Очищаем холст
      ctx.fillStyle = 'rgba(242, 219, 193, 1)';
      ctx.fillRect(0, 0, wheelSize, wheelSize);
      
      // Внешний ободок колеса
      ctx.beginPath();
      ctx.arc(radius, radius, radius - 2, 0, 2 * Math.PI);
      ctx.fillStyle = '#B67A45';
      ctx.fill();
      
      // Внутренний круг колеса
      ctx.beginPath();
      ctx.arc(radius, radius, radius - 8, 0, 2 * Math.PI);
      ctx.fillStyle = '#E6C9A8';
      ctx.fill();
      
      const anglePerSegment = 360 / bonuses.length;
      const startAngle = wheelRef.current.startAngle;
      
      const segmentPositions = [];
      
      bonuses.forEach((bonus, index) => {
        try {
          const angle = index * anglePerSegment;
          const startRad = ((angle + startAngle) * Math.PI) / 180;
          const endRad = ((angle + anglePerSegment + startAngle) * Math.PI) / 180;
          
          const segmentPositionDegrees = (angle + startAngle + anglePerSegment/2) % 360;
          segmentPositions.push({
            index: index,
            bonusName: bonus.name,
            degrees: segmentPositionDegrees,
            isAtTopPosition: (segmentPositionDegrees >= 350 || segmentPositionDegrees <= 10)
          });
          
          ctx.beginPath();
          ctx.moveTo(radius, radius);
          ctx.arc(radius, radius, radius - 8, startRad, endRad);
          ctx.closePath();
          
          const baseColor = index % 2 === 0 ? "#CF9E6F" : "#B67A45";
          ctx.fillStyle = baseColor;
          ctx.fill();
          
          // Белые линии разделения сегментов
          ctx.beginPath();
          ctx.moveTo(radius, radius);
          ctx.lineTo(radius + Math.cos(startRad) * (radius - 8), radius + Math.sin(startRad) * (radius - 8));
          ctx.lineWidth = 2;
          ctx.strokeStyle = '#E6C9A8';
          ctx.stroke();
          
          // Рисуем иконки
          ctx.save();
          
          // Позиционируем иконку в середине сегмента
          const iconDistance = radius * 0.65;
          const iconX = radius + Math.cos((startRad + endRad) / 2) * iconDistance;
          const iconY = radius + Math.sin((startRad + endRad) / 2) * iconDistance;
          
          // Используем предзагруженные изображения
          const preloadedIcon = iconImagesRef.current[bonus.iconKey];
          
          if (preloadedIcon && preloadedIcon.complete) { // Check if image is loaded
            try {
              ctx.drawImage(preloadedIcon, iconX - 12, iconY - 12, 24, 24);
            } catch (drawError) {
              console.error(`Error drawing preloaded icon ${bonus.iconKey}:`, drawError);
            }
          } else if (preloadedIcon) {
            // Optional: Handle cases where the image object exists but isn't loaded yet (should be rare with preload)
             console.warn(`Icon ${bonus.iconKey} accessed before fully loaded.`);
          } else {
             // Optional: Handle cases where the icon wasn't preloaded (e.g., due to error)
             console.warn(`Icon ${bonus.iconKey} not found in preloaded cache.`);
          }
          
          ctx.restore();
        } catch (error) {
          console.error(`Error drawing segment ${index}:`, error);
        }
      });
      
      wheelRef.current.segmentPositions = segmentPositions;
      
      // Центральный круг
      ctx.beginPath();
      ctx.arc(radius, radius, radius * 0.2, 0, 2 * Math.PI);
      ctx.fillStyle = '#B67A45';
      ctx.fill();
      
      // Белая окантовка центрального круга
      ctx.beginPath();
      ctx.arc(radius, radius, radius * 0.2, 0, 2 * Math.PI);
      ctx.lineWidth = 2;
      ctx.strokeStyle = '#E6C9A8';
      ctx.stroke();
    } catch (error) {
      console.error('Error in drawWheel:', error);
    }
  };
  
  const determineSegment = () => {
    if (!wheelRef.current.segmentPositions) return bonuses[0];
    
    // Ищем сегмент в верхней позиции (0 градусов)
    const topSegment = wheelRef.current.segmentPositions.find(s => s.isAtTopPosition);
    if (topSegment) {
      return bonuses[topSegment.index];
    }
    
    // Если не нашли точно в верхней позиции, берем ближайший к 0/360 градусам
    const closestSegment = wheelRef.current.segmentPositions.reduce((closest, segment) => {
      const distance = Math.min(Math.abs(segment.degrees - 0), Math.abs(segment.degrees - 360));
      if (distance < closest.distance) {
        return { index: segment.index, distance, name: segment.bonusName };
      }
      return closest;
    }, { index: 0, distance: 999, name: bonuses[0].name });
    
    return bonuses[closestSegment.index];
  };

  useEffect(() => {
    const animateArrow = () => {
      if (spinning) {
        // Для центральной стрелки делаем более плавное вращение
        const spinProgress = wheelRef.current.spinTime / wheelRef.current.spinTimeTotal;
        // Теперь для центральной стрелки немного меньшая амплитуда 
        const oscillationFrequency = (1 - spinProgress) * 15 + 5;
        const oscillationAmplitude = (1 - spinProgress) * 2 + 0.5;
        
        const oscillation = Math.sin(Date.now() / oscillationFrequency) * oscillationAmplitude;
        setArrowAngle(oscillation);
        arrowAnimationRef.current = requestAnimationFrame(animateArrow);
      } else if (showResult) {
        // Физика "выщелкивания" стрелки, как на настоящих колесах
        const targetAngle = 0;
        const currentAngle = arrowAngle;
        const spring = 0.3;
        const damping = 0.75;
        
        let newVelocity = arrowVelocity + (targetAngle - currentAngle) * spring;
        newVelocity *= damping;
        
        const newAngle = currentAngle + newVelocity;
        
        setArrowAngle(newAngle);
        setArrowVelocity(newVelocity);
        
        if (Math.abs(newVelocity) > 0.01 || Math.abs(newAngle) > 0.01) {
          arrowAnimationRef.current = requestAnimationFrame(animateArrow);
        } else {
          setArrowAngle(0);
          setArrowVelocity(0);
        }
      } else {
        // Для центральной стрелки не нужно покачивание в покое
        setArrowAngle(0);
      }
    };
    
    arrowAnimationRef.current = requestAnimationFrame(animateArrow);
    
    return () => {
      if (arrowAnimationRef.current) {
        cancelAnimationFrame(arrowAnimationRef.current);
      }
    };
  }, [spinning, showResult, arrowAngle, arrowVelocity]);

  const spinWheel = () => {
    if (spinning) return;
    
    try {
      setSpinning(true);
      setResult(null);
      setShowResult(false);
      
      const selectedSegment = Math.floor(Math.random() * bonuses.length);
      const selectedBonus = bonuses[selectedSegment];
      
      wheelRef.current.selectedBonus = selectedBonus;
      
      const anglePerSegment = 360 / bonuses.length;
      
      const targetSegmentMiddleAngle = (selectedSegment + 0.5) * anglePerSegment;
      const desiredFinalWheelAngle = (270 - targetSegmentMiddleAngle + 3600) % 360;

      const currentAngle = wheelRef.current.currentRotation || 0;
      const fullRotations = 360 * (5 + Math.floor(Math.random() * 3));

      const requiredDelta = (desiredFinalWheelAngle - (currentAngle % 360) + 360) % 360;
      const targetRotation = fullRotations + requiredDelta;

      wheelRef.current = {
        ...wheelRef.current,
        targetRotation: targetRotation,
        spinTime: 0,
        spinTimeTotal: 3000 + Math.random() * 1000,
        startAngle: currentAngle,
        selectedSegment: selectedSegment,
        selectedBonus: selectedBonus
      };
      
      console.log(`Selected segment: #${selectedSegment+1} - ${selectedBonus.name}. Target angle: ${targetRotation.toFixed(2)}, Final wheel angle: ${desiredFinalWheelAngle.toFixed(2)}`);
      
      rotateWheel();
    } catch (error) {
      console.error('Error spinning wheel:', error);
      setSpinning(false);
    }
  };

  const rotateWheel = () => {
    try {
      wheelRef.current.spinTime += 30;
      
      if (wheelRef.current.spinTime >= wheelRef.current.spinTimeTotal) {
        stopWheel();
        return;
      }
      
      const spinAngle = easeOut(
        wheelRef.current.spinTime,
        0,
        wheelRef.current.targetRotation,
        wheelRef.current.spinTimeTotal
      );
      
      wheelRef.current.startAngle = wheelRef.current.currentRotation + spinAngle;
      
      drawWheel();
      requestAnimationFrame(rotateWheel);
    } catch (error) {
      console.error('Error rotating wheel:', error);
      setSpinning(false);
    }
  };

  const stopWheel = () => {
    try {
      const finalBonus = wheelRef.current.selectedBonus;
      if (!finalBonus) {
          console.error("No bonus was selected before stopping.");
          setSpinning(false);
          setResult(bonuses[0]);
          setShowResult(true);
          return;
      }
      const finalBonusIndex = bonuses.findIndex(b => b.id === finalBonus.id);
       if (finalBonusIndex === -1) {
          console.error("Selected bonus not found in bonuses list:", finalBonus);
          setSpinning(false);
          setResult(bonuses[0]);
          setShowResult(true);
          return;
      }

      const finalAngleFromAnimation = wheelRef.current.startAngle % 360;
      wheelRef.current.currentRotation = finalAngleFromAnimation;

      setSpinning(false);
      setResult(finalBonus);

      console.log(`Wheel stopped naturally. Pre-selected prize: ${finalBonus.name} (Index: ${finalBonusIndex}). Final angle ~ ${finalAngleFromAnimation.toFixed(2)}`);

      setShowResult(true);

      triggerConfetti();

      const pulseEffect = () => {
        try {
          const canvas = canvasRef.current;
          if (!canvas || !wheelRef.current.pulseActive) return;

          const ctx = canvas.getContext('2d');
          if (!ctx) return;
          const radius = wheelSize / 2;

          drawWheel();

          ctx.save();
          const glowAlpha = Math.abs(Math.sin(Date.now() / 250)) * 0.20 + 0.05;
          ctx.globalAlpha = glowAlpha;
          ctx.beginPath();
          ctx.arc(radius, radius, radius - 5, 0, 2 * Math.PI);
          ctx.fillStyle = 'rgba(217, 119, 6, 0.8)';
          ctx.shadowColor = 'rgba(217, 119, 6, 1)';
          ctx.shadowBlur = 15;
          ctx.fill();
          ctx.restore();

          requestAnimationFrame(pulseEffect);

        } catch (error) {
          console.error('Error in pulse effect:', error);
          wheelRef.current.pulseActive = false;
        }
      };

      wheelRef.current.pulseActive = true;
      try {
        requestAnimationFrame(pulseEffect);
      } catch (error) {
        console.error('Error starting pulse effect:', error);
        wheelRef.current.pulseActive = false;
      }
    } catch (error) {
      console.error('Error stopping wheel:', error);
      setSpinning(false);
    }
  };

  useEffect(() => {
    if (!showResult) {
      wheelRef.current.pulseActive = false;

      const redrawTimeout = setTimeout(() => {
        if (canvasRef.current && !spinning && !showResult) {
            drawWheel();
        }
      }, 50);

      return () => clearTimeout(redrawTimeout);
    }
  }, [showResult]);

  const triggerConfetti = () => {
    try {
      const myCanvas = document.createElement('canvas');
      myCanvas.style.position = 'fixed';
      myCanvas.style.top = '0';
      myCanvas.style.left = '0';
      myCanvas.style.width = '100vw';
      myCanvas.style.height = '100vh';
      myCanvas.style.zIndex = '1000';
      myCanvas.style.pointerEvents = 'none';
      document.body.appendChild(myCanvas);
      
      const myConfetti = confetti.create(myCanvas, { resize: true });
      
      const isMobile = window.innerWidth <= 768;
      
      const particleCount = isMobile ? 30 : 70;
      const spread = isMobile ? 40 : 55;
      const startVelocity = isMobile ? 20 : 30;
      
      // Обновленные цвета конфетти в стиле страницы
      const colors = [
        '#B67A45', // Темно-коричневый (как ободок колеса)
        '#CF9E6F', // Светло-коричневый (как сегмент колеса)
        '#E6C9A8', // Бежевый (как фон сегментов/линии)
        '#D4B18E', // Средний бежевый 
        '#FBBF24'  // Золотистый/Янтарный акцент
      ];
      
      const duration = 3 * 1000;
      const end = Date.now() + duration;
      
      const interval = isMobile ? 150 : 100;
      
      (function frame() {
        myConfetti({
          particleCount: Math.ceil(particleCount / 2),
          angle: 60,
          spread: spread,
          origin: { x: 0, y: 0.5 },
          colors: colors,
          startVelocity: startVelocity,
          gravity: 1,
          shapes: ['circle', 'square'],
          scalar: isMobile ? 0.7 : 1
        });
        
        myConfetti({
          particleCount: Math.ceil(particleCount / 2),
          angle: 120,
          spread: spread,
          origin: { x: 1, y: 0.5 },
          colors: colors,
          startVelocity: startVelocity,
          gravity: 1,
          shapes: ['circle', 'square'],
          scalar: isMobile ? 0.7 : 1
        });
        
        if (Date.now() < end) {
          setTimeout(frame, interval);
        } else {
          setTimeout(() => {
            if (document.body.contains(myCanvas)) {
              document.body.removeChild(myCanvas);
            }
          }, 1500);
        }
      }());
    } catch (error) {
      console.error('Error triggering confetti:', error);
    }
  };

  const easeOut = (t, b, c, d) => {
    const ts = (t /= d) * t;
    const tc = ts * t;
    return b + c * (tc + -3 * ts + 3 * t);
  };

  return (
    <div className="bg-amber-50 min-h-screen flex items-start pt-20 md:pt-32" style={{backgroundImage: 'url(/images/back.jpg)', backgroundSize: 'cover', backgroundPosition: 'center'}}>
      <div className="container mx-auto text-center px-4 pb-20">
        
        <h1 className="text-2xl md:text-3xl font-bold mb-4 text-amber-900">Rewards for your visit</h1>
        <p className="text-sm md:text-base mb-8 max-w-xl mx-auto text-amber-800">
          Rotate the wheel to get a special reward for your next experience
        </p>
        
        <div className="flex flex-col items-center justify-center mb-12 md:mb-16">
          <div className="relative mb-6">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-amber-700/30 via-amber-600/25 to-amber-700/30 blur-md"></div>
            <div className="absolute inset-2 rounded-full bg-gradient-to-b from-amber-500/10 to-transparent blur-sm"></div>
            
            <div 
              className={`relative rounded-full p-3 bg-gradient-to-b from-amber-100 to-amber-200 shadow-[0_0_30px_rgba(146,64,14,0.3)] ${spinning ? 'wheel-spinning' : 'hover:shadow-[0_0_35px_rgba(146,64,14,0.4)]'} transition-shadow duration-300`}
              style={{ willChange: spinning ? 'box-shadow' : 'auto' }}
            >
              <div className="bg-gradient-to-b from-amber-50/90 to-amber-100/90 rounded-full overflow-hidden relative shadow-inner">
                <canvas 
                  ref={canvasRef} 
                  className="mx-auto rounded-full relative z-10 transition-transform"
                  style={{ willChange: spinning ? 'transform' : 'auto' }}
                />
                
                {/* Центральная стрелка-указатель */}
                <div 
                  className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20 transform-gpu"
                  style={{ 
                    transform: `translate(-50%, -50%) rotate(${spinning ? arrowAngle : 0}deg)`,
                    transformOrigin: 'center',
                    willChange: 'transform',
                    width: `${wheelSize < 320 ? '48%' : '42%'}`,
                    height: `${wheelSize < 320 ? '48%' : '42%'}`
                  }}
                >
                  {/* SVG для центральной стрелки */}
                  <svg width="100%" height="100%" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    {/* Центральный круг - центр колеса уже есть в canvas, этот элемент скрываем */}
                    <circle 
                      cx="50" 
                      cy="50" 
                      r="18" 
                      fill="none" 
                      stroke="none"
                    />
                    
                    {/* Стрелка указатель */}
                    <path 
                      d="M50 20L56 50H44L50 20Z" 
                      fill="#E6C9A8"
                      stroke="#B67A45"
                      strokeWidth="1"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          
          <button 
            onClick={spinWheel} 
            disabled={spinning}
            className={`
              px-7 md:px-9 py-3 md:py-4 rounded-full text-white font-semibold text-base md:text-lg uppercase tracking-wider
              ${spinning 
                ? 'bg-gray-500 cursor-not-allowed opacity-70' 
                : 'bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 hover:scale-105 hover:shadow-[0_0_15px_rgba(217,119,6,0.4)]'
              }
              transition-all duration-300 shadow-lg shadow-amber-900/30
              relative overflow-hidden
            `}
          >
            {spinning ? 'Rotating...' : 'ROTATE'}
            <div className="absolute inset-0 overflow-hidden rounded-full">
              <div className="absolute top-0 -inset-x-1/2 w-[200%] h-[40%] bg-gradient-to-b from-white/15 to-transparent"></div>
            </div>
          </button>
        </div>
        
        {showResult && (
          <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
            <div className="absolute inset-0 bg-black/50" onClick={() => setShowResult(false)}></div>
            <div className="relative max-w-sm w-full bg-gradient-to-b from-amber-100 to-amber-50 rounded-lg p-6 shadow-xl animate-pop-in">
              <div className="absolute top-0 left-0 right-0 h-1 bg-amber-600"></div>
              <div className="py-3">
                <h2 className="text-xl md:text-2xl font-bold mb-3 text-amber-900">
                  Your Reward
                </h2>
                <div className="text-4xl mb-3">
                  {result && result.icon}
                </div>
                <p className="text-lg md:text-xl mb-2 font-medium text-amber-800">
                  {result && result.name}
                </p>
                <p className="text-xs md:text-sm text-amber-700 mb-4">
                  Show this screen to redeem your reward.<br/>Valid for 30 days.
                </p>
                <div className="mt-4">
                  <button 
                    onClick={() => router.push('/services/loyalty')}
                    className="px-6 py-2 rounded-md bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-white font-medium transition-all duration-300 w-full"
                  >
                    Claim Reward
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div className="mt-12 md:mt-16 max-w-2xl mx-auto text-left">
          <h2 className="text-lg md:text-xl font-bold mb-4 text-white text-center">How It Works</h2>
          <div className="bg-amber-50/90 rounded-xl p-6 md:p-8 shadow-md">
            <ol className="space-y-3 text-sm md:text-base text-amber-800">
              <li className="flex gap-2">
                <span className="font-medium text-amber-800">1.</span>
                <span>Rotate the wheel for a chance to win one of our exclusive rewards.</span>
              </li>
              <li className="flex gap-2">
                <span className="font-medium text-amber-800">2.</span>
                <span>Show the result to redeem your prize during your visit.</span>
              </li>
              <li className="flex gap-2">
                <span className="font-medium text-amber-800">3.</span>
                <span>Each reward is valid for 30 days from the date of winning.</span>
              </li>
            </ol>
          </div>
        </div>
        
        <div className="mt-12 md:mt-16 pt-6 border-t border-amber-300/50">
          <a 
            href="https://d2d.studio" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-amber-800/70 hover:text-amber-700 transition-colors text-xs"
          >
            Made by D2D Studio
          </a>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes pop-in {
          0% { transform: scale(0.9); opacity: 0; }
          70% { transform: scale(1.02); opacity: 1; }
          100% { transform: scale(1); opacity: 1; }
        }
        
        @keyframes spin-glow {
          0% { box-shadow: 0 0 20px rgba(217, 119, 6, 0.3); }
          50% { box-shadow: 0 0 30px rgba(217, 119, 6, 0.6); }
          100% { box-shadow: 0 0 20px rgba(217, 119, 6, 0.3); }
        }
        
        .animate-pop-in {
          animation: pop-in 0.4s ease-out forwards;
        }
        
        .wheel-spinning {
          animation: spin-glow 1.2s infinite;
        }
      `}</style>
    </div>
  );
} 