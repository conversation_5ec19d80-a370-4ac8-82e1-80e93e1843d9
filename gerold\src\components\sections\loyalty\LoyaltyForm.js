"use client";
import { useState, useEffect, useRef } from "react";
import GoogleAuthProvider from "@/components/providers/GoogleAuthProvider";
import { AsYouType, isValidPhoneNumber } from 'libphonenumber-js';

const LoyaltyForm = () => {
  const [registrationType, setRegistrationType] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [userName, setUserName] = useState("");
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [showVirtualCard, setShowVirtualCard] = useState(false);
  const [cardQrValue, setCardQrValue] = useState("");
  const googleButtonRef = useRef(null);
  
  // Блокировка скролла и зума на мобильных устройствах
  useEffect(() => {
    // Определяем, является ли устройство мобильным
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
    
    // Полная блокировка скролла на всех устройствах
    // Для HTML
    document.documentElement.style.overflow = 'hidden';
    document.documentElement.style.height = '100%';
    
    // Для body
    document.body.style.overflow = 'hidden';
    document.body.style.height = '100vh';
    document.body.style.width = '100vw';
    document.body.style.position = 'fixed';
    document.body.style.touchAction = 'none';
    
    // Блокировка событий скролла
    const preventDefault = (e) => e.preventDefault();
    document.addEventListener('wheel', preventDefault, { passive: false });
    document.addEventListener('touchmove', preventDefault, { passive: false });
    
    const style = document.createElement('style');
    style.innerHTML = `
      input, textarea {
        font-size: 16px !important;
      }
      html {
        touch-action: none;
        overflow: hidden;
        height: 100%;
      }
      body {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%;
        touch-action: none;
      }
    `;
    document.head.appendChild(style);
    
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (viewportMeta) {
      viewportMeta.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
    } else {
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
      document.head.appendChild(meta);
    }
    
    return () => {
      // Восстанавливаем нормальный скролл при размонтировании
      document.documentElement.style.overflow = '';
      document.documentElement.style.height = '';
      
      document.body.style.overflow = '';
      document.body.style.height = '';
      document.body.style.width = '';
      document.body.style.position = '';
      document.body.style.touchAction = '';
      
      document.removeEventListener('wheel', preventDefault);
      document.removeEventListener('touchmove', preventDefault);
      
      const viewportMeta = document.querySelector('meta[name="viewport"]');
      if (viewportMeta) {
        viewportMeta.content = 'width=device-width, initial-scale=1';
      }
    };
  }, []);
  
  // Загружаем html2canvas для сохранения карты как изображения
  useEffect(() => {
    // Только загружаем скрипт если он еще не загружен
    if (!window.html2canvas && registrationComplete) {
      const script = document.createElement('script');
      script.src = 'https://html2canvas.hertzen.com/dist/html2canvas.min.js';
      script.async = true;
      document.body.appendChild(script);
      
      return () => {
        if (document.body.contains(script)) {
          document.body.removeChild(script);
        }
      };
    }
  }, [registrationComplete]);
  
  useEffect(() => {
    const initializeGoogleSignIn = () => {
      if (window.google && googleButtonRef.current) {
        try {
          window.google.accounts.id.initialize({
            client_id: "************-j1vcd8tgl99hudfgpvlcl3l6c9qbdm54.apps.googleusercontent.com",
            callback: handleGoogleSignIn,
            auto_select: false,
            cancel_on_tap_outside: true,
          });

          window.google.accounts.id.renderButton(
            googleButtonRef.current,
            { 
              type: 'standard',
              shape: 'pill',
              theme: 'filled_blue',
              size: 'large',
              logo_alignment: 'center',
              locale: 'en',
              text: 'signin_with',
              width: window.innerWidth < 480 ? 240 : 320,
              ux_mode: 'popup',
              useOneTap: false
            }
          );
          
        } catch (error) {
          console.error('Error initializing Google Sign-In:', error);
        }
      } else {
        setTimeout(initializeGoogleSignIn, 300);
      }
    };

    const timer = setTimeout(initializeGoogleSignIn, 500);
    const handleResize = () => {
      // Перерисовка кнопки Google при изменении размера окна
      clearTimeout(timer);
      setTimeout(initializeGoogleSignIn, 300);
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleGoogleSignIn = (response) => {
    console.log("Google login successful:", response);
    
    if (response && response.credential) {
      try {
        // Декодируем JWT токен от Google
        const base64Url = response.credential.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        // Правильно декодируем Base64, обрабатывая UTF-8 символы
        const jsonPayload = decodeURIComponent(
          atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join('')
        );
        
        const payload = JSON.parse(jsonPayload);
        console.log("User info:", payload);
        
        // Используем имя из данных Google
        setUserName(payload.name || "");
      } catch (error) {
        console.error("Error decoding JWT:", error);
      }
    }
    
    setRegistrationType("google");
    setRegistrationComplete(true);
  };


  const handlePhoneChange = (e) => {
    const input = e.target.value;
    
    const formatter = new AsYouType();
    const formatted = formatter.input(input);
    
    setPhoneNumber(formatted);
  };

  const handlePhoneSubmit = (e) => {
    e.preventDefault();
    
    if (userName && phoneNumber) {
      let isValid = false;
      
      try {
        isValid = isValidPhoneNumber(phoneNumber) || phoneNumber.length >= 7;
      } catch (error) {
        isValid = phoneNumber.length >= 7; 
      }
      
      if (isValid) {
        console.log("Phone registration:", { userName, phoneNumber });
        setRegistrationType("phone");
        setRegistrationComplete(true);
      } else {
        alert("Please enter a valid phone number");
      }
    }
  };

  const handleGetLoyaltyCard = async () => {
    try {
      const userInfo = {
        name: userName,
        registrationType: registrationType,
        timestamp: new Date().toISOString()
      };
      
      const cardId = `${userName.replace(/\s+/g, '').toLowerCase()}-${Date.now()}`;
      setCardQrValue(`https://d2d.media/loyalty/card/${cardId}`);
      
      setShowVirtualCard(true);
      
      /* Заглушено

      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      
      if (isIOS) {
        const response = await fetch('/api/loyalty/generate-pass', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(userInfo),
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate pass');
        }
        
        const passData = await response.blob();
        
        const url = window.URL.createObjectURL(passData);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'd2d_loyalty.pkpass';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        setShowVirtualCard(true);
      }
      */
    } catch (error) {
      console.error('Error getting loyalty card:', error);
      alert('Something went wrong. Please try again later.');
    }
  };

  const closeVirtualCard = () => {
    setShowVirtualCard(false);
  };

  // Функция сохранения карты как изображения
  const saveCardAsImage = () => {
    try {
      // Создаем временный div для красивой карты
      const tempCard = document.createElement('div');
      tempCard.id = 'temp-card-for-saving';
      tempCard.style.width = '800px';
      tempCard.style.height = '460px';
      tempCard.style.position = 'fixed';
      tempCard.style.top = '-9999px';
      tempCard.style.left = '-9999px';
      tempCard.style.zIndex = '-1000';
      tempCard.style.backgroundColor = '#1a2540';
      tempCard.style.borderRadius = '12px';
      tempCard.style.overflow = 'hidden';
      tempCard.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.5)';
      tempCard.style.fontFamily = 'Arial, sans-serif';
      
      // Генерируем текущий год
      const currentYear = new Date().getFullYear();
      
      // Создаем верхнюю полоску
      const topBar = document.createElement('div');
      topBar.style.width = '100%';
      topBar.style.height = '50px';
      topBar.style.backgroundColor = '#1e2b4a';
      topBar.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
      topBar.style.position = 'absolute';
      topBar.style.top = '0';
      topBar.style.left = '0';
      
      // Создаем заголовок D2D LOYALTY
      const title = document.createElement('div');
      title.style.position = 'absolute';
      title.style.top = '70px';
      title.style.left = '70px';
      title.style.color = 'white';
      title.style.fontWeight = 'bold';
      title.style.fontSize = '50px';
      title.style.letterSpacing = '1px';
      title.innerText = 'D2D LOYALTY';
      
      // QR-код
      const qrContainer = document.createElement('div');
      qrContainer.style.position = 'absolute';
      qrContainer.style.top = '50%';
      qrContainer.style.right = '70px';
      qrContainer.style.transform = 'translateY(-50%)';
      qrContainer.style.backgroundColor = 'white';
      qrContainer.style.padding = '20px';
      qrContainer.style.borderRadius = '10px';
      
      const qrImage = document.createElement('img');
      qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=220x220&data=${encodeURIComponent(cardQrValue)}`;
      qrImage.width = 220;
      qrImage.height = 220;
      qrImage.style.display = 'block';
      qrContainer.appendChild(qrImage);
      
      // Label MEMBER NAME
      const memberLabel = document.createElement('div');
      memberLabel.style.position = 'absolute';
      memberLabel.style.top = '160px';
      memberLabel.style.left = '70px';
      memberLabel.style.color = '#8d97af';
      memberLabel.style.fontSize = '26px';
      memberLabel.style.letterSpacing = '1px';
      memberLabel.innerText = 'MEMBER NAME';
      
      // Member Name Value
      const memberName = document.createElement('div');
      memberName.style.position = 'absolute';
      memberName.style.top = '200px';
      memberName.style.left = '70px';
      memberName.style.color = 'white';
      memberName.style.fontSize = '52px';
      memberName.style.fontWeight = '500';
      memberName.innerText = userName;
      
      // LEVEL label
      const levelLabel = document.createElement('div');
      levelLabel.style.position = 'absolute';
      levelLabel.style.top = '300px';
      levelLabel.style.left = '70px';
      levelLabel.style.color = '#8d97af';
      levelLabel.style.fontSize = '26px';
      levelLabel.style.letterSpacing = '1px';
      levelLabel.innerText = 'LEVEL';
      
      // LEVEL value
      const levelValue = document.createElement('div');
      levelValue.style.position = 'absolute';
      levelValue.style.top = '340px';
      levelValue.style.left = '70px';
      levelValue.style.color = 'white';
      levelValue.style.fontSize = '48px';
      levelValue.style.fontWeight = '500';
      levelValue.innerText = 'Gold';
      
      // SINCE label
      const sinceLabel = document.createElement('div');
      sinceLabel.style.position = 'absolute';
      sinceLabel.style.top = '300px';
      sinceLabel.style.left = '250px';
      sinceLabel.style.color = '#8d97af';
      sinceLabel.style.fontSize = '26px';
      sinceLabel.style.letterSpacing = '1px';
      sinceLabel.innerText = 'SINCE';
      
      // SINCE value
      const sinceValue = document.createElement('div');
      sinceValue.style.position = 'absolute';
      sinceValue.style.top = '340px';
      sinceValue.style.left = '250px';
      sinceValue.style.color = 'white';
      sinceValue.style.fontSize = '48px';
      sinceValue.style.fontWeight = '500';
      sinceValue.innerText = currentYear;
      
      // Card number
      const cardNumber = document.createElement('div');
      cardNumber.style.position = 'absolute';
      cardNumber.style.top = '70px';
      cardNumber.style.right = '70px';
      cardNumber.style.color = 'rgba(255, 255, 255, 0.7)';
      cardNumber.style.fontFamily = 'monospace';
      cardNumber.style.fontSize = '16px';
      const shortId = cardQrValue.substring(cardQrValue.lastIndexOf('/') + 1, cardQrValue.lastIndexOf('/') + 13);
      cardNumber.innerText = `#${shortId}`;
      
      // Made by D2D Studio
      const madeBy = document.createElement('div');
      madeBy.style.position = 'absolute';
      madeBy.style.bottom = '15px';
      madeBy.style.right = '20px';
      madeBy.style.color = 'rgba(255, 255, 255, 0.5)';
      madeBy.style.fontSize = '14px';
      madeBy.style.fontStyle = 'italic';
      madeBy.innerText = 'Made by D2D Studio — d2d.studio';
      
      // Добавляем все элементы в карту
      tempCard.appendChild(topBar);
      tempCard.appendChild(title);
      tempCard.appendChild(qrContainer);
      tempCard.appendChild(memberLabel);
      tempCard.appendChild(memberName);
      tempCard.appendChild(levelLabel);
      tempCard.appendChild(levelValue);
      tempCard.appendChild(sinceLabel);
      tempCard.appendChild(sinceValue);
      tempCard.appendChild(cardNumber);
      tempCard.appendChild(madeBy);
      
      // Добавляем элемент на страницу
      document.body.appendChild(tempCard);
      
      // После создания элемента, делаем screenshot
      setTimeout(() => {
        if (window.html2canvas) {
          const options = {
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#1a2540',
            scale: 1,
            logging: false,
            windowWidth: 1200,
            windowHeight: 800,
          };
          
          html2canvas(tempCard, options).then(canvas => {
            try {
              // Сохраняем canvas как изображение
              const link = document.createElement('a');
              link.download = 'd2d_loyalty_card.png';
              link.href = canvas.toDataURL('image/png');
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              
              // Удаляем временный элемент
              document.body.removeChild(tempCard);
            } catch (error) {
              console.error('Error saving image:', error);
              alert('Could not save image. Please take a screenshot instead.');
              document.body.removeChild(tempCard);
            }
          }).catch(err => {
            console.error('html2canvas error:', err);
            alert('Error creating image. Please take a screenshot instead.');
            document.body.removeChild(tempCard);
          });
        } else {
          alert('Function to save card is not available. Make a screenshot.');
          document.body.removeChild(tempCard);
        }
      }, 800); // Увеличиваем таймаут для гарантии полной загрузки
      
    } catch (error) {
      console.error('Error creating card for saving:', error);
      alert('Could not create card image. Please take a screenshot instead.');
    }
  };

  return (
    <div className="bg-[#0e1424] fixed inset-0 w-full h-full flex items-center justify-center overflow-hidden">
      <div className="absolute top-6 left-1/2 -translate-x-1/2 bg-white/15 backdrop-blur-md text-white px-6 py-3 rounded-xl shadow-lg z-20">
        <img src="/logo.png" alt="Logo" className="h-12 sm:h-16" />
      </div>
      <div className="absolute inset-0 bg-gradient-to-b from-[#1e2b4a]/50 to-[#0e1424] opacity-80"></div>
      <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-xl"></div>
      <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-indigo-500/10 rounded-full blur-xl"></div>
      
      {/* Card content */}
      <div className="relative z-10 w-full max-w-[90%] sm:max-w-md mx-auto px-4 sm:px-6 py-4 sm:py-8 flex flex-col">
        <h2 className="text-xl sm:text-3xl mb-4 sm:mb-8 font-bold text-white text-center">
          Loyalty Card<br />Registration
        </h2>

        {!registrationComplete ? (
          <div className="space-y-4 sm:space-y-6 w-full">
            <form onSubmit={handlePhoneSubmit} className="space-y-3 sm:space-y-6">
              <div className="space-y-1 sm:space-y-2">
                <label className="block text-indigo-300 text-sm font-medium pl-1">
                  Name
                </label>
                <input
                  type="text"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Enter your name"
                  className="w-full px-3 sm:px-4 py-2.5 sm:py-3.5 bg-[#1a2644] border border-[#2a375a] text-white rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 transition-all duration-300 placeholder:text-gray-500 text-sm sm:text-base"
                  required
                  style={{ fontSize: '16px' }}
                />
              </div>

              <div className="space-y-1 sm:space-y-2">
                <label className="block text-indigo-300 text-sm font-medium pl-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={phoneNumber}
                  onChange={handlePhoneChange}
                  placeholder="Enter your phone number"
                  className="w-full px-3 sm:px-4 py-2.5 sm:py-3.5 bg-[#1a2644] border border-[#2a375a] text-white rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 transition-all duration-300 placeholder:text-gray-500 text-sm sm:text-base"
                  inputMode="numeric"
                  required
                  style={{ fontSize: '16px' }}
                />
              </div>

              <button
                type="submit"
                className="w-full py-2.5 sm:py-3.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-lg text-white font-medium transition-all duration-300 shadow-lg shadow-indigo-900/30 text-sm sm:text-base"
              >
                Register with Phone
              </button>
            </form>

            <div className="flex items-center my-2 sm:my-4">
              <div className="flex-1 h-px bg-[#2a375a]"></div>
              <p className="mx-3 sm:mx-4 text-gray-400 text-xs sm:text-sm">OR</p>
              <div className="flex-1 h-px bg-[#2a375a]"></div>
            </div>

            <div className="flex justify-center">
              <GoogleAuthProvider>
                <div className="google-btn-wrapper w-full flex justify-center items-center">
                  <div ref={googleButtonRef} className="w-full max-w-[240px] sm:max-w-[320px]"></div>
                </div>
              </GoogleAuthProvider>
            </div>
          </div>
        ) : (
          <div className="text-center py-2 sm:py-4 space-y-3 sm:space-y-6">
            <div className="flex justify-center">
              <div className="w-14 h-14 sm:w-20 sm:h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center shadow-lg shadow-indigo-900/30">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="h-7 w-7 sm:h-10 sm:w-10 text-white" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M5 13l4 4L19 7" 
                  />
                </svg>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg sm:text-2xl mb-1 sm:mb-2 font-bold text-white">
                Registration Successful!
              </h3>
              <p className="mb-4 sm:mb-8 text-gray-400 text-xs sm:text-sm">
                Your loyalty card has been registered successfully.
              </p>
            </div>
            
            <button
              className="w-full py-2.5 sm:py-3.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-lg text-white font-medium transition-all duration-300 shadow-lg shadow-indigo-900/30 text-sm sm:text-base"
              onClick={handleGetLoyaltyCard}
            >
              Get Loyalty Card
            </button>
          </div>
        )}
      </div>

      {/* Footer с ссылкой - перемещен в самый низ страницы */}
      <div className="absolute bottom-5 left-0 right-0 z-10 text-center">
        <a 
          href="https://d2d.studio/" 
          target="_blank" 
          rel="noopener noreferrer" 
          className="text-gray-400 text-xs sm:text-sm hover:text-indigo-300 transition-colors duration-300"
        >
          Made by D2D Studio
        </a>
      </div>
      
      {/* Модальное окно виртуальной карты - улучшенный дизайн */}
      {showVirtualCard && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4">
          <div className="relative w-full max-w-md mx-auto bg-gradient-to-b from-[#1e2b4a] to-[#131c31] p-6 rounded-xl shadow-2xl" id="virtual-loyalty-card">
            <button 
              onClick={closeVirtualCard}
              className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center text-white bg-black/30 rounded-full hover:bg-black/40 transition-all duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
            
            <div className="flex justify-center mb-3">
              <img src="/logo.png" alt="D2D Studio" className="h-14" />
            </div>
            
            <h3 className="text-xl text-center font-bold text-white mb-5">Virtual Loyalty Card</h3>
            
            {/* Виртуальная карта в стиле сохраняемой */}
            <div className="mb-5 bg-gradient-to-b from-[#1e2c4a] to-[#0e1424] rounded-lg p-5 shadow-lg border border-[#2a375a]/30 relative overflow-hidden">
              {/* QR-код */}
              <div className="bg-white p-3 rounded-lg mb-3 mx-auto w-max">
                <img 
                  src={`https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=${encodeURIComponent(cardQrValue)}`} 
                  alt="Loyalty Card QR Code" 
                  className="w-[180px] h-[180px]"
                />
                <p className="text-center text-gray-800 text-xs mt-1">Scan this code in store</p>
              </div>
              
              {/* Информация пользователя */}
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div>
                  <p className="text-xs text-indigo-300 uppercase tracking-wider mb-1">Name</p>
                  <p className="font-medium text-lg text-white">{userName}</p>
                </div>
                <div>
                  <p className="text-xs text-indigo-300 uppercase tracking-wider mb-1">Member Level</p>
                  <p className="font-medium text-lg text-white">Gold</p>
                </div>
              </div>
              
              <div className="mt-3">
                <p className="text-xs text-indigo-300 uppercase tracking-wider mb-1">Card Number</p>
                <p className="font-mono text-white text-sm">{cardQrValue.substring(cardQrValue.lastIndexOf('/') + 1, cardQrValue.lastIndexOf('/') + 13)}</p>
              </div>
              
              {/* Декоративные элементы */}
              <div className="absolute top-3 right-3 w-20 h-20 bg-blue-500/5 rounded-full blur-xl pointer-events-none"></div>
              <div className="absolute bottom-3 left-3 w-20 h-20 bg-indigo-500/5 rounded-full blur-xl pointer-events-none"></div>
              
              {/* Подпись D2D Studio */}
              <div className="mt-5 text-right">
                <p className="text-gray-400 text-xs italic">Made by D2D Studio</p>
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={saveCardAsImage}
                className="flex-1 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-lg text-white text-sm font-medium transition-all duration-300 shadow-indigo-900/30"
              >
                Download Card
              </button>
              <button
                onClick={closeVirtualCard}
                className="flex-1 py-3 bg-gray-700/50 hover:bg-gray-700/70 rounded-lg text-white text-sm font-medium transition-all duration-300"
              >
                Close
              </button>
            </div>
            
            {/* Ссылка на D2D Studio в модальном окне */}
            <div className="mt-4 text-center">
              <a 
                href="https://d2d.studio/" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-gray-400 text-xs hover:text-indigo-300 transition-colors duration-300"
              >
                Visit d2d.studio
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LoyaltyForm;