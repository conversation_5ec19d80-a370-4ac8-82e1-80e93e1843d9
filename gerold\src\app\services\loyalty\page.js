"use client";
import LoyaltyForm from "@/components/sections/loyalty/LoyaltyForm";

const LoyaltyPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-[#0a1128] to-[#111b33] flex items-center justify-center py-20 px-4 relative overflow-hidden">
      {/* Futuristic background elements */}
      <div className="absolute w-full h-full">
        <div className="absolute top-0 left-0 w-full h-1/3 bg-blue-500 opacity-5 rounded-full blur-[100px]"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-purple-500 opacity-5 rounded-full blur-[100px]"></div>
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-cyan-500 opacity-5 rounded-full blur-[100px]"></div>
        
        {/* Grid lines */}
        <div className="w-full h-full opacity-10 bg-[linear-gradient(to_right,#20253a_1px,transparent_1px),linear-gradient(to_bottom,#20253a_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      </div>
      
      <LoyaltyForm />
    </div>
  );
};

export default LoyaltyPage; 